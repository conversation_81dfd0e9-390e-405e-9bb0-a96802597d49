import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../services/customer_service.dart';
import '../models/create_customer_request.dart';
import 'customer_form_event.dart';
import 'customer_form_state.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/customer_tag_model.dart';

/// Bloc để quản lý customer form
class CustomerFormBloc extends Bloc<CustomerFormEvent, CustomerFormState> {
  final CustomerService _customerService;

  CustomerFormBloc({
    required CustomerService customerService,
  })  : _customerService = customerService,
        super(const CustomerFormInitial()) {
    on<CustomerFormInitialized>(_onInitialized);
    on<CustomerFormDataUpdated>(_onDataUpdated);
    on<CustomerFormFieldUpdated>(_onFieldUpdated);
    on<CustomerFormValidated>(_onValidated);
    on<CustomerFormStepValidated>(_onStepValidated);
    on<CustomerFormNextStep>(_onNextStep);
    on<CustomerFormPreviousStep>(_onPreviousStep);
    on<CustomerFormGoToStep>(_onGoToStep);
    on<CustomerFormSubmitted>(_onSubmitted);
    on<CustomerFormReset>(_onReset);
  }

  /// Khởi tạo form
  Future<void> _onInitialized(
    CustomerFormInitialized event,
    Emitter<CustomerFormState> emit,
  ) async {
    emit(const CustomerFormLoading());

    try {
      // Khởi tạo form với data ban đầu hoặc dữ liệu từ QR
      final initialData = _getInitialFormData();
      
      // Merge với initialData từ event nếu có
      if (event.initialData != null) {
        debugPrint('CustomerFormBloc: Received initialData with ${event.initialData!.length} entries:');
        for (final entry in event.initialData!.entries) {
          debugPrint('  ${entry.key}: ${entry.value} (${entry.value?.runtimeType})');
        }
        initialData.addAll(event.initialData!);
        debugPrint('CustomerFormBloc: Merged form data:');
        for (final entry in initialData.entries) {
          debugPrint('  ${entry.key}: ${entry.value} (${entry.value?.runtimeType})');
        }
      } else {
        debugPrint('CustomerFormBloc: No initialData provided');
      }
      
      emit(CustomerFormReady(
        formData: initialData,
        currentStep: 0,
        validationErrors: const {},
      ));
    } catch (e) {
      emit(CustomerFormFailure(
        error: 'Không thể khởi tạo form: $e',
        errorType: CustomerExceptionType.unknown,
        formData: const {},
      ));
    }
  }

  /// Cập nhật form data
  Future<void> _onDataUpdated(
    CustomerFormDataUpdated event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      emit(currentState.copyWith(formData: event.formData));
    }
  }

  /// Validate form
  Future<void> _onValidated(
    CustomerFormValidated event,
    Emitter<CustomerFormState> emit,
  ) async {
    try {
      final validationErrors = _validateFormData(event.formData);

      if (validationErrors.isNotEmpty) {
        emit(CustomerFormValidationFailure(
          validationErrors: validationErrors,
          formData: event.formData,
        ));
      } else {
        if (state is CustomerFormReady) {
          final currentState = state as CustomerFormReady;
          emit(currentState.copyWith(
            formData: event.formData,
            validationErrors: const {},
          ));
        }
      }
    } catch (e) {
      emit(CustomerFormFailure(
        error: 'Lỗi validation: $e',
        errorType: CustomerExceptionType.validationError,
        formData: event.formData,
      ));
    }
  }

  /// Submit form
  Future<void> _onSubmitted(
    CustomerFormSubmitted event,
    Emitter<CustomerFormState> emit,
  ) async {
    emit(CustomerFormSubmitting(event.request.toJson()));

    try {
      final success = await _customerService.createCustomer(event.request);
      emit(CustomerFormSuccess(success));
    } on CustomerException catch (e) {
      if (e.type == CustomerExceptionType.validationError) {
        // Handle validation errors
        final validationErrors = <String, List<String>>{};
        final errorMessage = e.message;
        
        // Parse validation errors from service
        if (errorMessage.contains('Họ tên')) {
          validationErrors['fullName'] = ['Họ tên không được để trống'];
        }
        if (errorMessage.contains('Số điện thoại')) {
          validationErrors['phoneNumber'] = ['Số điện thoại không đúng định dạng'];
        }
        if (errorMessage.contains('Email')) {
          validationErrors['email'] = ['Email không đúng định dạng'];
        }
        if (errorMessage.contains('Địa chỉ thường trú')) {
          validationErrors['permanentAddress'] = ['Địa chỉ thường trú không được để trống'];
        }
        if (errorMessage.contains('Tỉnh/thành phố')) {
          validationErrors['province'] = ['Tỉnh/thành phố không được để trống'];
        }
        if (errorMessage.contains('Trạng thái khách hàng')) {
          validationErrors['status'] = ['Trạng thái khách hàng không được để trống'];
        }

        emit(CustomerFormValidationFailure(
          validationErrors: validationErrors,
          formData: event.request.toJson(),
        ));
      } else {
        emit(CustomerFormFailure(
          error: e.message,
          errorType: e.type,
          formData: event.request.toJson(),
        ));
      }
    } catch (e) {
      emit(CustomerFormFailure(
        error: 'Lỗi không xác định: $e',
        errorType: CustomerExceptionType.unknown,
        formData: event.request.toJson(),
      ));
    }
  }

  /// Reset form
  Future<void> _onReset(
    CustomerFormReset event,
    Emitter<CustomerFormState> emit,
  ) async {
    try {
      final initialData = _getInitialFormData();
      emit(CustomerFormReady(
        formData: initialData,
        currentStep: 0,
        validationErrors: const {},
      ));
    } catch (e) {
      emit(CustomerFormFailure(
        error: 'Không thể reset form: $e',
        errorType: CustomerExceptionType.unknown,
        formData: const {},
      ));
    }
  }

  /// Cập nhật field cụ thể
  Future<void> _onFieldUpdated(
    CustomerFormFieldUpdated event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      final updatedFormData = Map<String, dynamic>.from(currentState.formData);
      
      // Chuẩn hóa giá trị: empty string -> null
      final normalizedValue = _normalizeFieldValue(event.value);
      updatedFormData[event.field] = normalizedValue;
      
      emit(currentState.copyWith(formData: updatedFormData));
    }
  }

  /// Validate step hiện tại
  Future<void> _onStepValidated(
    CustomerFormStepValidated event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      final stepValidationErrors = _validateStep(event.step, event.formData);
      
      emit(currentState.copyWith(
        formData: event.formData,
        validationErrors: stepValidationErrors,
        isStepValid: stepValidationErrors.isEmpty,
      ));
    }
  }

  /// Chuyển step tiếp theo
  Future<void> _onNextStep(
    CustomerFormNextStep event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      
      // Validate step hiện tại
      final stepValidationErrors = _validateStep(event.currentStep, event.formData);
      
      if (stepValidationErrors.isNotEmpty) {
        // Step không hợp lệ, không chuyển
        emit(currentState.copyWith(
          formData: event.formData,
          validationErrors: stepValidationErrors,
          isStepValid: false,
        ));
        return;
      }
      
      // Step hợp lệ, chuyển step tiếp theo
      final nextStep = event.currentStep + 1;
      if (nextStep < currentState.totalSteps) {
        emit(currentState.copyWith(
          formData: event.formData,
          currentStep: nextStep,
          validationErrors: const {},
          isStepValid: true,
        ));
      } else {
        // Đã ở step cuối, submit form
        _submitForm(event.formData, emit);
      }
    }
  }

  /// Chuyển step trước đó
  Future<void> _onPreviousStep(
    CustomerFormPreviousStep event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      final previousStep = event.currentStep - 1;
      
      if (previousStep >= 0) {
        emit(currentState.copyWith(
          currentStep: previousStep,
          validationErrors: const {},
          isStepValid: true,
        ));
      }
    }
  }

  /// Chuyển đến step cụ thể
  Future<void> _onGoToStep(
    CustomerFormGoToStep event,
    Emitter<CustomerFormState> emit,
  ) async {
    if (state is CustomerFormReady) {
      final currentState = state as CustomerFormReady;
      
      if (event.step >= 0 && event.step < currentState.totalSteps) {
        emit(currentState.copyWith(
          currentStep: event.step,
          validationErrors: const {},
          isStepValid: true,
        ));
      }
    }
  }

  /// Submit form từ form data
  void _submitForm(Map<String, dynamic> formData, Emitter<CustomerFormState> emit) {
    try {
      final request = _createCustomerRequestFromFormData(formData);
      add(CustomerFormSubmitted(request));
    } catch (e) {
      emit(CustomerFormFailure(
        error: 'Lỗi tạo request: $e',
        errorType: CustomerExceptionType.validationError,
        formData: formData,
      ));
    }
  }

  /// Tạo CreateCustomerRequest từ form data
  CreateCustomerRequest _createCustomerRequestFromFormData(Map<String, dynamic> formData) {
    return CreateCustomerRequest(
      fullName: formData['fullName'],
      phoneNumber: formData['phoneNumber'],
      email: formData['email'],
      sex: _convertGenderToApiFormat(formData['gender']),
      dob: _convertBirthDateToApiFormat(formData['birthDate']),
      idNo: formData['idCardNumber'],
      idType: formData['idCardType'],
      permanentAddress: formData['permanentAddress'],
      currentAddress: formData['currentAddress'],
      provinceId: formData['province'],
      // district field removed from API request
      wardsId: formData['ward'],
      occupation: formData['occupation'],
      workplace: formData['workplace'],
      monthlyIncome: formData['monthlyIncome'],
      workExperience: _convertWorkExperienceToApiFormat(formData['workExperience']),
      status: _convertStatusToApiFormat(formData['status']),
      source: _convertSourceToApiFormat(formData['source']),
      tagIds: _convertTagsToIds(formData['tags']),
      noteContent: formData['notes'],
      // revenue field removed from API request
    );
  }

  /// Validate step cụ thể
  Map<String, List<String>> _validateStep(int step, Map<String, dynamic> formData) {
    final errors = <String, List<String>>{};

    switch (step) {
      case 0: // Basic info step
        if (formData['fullName'] == null || formData['fullName'].toString().trim().isEmpty) {
          errors['fullName'] = ['Họ tên không được để trống'];
        } else if (formData['fullName'].toString().length < 2) {
          errors['fullName'] = ['Họ tên phải có ít nhất 2 ký tự'];
        }

        if (formData['phoneNumber'] != null && formData['phoneNumber'].toString().isNotEmpty) {
          final phone = formData['phoneNumber'].toString();
          if (!_isValidVietnamesePhone(phone)) {
            errors['phoneNumber'] = ['Số điện thoại không đúng định dạng'];
          }
        }

        if (formData['email'] != null && formData['email'].toString().isNotEmpty) {
          final email = formData['email'].toString();
          if (!_isValidEmail(email)) {
            errors['email'] = ['Email không đúng định dạng'];
          }
        }
        break;

      case 1: // Address info step
        // Tất cả trường trong Step 2 đều không bắt buộc theo yêu cầu
        // Không cần validation gì cả
        break;

      case 2: // Career info step
        if (formData['occupation'] == null || formData['occupation'].toString().trim().isEmpty) {
          errors['occupation'] = ['Nghề nghiệp không được để trống'];
        }
        break;

      case 3: // Classification step
        if (formData['status'] == null || formData['status'].toString().trim().isEmpty) {
          errors['status'] = ['Trạng thái khách hàng không được để trống'];
        }
        
        if (formData['source'] == null || formData['source'].toString().trim().isEmpty) {
          errors['source'] = ['Nguồn khách hàng không được để trống'];
        }
        break;
    }

    return errors;
  }

  /// Tạo initial form data với nullable fields
  Map<String, dynamic> _getInitialFormData() {
    return {
      // Basic info - nullable
      'fullName': null,
      'phoneNumber': null,
      'email': null,
      'idCardNumber': null,
      'gender': null,
      'birthDate': null,
      
      // Address info - nullable
      'permanentAddress': null,
      'province': null,
      'district': null,
      'ward': null,
      'currentAddress': null,
      'sameAddress': false,
      
      // Career info - nullable
      'occupation': null,
      'workplace': null,
      'monthlyIncome': null,
      'workExperience': null,
      
      // Classification - nullable
      'status': null,
      'source': null,
      'tags': null,
      'notes': null,
      'revenue': null,
    };
  }

  /// Validate form data
  Map<String, List<String>> _validateFormData(Map<String, dynamic> formData) {
    final errors = <String, List<String>>{};

    // Validate basic info
    if (formData['fullName'] == null || formData['fullName'].toString().trim().isEmpty) {
      errors['fullName'] = ['Họ tên không được để trống'];
    } else if (formData['fullName'].toString().length < 2) {
      errors['fullName'] = ['Họ tên phải có ít nhất 2 ký tự'];
    }

    if (formData['phoneNumber'] != null && formData['phoneNumber'].toString().isNotEmpty) {
      final phone = formData['phoneNumber'].toString();
      if (!_isValidVietnamesePhone(phone)) {
        errors['phoneNumber'] = ['Số điện thoại không đúng định dạng'];
      }
    }

    if (formData['email'] != null && formData['email'].toString().isNotEmpty) {
      final email = formData['email'].toString();
      if (!_isValidEmail(email)) {
        errors['email'] = ['Email không đúng định dạng'];
      }
    }

    // Validate address info - Tất cả trường đều không bắt buộc theo yêu cầu
    // Không cần validation gì cả

    // Validate classification
    if (formData['status'] == null || formData['status'].toString().trim().isEmpty) {
      errors['status'] = ['Trạng thái khách hàng không được để trống'];
    }

    if (formData['source'] == null || formData['source'].toString().trim().isEmpty) {
      errors['source'] = ['Nguồn khách hàng không được để trống'];
    }

    return errors;
  }

  /// Validate Vietnamese phone number
  bool _isValidVietnamesePhone(String phone) {
    final digits = phone.replaceAll(RegExp(r'[^\d]'), '');

    if (digits.startsWith('84') && digits.length == 11) {
      return true;
    } else if (digits.startsWith('0') && digits.length == 11) {
      return true;
    } else if (digits.startsWith('0') && digits.length == 10) {
      return true;
    } else if (digits.length == 9) {
      return true;
    } else if (digits.length == 10 && !digits.startsWith('0')) {
      return true;
    }

    return false;
  }

  /// Validate email format
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    return emailRegex.hasMatch(email);
  }

  /// Convert gender to API format - đã được lưu dưới dạng code
  String? _convertGenderToApiFormat(dynamic gender) {
    if (gender == null) return null;
    
    // Nếu là ConfigModel, lấy code
    if (gender is ConfigModel) {
      return gender.code;
    }
    
    // Form data đã lưu code trực tiếp, return as is
    return gender.toString();
  }

  /// Convert birth date to API format (YYYY-MM-DD)
  String? _convertBirthDateToApiFormat(dynamic birthDate) {
    if (birthDate == null) return null;
    if (birthDate is DateTime) {
      return birthDate.toIso8601String().split('T')[0];
    }
    if (birthDate is String) {
      // Try to parse and format
      try {
        final date = DateTime.parse(birthDate);
        return date.toIso8601String().split('T')[0];
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // TODO: Implement if needed
  // /// Convert monthly income to integer
  // int? _convertMonthlyIncomeToInt(dynamic monthlyIncome) {
  //   if (monthlyIncome == null) return null;
  //   if (monthlyIncome is int) return monthlyIncome;
  //   if (monthlyIncome is String) {
  //     // Remove non-digits and convert to int
  //     final cleanValue = monthlyIncome.replaceAll(RegExp(r'[^\d]'), '');
  //     if (cleanValue.isNotEmpty) {
  //       return int.tryParse(cleanValue);
  //     }
  //   }
  //   return null;
  // }

  /// Convert work experience to API format - đã được lưu dưới dạng code
  String? _convertWorkExperienceToApiFormat(dynamic workExperience) {
    if (workExperience == null) return null;
    
    // Nếu là ConfigModel, lấy code
    if (workExperience is ConfigModel) {
      return workExperience.code;
    }
    
    // Form data đã lưu code trực tiếp, return as is
    return workExperience.toString();
  }

  /// Convert status to API format - đã được lưu dưới dạng code
  String? _convertStatusToApiFormat(dynamic status) {
    if (status == null) return null;
    
    // Nếu là ConfigModel, lấy code
    if (status is ConfigModel) {
      return status.code;
    }
    
    // Form data đã lưu code trực tiếp, return as is
    return status.toString();
  }

  /// Convert source to API format - đã được lưu dưới dạng code
  String? _convertSourceToApiFormat(dynamic source) {
    if (source == null) return null;
    
    // Nếu là ConfigModel, lấy code
    if (source is ConfigModel) {
      return source.code;
    }
    
    // Form data đã lưu code trực tiếp, return as is
    return source.toString();
  }

  /// Convert tags to IDs - đã được lưu dưới dạng id
  List<String> _convertTagsToIds(List<dynamic>? tags) {
    if (tags == null) return [];
    
    return tags.map<String>((tag) {
      if (tag is String) {
        // Form data đã lưu tag id trực tiếp
        return tag;
      } else if (tag is CustomerTagModel) {
        // Nếu là CustomerTagModel, lấy id
        return tag.id ?? tag.name ?? tag.toString();
      } else if (tag is Map<String, dynamic>) {
        // Nếu là object, lấy id hoặc name
        return tag['id'] ?? tag['name'] ?? tag.toString();
      }
      return tag.toString();
    }).toList();
  }

  /// Chuẩn hóa giá trị field: empty string -> null
  dynamic _normalizeFieldValue(dynamic value) {
    if (value == null) return null;
    
    // Nếu là string, kiểm tra có rỗng không
    if (value is String) {
      final trimmed = value.trim();
      return trimmed.isEmpty ? null : trimmed;
    }
    
    // Nếu là list, kiểm tra có rỗng không
    if (value is List) {
      return value.isEmpty ? null : value;
    }
    
    // Các kiểu dữ liệu khác giữ nguyên
    return value;
  }
} 