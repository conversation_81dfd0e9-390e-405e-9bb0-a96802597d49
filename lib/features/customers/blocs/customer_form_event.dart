import 'package:equatable/equatable.dart';
import '../models/create_customer_request.dart';

/// Events cho CustomerFormBloc
abstract class CustomerFormEvent extends Equatable {
  const CustomerFormEvent();

  @override
  List<Object?> get props => [];
}

/// Event khởi tạo form
class CustomerFormInitialized extends CustomerFormEvent {
  final Map<String, dynamic>? initialData;
  
  const CustomerFormInitialized({this.initialData});
  
  @override
  List<Object?> get props => [initialData];
}

/// Event cập nhật form data
class CustomerFormDataUpdated extends CustomerFormEvent {
  final Map<String, dynamic> formData;

  const CustomerFormDataUpdated(this.formData);

  @override
  List<Object?> get props => [formData];
}

/// Event cập nhật field cụ thể
class CustomerFormFieldUpdated extends CustomerFormEvent {
  final String field;
  final dynamic value;

  const CustomerFormFieldUpdated(this.field, this.value);

  @override
  List<Object?> get props => [field, value];
}

/// Event validate form
class CustomerFormValidated extends CustomerFormEvent {
  final Map<String, dynamic> formData;

  const CustomerFormValidated(this.formData);

  @override
  List<Object?> get props => [formData];
}

/// Event validate step hiện tại
class CustomerFormStepValidated extends CustomerFormEvent {
  final int step;
  final Map<String, dynamic> formData;

  const CustomerFormStepValidated(this.step, this.formData);

  @override
  List<Object?> get props => [step, formData];
}

/// Event chuyển step tiếp theo
class CustomerFormNextStep extends CustomerFormEvent {
  final int currentStep;
  final Map<String, dynamic> formData;

  const CustomerFormNextStep(this.currentStep, this.formData);

  @override
  List<Object?> get props => [currentStep, formData];
}

/// Event chuyển step trước đó
class CustomerFormPreviousStep extends CustomerFormEvent {
  final int currentStep;

  const CustomerFormPreviousStep(this.currentStep);

  @override
  List<Object?> get props => [currentStep];
}

/// Event chuyển đến step cụ thể
class CustomerFormGoToStep extends CustomerFormEvent {
  final int step;

  const CustomerFormGoToStep(this.step);

  @override
  List<Object?> get props => [step];
}

/// Event submit form
class CustomerFormSubmitted extends CustomerFormEvent {
  final CreateCustomerRequest request;

  const CustomerFormSubmitted(this.request);

  @override
  List<Object?> get props => [request];
}

/// Event reset form
class CustomerFormReset extends CustomerFormEvent {
  const CustomerFormReset();
} 