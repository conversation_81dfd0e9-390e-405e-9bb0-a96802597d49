import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../../core/theme/index.dart';
import '../../../auth/blocs/master_data_bloc.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/constants/config_types.dart';

class BasicInfoStep extends StatefulWidget {
  final GlobalKey<FormState> formKey;
  final Map<String, dynamic> customerData;

  const BasicInfoStep({
    super.key,
    required this.formKey,
    required this.customerData,
  });

  @override
  State<BasicInfoStep> createState() => _BasicInfoStepState();
}

class _BasicInfoStepState extends State<BasicInfoStep> {
  late TextEditingController _nameController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _idNumberController;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.customerData['fullName'] ?? '');
    _phoneController = TextEditingController(text: widget.customerData['phoneNumber'] ?? '');
    _emailController = TextEditingController(text: widget.customerData['email'] ?? '');
    _idNumberController = TextEditingController(text: widget.customerData['idCardNumber'] ?? '');

    // Listen to changes
    _nameController.addListener(() {
      widget.customerData['fullName'] = _nameController.text;
    });
    _phoneController.addListener(() {
      widget.customerData['phoneNumber'] = _phoneController.text;
    });
    _emailController.addListener(() {
      widget.customerData['email'] = _emailController.text;
    });
    _idNumberController.addListener(() {
      widget.customerData['idCardNumber'] = _idNumberController.text;
    });

    // Load gender options when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<MasterDataBloc>().add(LoadConfigEvent(ConfigTypes.SEX));
    });
  }

  @override
  void didUpdateWidget(BasicInfoStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Cập nhật controllers khi customerData thay đổi (ví dụ từ QR scan)
    if (oldWidget.customerData != widget.customerData) {
      _nameController.text = widget.customerData['fullName'] ?? '';
      _phoneController.text = widget.customerData['phoneNumber'] ?? '';
      _emailController.text = widget.customerData['email'] ?? '';
      _idNumberController.text = widget.customerData['idCardNumber'] ?? '';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _idNumberController.dispose();
    super.dispose();
  }

  void _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: widget.customerData['birthDate'] ?? DateTime(1990),
      firstDate: DateTime(1940),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.kienlongOrange,
              onPrimary: Colors.white,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        widget.customerData['birthDate'] = picked;
      });
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return '';
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: widget.formKey,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Introduction
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
              decoration: BoxDecoration(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(
                  color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    TablerIcons.info_circle,
                    color: AppColors.kienlongSkyBlue,
                    size: AppDimensions.iconM,
                  ),
                  SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      'Nhập thông tin cơ bản của khách hàng. Các trường có dấu (*) là bắt buộc.',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.kienlongSkyBlue,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Name Field
            Text(
              'Họ và tên *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                hintText: 'Nhập họ và tên đầy đủ',
                prefixIcon: Icon(
                  TablerIcons.user,
                  color: AppColors.textSecondary,
                ),
              ),
              textCapitalization: TextCapitalization.words,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Vui lòng nhập họ và tên';
                }
                if (value.trim().length < 2) {
                  return 'Họ tên phải có ít nhất 2 ký tự';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Phone Field
            Text(
              'Số điện thoại *',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                hintText: 'Nhập số điện thoại',
                prefixIcon: Icon(
                  TablerIcons.phone,
                  color: AppColors.textSecondary,
                ),
              ),
              keyboardType: TextInputType.phone,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(11),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Vui lòng nhập số điện thoại';
                }
                if (!RegExp(r'^(0[3-9])\d{8}$').hasMatch(value)) {
                  return 'Số điện thoại không hợp lệ';
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Email Field
            Text(
              'Email',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _emailController,
              decoration: InputDecoration(
                hintText: 'Nhập địa chỉ email (không bắt buộc)',
                prefixIcon: Icon(
                  TablerIcons.mail,
                  color: AppColors.textSecondary,
                ),
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                    return 'Địa chỉ email không hợp lệ';
                  }
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // ID Number Field
            Text(
              'Số GTTT',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            TextFormField(
              controller: _idNumberController,
              decoration: InputDecoration(
                hintText: 'Nhập số giấy tờ tùy thân (không bắt buộc)',
                prefixIcon: Icon(
                  TablerIcons.id,
                  color: AppColors.textSecondary,
                ),
              ),
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(12),
              ],
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  if (value.length != 12) {
                    return 'Số GTTT phải có 12 số';
                  }
                }
                return null;
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Gender Field
            Text(
              'Giới tính',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            BlocBuilder<MasterDataBloc, MasterDataState>(
              builder: (context, state) {
                List<ConfigModel> genderOptions = [];
                
                // Check new composite state first
                if (state is MasterDataLoaded) {
                  final configs = state.configsByGroup[ConfigTypes.SEX];
                  if (configs != null) {
                    genderOptions = configs;
                  }
                } 
                // Fallback to legacy state for backward compatibility
                else if (state is ConfigLoaded && state.groupCode == ConfigTypes.SEX) {
                  genderOptions = state.configs;
                } else if (state is MasterDataError && state.type == 'config') {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      DropdownButtonFormField<String>(
                        value: null,
                        decoration: InputDecoration(
                          hintText: 'Lỗi tải dữ liệu',
                          prefixIcon: Icon(
                            TablerIcons.gender_male,
                            color: AppColors.textSecondary,
                          ),
                        ),
                        items: [],
                        onChanged: null,
                      ),
                      SizedBox(height: AppDimensions.spacingS),
                      Text(
                        'Không thể tải danh sách giới tính',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    ],
                  );
                }

                return DropdownButtonFormField<String>(
                  value: (widget.customerData['gender']?.toString().isEmpty ?? true) ? null : widget.customerData['gender'],
                  decoration: InputDecoration(
                    hintText: (state is MasterDataLoading && state.type == 'config') ? 'Đang tải...' : 'Chọn giới tính',
                    prefixIcon: Icon(
                      TablerIcons.gender_male,
                      color: AppColors.textSecondary,
                    ),
                  ),
                  items: genderOptions.map((config) {
                    return DropdownMenuItem<String>(
                      value: config.code, // Lưu code thay vì value
                      child: Text(config.label ?? ''),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() {
                      widget.customerData['gender'] = value ?? '';
                    });
                  },
                );
              },
            ),

            SizedBox(height: AppDimensions.spacingL),

            // Birth Date Field
            Text(
              'Ngày sinh',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            InkWell(
              onTap: _selectBirthDate,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingM,
                  vertical: AppDimensions.paddingM + 2,
                ),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: AppColors.borderLight,
                  ),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: Row(
                  children: [
                    Icon(
                      TablerIcons.calendar,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Expanded(
                      child: Text(
                        widget.customerData['birthDate'] != null
                            ? _formatDate(widget.customerData['birthDate'])
                            : 'Chọn ngày sinh',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: widget.customerData['birthDate'] != null
                              ? AppColors.textPrimary
                              : AppColors.textSecondary,
                        ),
                      ),
                    ),
                    Icon(
                      TablerIcons.chevron_down,
                      color: AppColors.textSecondary,
                      size: AppDimensions.iconS,
                    ),
                  ],
                ),
              ),
            ),

            SizedBox(height: AppDimensions.spacingXL),
          ],
        ),
      ),
    );
  }
} 