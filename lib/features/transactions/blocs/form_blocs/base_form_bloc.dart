import 'dart:async';
import 'package:rxdart/rxdart.dart';
import '../../../../shared/utils/app_logger.dart';
import '../../../customers/models/customer_model.dart';
import '../../../../shared/models/province_model.dart';
import '../../../../shared/models/ward_model.dart';
import '../../../../shared/models/config_model.dart';

/// Base Form Bloc với RxDart - chứa tất cả shared logic
abstract class BaseFormBloc {
  
  // ===== SHARED BORROWER FIELDS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  final _borrowerIdIssueDateSubject = BehaviorSubject<String?>();
  final _borrowerIdExpiryDateSubject = BehaviorSubject<String?>();
  final _borrowerBirthDateSubject = BehaviorSubject<String?>();
  final _borrowerGenderModelSubject = BehaviorSubject<ConfigModel?>();
  final _borrowerMaritalStatusModelSubject = BehaviorSubject<ConfigModel?>();
  final _borrowerPermanentProvinceModelSubject = BehaviorSubject<ProvinceModel?>();
  final _borrowerPermanentWardModelSubject = BehaviorSubject<WardModel?>();
  final _borrowerCurrentProvinceModelSubject = BehaviorSubject<ProvinceModel?>();
  final _borrowerCurrentWardModelSubject = BehaviorSubject<WardModel?>();
  final _borrowerCurrentSamePermanentSubject = BehaviorSubject<bool>();

  // ===== SHARED CO-BORROWER FIELDS =====
  // Keep streams for dropdowns and switches - text fields use TextController
  final _hasCoBorrowerSubject = BehaviorSubject<bool>();
  final _coBorrowerIdTypeModelSubject = BehaviorSubject<ConfigModel?>();
  final _coBorrowerIdIssueDateSubject = BehaviorSubject<String?>();
  final _coBorrowerIdExpiryDateSubject = BehaviorSubject<String?>();
  final _coBorrowerBirthDateSubject = BehaviorSubject<String?>();
  final _coBorrowerGenderModelSubject = BehaviorSubject<ConfigModel?>();
  final _coBorrowerMaritalStatusModelSubject = BehaviorSubject<ConfigModel?>();
  final _coBorrowerPermanentProvinceModelSubject = BehaviorSubject<ProvinceModel?>();
  final _coBorrowerPermanentWardModelSubject = BehaviorSubject<WardModel?>();
  final _coBorrowerCurrentProvinceModelSubject = BehaviorSubject<ProvinceModel?>();
  final _coBorrowerCurrentWardModelSubject = BehaviorSubject<WardModel?>();
  final _coBorrowerCurrentSamePermanentSubject = BehaviorSubject<bool>();

  // ===== BUSINESS LOCATION FIELDS =====
  // Keep streams for dropdowns - text fields use TextController
  final _businessLocationProvinceModelSubject = BehaviorSubject<ProvinceModel?>();
  final _businessLocationWardModelSubject = BehaviorSubject<WardModel?>();

  // ===== BRANCH INFO =====
  // Branch code is now handled directly in form data, no need for stream
  
  // ===== VALIDATION SUBJECTS =====
  final _isValidSubject = BehaviorSubject<bool>();
  final _validationErrorsSubject = BehaviorSubject<List<String>>();
  final _isLoadingSubject = BehaviorSubject<bool>();

  // ===== STREAMS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  Stream<String?> get borrowerIdIssueDateStream => _borrowerIdIssueDateSubject.stream;
  Stream<String?> get borrowerIdExpiryDateStream => _borrowerIdExpiryDateSubject.stream;
  Stream<String?> get borrowerBirthDateStream => _borrowerBirthDateSubject.stream;
  Stream<ConfigModel?> get borrowerGenderModelStream => _borrowerGenderModelSubject.stream;
  Stream<ConfigModel?> get borrowerMaritalStatusModelStream => _borrowerMaritalStatusModelSubject.stream;
  Stream<ProvinceModel?> get borrowerPermanentProvinceModelStream => _borrowerPermanentProvinceModelSubject.stream;
  Stream<WardModel?> get borrowerPermanentWardModelStream => _borrowerPermanentWardModelSubject.stream;
  Stream<ProvinceModel?> get borrowerCurrentProvinceModelStream => _borrowerCurrentProvinceModelSubject.stream;
  Stream<WardModel?> get borrowerCurrentWardModelStream => _borrowerCurrentWardModelSubject.stream;
  Stream<bool> get borrowerCurrentSamePermanentStream => _borrowerCurrentSamePermanentSubject.stream;
  
  Stream<bool> get hasCoBorrowerStream => _hasCoBorrowerSubject.stream;
  Stream<ConfigModel?> get coBorrowerIdTypeModelStream => _coBorrowerIdTypeModelSubject.stream;
  Stream<String?> get coBorrowerIdIssueDateStream => _coBorrowerIdIssueDateSubject.stream;
  Stream<String?> get coBorrowerIdExpiryDateStream => _coBorrowerIdExpiryDateSubject.stream;
  Stream<String?> get coBorrowerBirthDateStream => _coBorrowerBirthDateSubject.stream;
  Stream<ConfigModel?> get coBorrowerGenderModelStream => _coBorrowerGenderModelSubject.stream;
  Stream<ConfigModel?> get coBorrowerMaritalStatusModelStream => _coBorrowerMaritalStatusModelSubject.stream;
  Stream<ProvinceModel?> get coBorrowerPermanentProvinceModelStream => _coBorrowerPermanentProvinceModelSubject.stream;
  Stream<WardModel?> get coBorrowerPermanentWardModelStream => _coBorrowerPermanentWardModelSubject.stream;
  Stream<ProvinceModel?> get coBorrowerCurrentProvinceModelStream => _coBorrowerCurrentProvinceModelSubject.stream;
  Stream<WardModel?> get coBorrowerCurrentWardModelStream => _coBorrowerCurrentWardModelSubject.stream;
  Stream<bool> get coBorrowerCurrentSamePermanentStream => _coBorrowerCurrentSamePermanentSubject.stream;
  
  Stream<ProvinceModel?> get businessLocationProvinceModelStream => _businessLocationProvinceModelSubject.stream;
  Stream<WardModel?> get businessLocationWardModelStream => _businessLocationWardModelSubject.stream;
  
  Stream<bool> get isValidStream => _isValidSubject.stream;
  Stream<List<String>> get validationErrorsStream => _validationErrorsSubject.stream;
  Stream<bool> get isLoadingStream => _isLoadingSubject.stream;

  // ===== VALUES =====
  // Keep values for dropdowns, switches, and date fields - text fields use TextController
  String? get borrowerIdIssueDate => _borrowerIdIssueDateSubject.valueOrNull;
  String? get borrowerIdExpiryDate => _borrowerIdExpiryDateSubject.valueOrNull;
  String? get borrowerBirthDate => _borrowerBirthDateSubject.valueOrNull;
  ConfigModel? get borrowerGenderModel => _borrowerGenderModelSubject.valueOrNull;
  ConfigModel? get borrowerMaritalStatusModel => _borrowerMaritalStatusModelSubject.valueOrNull;
  ProvinceModel? get borrowerPermanentProvinceModel => _borrowerPermanentProvinceModelSubject.valueOrNull;
  WardModel? get borrowerPermanentWardModel => _borrowerPermanentWardModelSubject.valueOrNull;
  ProvinceModel? get borrowerCurrentProvinceModel => _borrowerCurrentProvinceModelSubject.valueOrNull;
  WardModel? get borrowerCurrentWardModel => _borrowerCurrentWardModelSubject.valueOrNull;
  bool get borrowerCurrentSamePermanent => _borrowerCurrentSamePermanentSubject.valueOrNull ?? false;
  
  bool get hasCoBorrower => _hasCoBorrowerSubject.valueOrNull ?? true;
  ConfigModel? get coBorrowerIdTypeModel => _coBorrowerIdTypeModelSubject.valueOrNull;
  String? get coBorrowerIdIssueDate => _coBorrowerIdIssueDateSubject.valueOrNull;
  String? get coBorrowerIdExpiryDate => _coBorrowerIdExpiryDateSubject.valueOrNull;
  String? get coBorrowerBirthDate => _coBorrowerBirthDateSubject.valueOrNull;
  ConfigModel? get coBorrowerGenderModel => _coBorrowerGenderModelSubject.valueOrNull;
  ConfigModel? get coBorrowerMaritalStatusModel => _coBorrowerMaritalStatusModelSubject.valueOrNull;
  ProvinceModel? get coBorrowerPermanentProvinceModel => _coBorrowerPermanentProvinceModelSubject.valueOrNull;
  WardModel? get coBorrowerPermanentWardModel => _coBorrowerPermanentWardModelSubject.valueOrNull;
  ProvinceModel? get coBorrowerCurrentProvinceModel => _coBorrowerCurrentProvinceModelSubject.valueOrNull;
  WardModel? get coBorrowerCurrentWardModel => _coBorrowerCurrentWardModelSubject.valueOrNull;
  bool get coBorrowerCurrentSamePermanent => _coBorrowerCurrentSamePermanentSubject.valueOrNull ?? true;
  
  ProvinceModel? get businessLocationProvinceModel => _businessLocationProvinceModelSubject.valueOrNull;
  WardModel? get businessLocationWardModel => _businessLocationWardModelSubject.valueOrNull;
  
  bool get isValid => _isValidSubject.valueOrNull ?? false;
  List<String> get validationErrors => _validationErrorsSubject.valueOrNull ?? <String>[];
  bool get isLoading => _isLoadingSubject.valueOrNull ?? false;

  // ===== SHARED BORROWER METHODS =====
  
  /// Update borrower ID issue date
  void updateBorrowerIdIssueDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower ID issue date to: $date');
    updateFieldAndValidate(_borrowerIdIssueDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Borrower ID issue date updated');
  }

  /// Update borrower ID expiry date
  void updateBorrowerIdExpiryDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower ID expiry date to: $date');
    updateFieldAndValidate(_borrowerIdExpiryDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Borrower ID expiry date updated');
  }

  /// Update borrower birth date
  void updateBorrowerBirthDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower birth date to: $date');
    updateFieldAndValidate(_borrowerBirthDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Borrower birth date updated');
  }

  /// Update borrower gender
  void updateBorrowerGender(ConfigModel genderModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower gender to: ${genderModel.id}');
    _borrowerGenderModelSubject.add(genderModel);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Borrower gender updated');
  }

  /// Update borrower marital status
  void updateBorrowerMaritalStatus(ConfigModel maritalStatusModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower marital status to: ${maritalStatusModel.id}');
    _borrowerMaritalStatusModelSubject.add(maritalStatusModel);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Borrower marital status updated');
  }

  /// Update borrower permanent province
  void updateBorrowerPermanentProvince(ProvinceModel provinceModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower permanent province to: ${provinceModel.name}');
    _borrowerPermanentProvinceModelSubject.add(provinceModel);
    // Clear ward khi đổi tỉnh
    _borrowerPermanentWardModelSubject.add(null);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Borrower permanent province updated');
  }

  /// Update borrower permanent ward
  void updateBorrowerPermanentWard(WardModel wardModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower permanent ward to: ${wardModel.name}');
    updateFieldAndValidate(_borrowerPermanentWardModelSubject, wardModel);
    appLogger.d('✅ BaseFormBloc: Borrower permanent ward updated');
  }


  /// Update borrower current province
  void updateBorrowerCurrentProvince(ProvinceModel provinceModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower current province to: ${provinceModel.name}');
    _borrowerCurrentProvinceModelSubject.add(provinceModel);
    // Clear ward khi đổi tỉnh
    _borrowerCurrentWardModelSubject.add(null);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Borrower current province updated');
  }

  /// Update borrower current ward
  void updateBorrowerCurrentWard(WardModel wardModel) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower current ward to: ${wardModel.name}');
    updateFieldAndValidate(_borrowerCurrentWardModelSubject, wardModel);
    appLogger.d('✅ BaseFormBloc: Borrower current ward updated');
  }

  /// Update borrower current same permanent
  void updateBorrowerCurrentSamePermanent(bool isSame) {
    appLogger.d('🔄 BaseFormBloc: Updating borrower current same permanent to: $isSame');
    updateFieldAndValidate(_borrowerCurrentSamePermanentSubject, isSame);
    appLogger.d('✅ BaseFormBloc: Borrower current same permanent updated');
  }

  // ===== SHARED CO-BORROWER METHODS =====
  
  /// Update has co-borrower
  void updateHasCoBorrower(bool hasCoBorrower) {
    appLogger.d('🔄 BaseFormBloc: Updating has co-borrower to: $hasCoBorrower');
    updateFieldAndValidate(_hasCoBorrowerSubject, hasCoBorrower);
    appLogger.d('✅ BaseFormBloc: Has co-borrower updated');
  }

  /// Update co-borrower ID type
  void updateCoBorrowerIdType(ConfigModel idTypeModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower ID type to: ${idTypeModel.id}');
    _coBorrowerIdTypeModelSubject.add(idTypeModel);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Co-borrower ID type updated');
  }

  /// Update co-borrower ID issue date
  void updateCoBorrowerIdIssueDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower ID issue date to: $date');
    updateFieldAndValidate(_coBorrowerIdIssueDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Co-borrower ID issue date updated');
  }

  /// Update co-borrower ID expiry date
  void updateCoBorrowerIdExpiryDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower ID expiry date to: $date');
    updateFieldAndValidate(_coBorrowerIdExpiryDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Co-borrower ID expiry date updated');
  }

  /// Update co-borrower birth date
  void updateCoBorrowerBirthDate(String date) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower birth date to: $date');
    updateFieldAndValidate(_coBorrowerBirthDateSubject, date);
    appLogger.d('✅ BaseFormBloc: Co-borrower birth date updated');
  }

  /// Update co-borrower gender
  void updateCoBorrowerGender(ConfigModel genderModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower gender to: ${genderModel.id}');
    _coBorrowerGenderModelSubject.add(genderModel);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Co-borrower gender updated');
  }

  /// Update co-borrower marital status
  void updateCoBorrowerMaritalStatus(ConfigModel maritalStatusModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower marital status to: ${maritalStatusModel.id}');
    _coBorrowerMaritalStatusModelSubject.add(maritalStatusModel);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Co-borrower marital status updated');
  }

  /// Update co-borrower permanent province
  void updateCoBorrowerPermanentProvince(ProvinceModel provinceModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower permanent province to: ${provinceModel.name}');
    _coBorrowerPermanentProvinceModelSubject.add(provinceModel);
    // Clear ward khi đổi tỉnh
    _coBorrowerPermanentWardModelSubject.add(null);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Co-borrower permanent province updated');
  }

  /// Update co-borrower permanent ward
  void updateCoBorrowerPermanentWard(WardModel wardModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower permanent ward to: ${wardModel.name}');
    updateFieldAndValidate(_coBorrowerPermanentWardModelSubject, wardModel);
    appLogger.d('✅ BaseFormBloc: Co-borrower permanent ward updated');
  }


  /// Update co-borrower current province
  void updateCoBorrowerCurrentProvince(ProvinceModel provinceModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower current province to: ${provinceModel.name}');
    _coBorrowerCurrentProvinceModelSubject.add(provinceModel);
    // Clear ward khi đổi tỉnh
    _coBorrowerCurrentWardModelSubject.add(null);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Co-borrower current province updated');
  }

  /// Update co-borrower current ward
  void updateCoBorrowerCurrentWard(WardModel wardModel) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower current ward to: ${wardModel.name}');
    updateFieldAndValidate(_coBorrowerCurrentWardModelSubject, wardModel);
    appLogger.d('✅ BaseFormBloc: Co-borrower current ward updated');
  }


  /// Update co-borrower current same permanent
  void updateCoBorrowerCurrentSamePermanent(bool isSame) {
    appLogger.d('🔄 BaseFormBloc: Updating co-borrower current same permanent to: $isSame');
    updateFieldAndValidate(_coBorrowerCurrentSamePermanentSubject, isSame);
    appLogger.d('✅ BaseFormBloc: Co-borrower current same permanent updated');
  }

  /// Update business location province
  void updateBusinessLocationProvince(ProvinceModel provinceModel) {
    appLogger.d('🔄 BaseFormBloc: Updating business location province to: ${provinceModel.name}');
    _businessLocationProvinceModelSubject.add(provinceModel);
    // Clear ward khi đổi tỉnh
    _businessLocationWardModelSubject.add(null);
    updateValidationState();
    appLogger.d('✅ BaseFormBloc: Business location province updated');
  }

  /// Update business location ward
  void updateBusinessLocationWard(WardModel wardModel) {
    appLogger.d('🔄 BaseFormBloc: Updating business location ward to: ${wardModel.name}');
    updateFieldAndValidate(_businessLocationWardModelSubject, wardModel);
    appLogger.d('✅ BaseFormBloc: Business location ward updated');
  }

  // Business location address and branch code now handled by TextController

  // ===== SHARED VALIDATION =====
  
  /// Validate shared borrower info
  List<String> validateBorrowerInfo() {
    final errors = <String>[];
    
    // Text field validation now handled by form data validation
    // Only validate dropdown and switch fields here
    
    if (borrowerPermanentProvinceModel == null) {
      errors.add('Vui lòng chọn tỉnh/thành phố thường trú');
    }
    
    if (borrowerPermanentWardModel == null) {
      errors.add('Vui lòng chọn phường/xã thường trú');
    }
    
    return errors;
  }

  /// Validate shared co-borrower info
  List<String> validateCoBorrowerInfo() {
    final errors = <String>[];
    
    // Text field validation now handled by form data validation
    // Only validate dropdown and switch fields here
    
    return errors;
  }

  // ===== SHARED INITIALIZATION =====
  
  /// Initialize with customer data
  void initializeWithCustomer(CustomerModel? customer) {
    appLogger.d('🔄 BaseFormBloc: Initializing with customer');
    
    if (customer != null) {
      // Only initialize dropdown and switch fields
      _borrowerPermanentProvinceModelSubject.add(customer.province?.id.isNotEmpty == true ? customer.province : null);
      _borrowerPermanentWardModelSubject.add(customer.ward?.id?.isNotEmpty == true ? customer.ward : null);
      _borrowerCurrentSamePermanentSubject.add(customer.sameAddress);
      
      // Set defaults
      _hasCoBorrowerSubject.add(true);
      _borrowerCurrentSamePermanentSubject.add(true);
      
      appLogger.d('✅ BaseFormBloc: Initialized with customer: ${customer.fullName}');
    }
    
    updateValidationState();
  }

  // ===== ABSTRACT METHODS =====
  
  /// Validate form - to be implemented by subclasses
  List<String> validateForm();
  
  /// Sync to parent bloc - to be implemented by subclasses
  void syncToParentBloc();
  

  // ===== PROTECTED METHODS =====
  
  /// Update validation state only
  void updateValidationState() {
    final errors = <String>[];
    errors.addAll(validateBorrowerInfo());
    errors.addAll(validateCoBorrowerInfo());
    errors.addAll(validateForm());
    
    _validationErrorsSubject.add(errors);
    _isValidSubject.add(errors.isEmpty);
  }
  
  /// Update field and trigger validation
  void updateFieldAndValidate<T>(BehaviorSubject<T> subject, T value) {
    subject.add(value);
    updateValidationState();
  }
  
  /// Update field, validate and sync to parent
  void updateFieldValidateAndSync<T>(BehaviorSubject<T> subject, T value) {
    subject.add(value);
    updateValidationState();
    syncToParentBloc();
  }
  
  

  // ===== DISPOSE =====
  
  /// Dispose all subjects
  void dispose() {
    appLogger.d('🔄 BaseFormBloc: Disposing all subjects');
    
    // Dispose borrower subjects
    _borrowerIdIssueDateSubject.close();
    _borrowerIdExpiryDateSubject.close();
    _borrowerBirthDateSubject.close();
    _borrowerGenderModelSubject.close();
    _borrowerMaritalStatusModelSubject.close();
    _borrowerPermanentProvinceModelSubject.close();
    _borrowerPermanentWardModelSubject.close();
    _borrowerCurrentProvinceModelSubject.close();
    _borrowerCurrentWardModelSubject.close();
    _borrowerCurrentSamePermanentSubject.close();
    
    // Dispose co-borrower subjects
    _hasCoBorrowerSubject.close();
    _coBorrowerIdTypeModelSubject.close();
    _coBorrowerIdIssueDateSubject.close();
    _coBorrowerIdExpiryDateSubject.close();
    _coBorrowerBirthDateSubject.close();
    _coBorrowerGenderModelSubject.close();
    _coBorrowerMaritalStatusModelSubject.close();
    _coBorrowerPermanentProvinceModelSubject.close();
    _coBorrowerPermanentWardModelSubject.close();
    _coBorrowerCurrentProvinceModelSubject.close();
    _coBorrowerCurrentWardModelSubject.close();
    _coBorrowerCurrentSamePermanentSubject.close();
    
    // Dispose business location subjects
    _businessLocationProvinceModelSubject.close();
    _businessLocationWardModelSubject.close();
    
    // Dispose validation subjects
    _isValidSubject.close();
    _validationErrorsSubject.close();
    _isLoadingSubject.close();
    
    appLogger.d('✅ BaseFormBloc: All subjects disposed');
  }
}