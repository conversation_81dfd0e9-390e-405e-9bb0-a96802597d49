import 'dart:async';
import 'package:rxdart/rxdart.dart';
import '../../../../shared/utils/app_logger.dart';
import '../../../customers/models/customer_model.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/collateral_category_model.dart';
import 'base_form_bloc.dart';

/// InstallmentLoanFormBloc - extends BaseFormBloc với shared logic
class InstallmentLoanFormBloc extends BaseFormBloc {
  
  // ===== INSTALLMENT LOAN SPECIFIC FIELDS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  final _loanTermModelSubject = BehaviorSubject<ConfigModel?>();
  final _collateralTypeModelSubject = BehaviorSubject<CollateralCategoryModel?>();
  final _collateralConditionModelSubject = BehaviorSubject<ConfigModel?>();
  final _vehicleConditionAtHandoverModelSubject = BehaviorSubject<ConfigModel?>();
  final _vehicleRegistrationDateSubject = BehaviorSubject<String?>();
  final _collateralOwnerBirthYearSubject = BehaviorSubject<String?>();
  
  // Additional dropdown fields
  final _loanMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _loanPurposeModelSubject = BehaviorSubject<ConfigModel?>();
  final _repaymentMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _disbursementMethodModelSubject = BehaviorSubject<ConfigModel?>();
  final _incomeSourceModelSubject = BehaviorSubject<ConfigModel?>();

  // ===== STREAMS =====
  // Keep streams for dropdowns, switches, and date fields - text fields use TextController
  Stream<ConfigModel?> get loanTermModelStream => _loanTermModelSubject.stream;
  Stream<CollateralCategoryModel?> get collateralTypeModelStream => _collateralTypeModelSubject.stream;
  Stream<ConfigModel?> get collateralConditionModelStream => _collateralConditionModelSubject.stream;
  Stream<ConfigModel?> get vehicleConditionAtHandoverModelStream => _vehicleConditionAtHandoverModelSubject.stream;
  Stream<String?> get vehicleRegistrationDateStream => _vehicleRegistrationDateSubject.stream;
  Stream<String?> get collateralOwnerBirthYearStream => _collateralOwnerBirthYearSubject.stream;
  
  // Additional dropdown streams
  Stream<ConfigModel?> get loanMethodModelStream => _loanMethodModelSubject.stream;
  Stream<ConfigModel?> get loanPurposeModelStream => _loanPurposeModelSubject.stream;
  Stream<ConfigModel?> get repaymentMethodModelStream => _repaymentMethodModelSubject.stream;
  Stream<ConfigModel?> get disbursementMethodModelStream => _disbursementMethodModelSubject.stream;
  Stream<ConfigModel?> get incomeSourceModelStream => _incomeSourceModelSubject.stream;

  // ===== VALUES =====
  // Keep values for dropdowns, switches, and date fields - text fields use TextController
  ConfigModel? get loanTermModel => _loanTermModelSubject.valueOrNull;
  CollateralCategoryModel? get collateralTypeModel => _collateralTypeModelSubject.valueOrNull;
  ConfigModel? get collateralConditionModel => _collateralConditionModelSubject.valueOrNull;
  ConfigModel? get vehicleConditionAtHandoverModel => _vehicleConditionAtHandoverModelSubject.valueOrNull;
  String? get vehicleRegistrationDate => _vehicleRegistrationDateSubject.valueOrNull;
  String? get collateralOwnerBirthYear => _collateralOwnerBirthYearSubject.valueOrNull;
  
  // Additional dropdown values
  ConfigModel? get loanMethodModel => _loanMethodModelSubject.valueOrNull;
  ConfigModel? get loanPurposeModel => _loanPurposeModelSubject.valueOrNull;
  ConfigModel? get repaymentMethodModel => _repaymentMethodModelSubject.valueOrNull;
  ConfigModel? get disbursementMethodModel => _disbursementMethodModelSubject.valueOrNull;
  ConfigModel? get incomeSourceModel => _incomeSourceModelSubject.valueOrNull;

  // ===== INSTALLMENT LOAN SPECIFIC METHODS =====
  
  /// Update collateral owner birth year
  void updateCollateralOwnerBirthYear(String birthYear) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral owner birth year to: $birthYear');
    updateFieldAndValidate(_collateralOwnerBirthYearSubject, birthYear);
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral owner birth year updated');
  }

  /// Update vehicle registration date
  void updateVehicleRegistrationDate(String date) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating vehicle registration date to: $date');
    updateFieldAndValidate(_vehicleRegistrationDateSubject, date);
    appLogger.d('✅ InstallmentLoanFormBloc: Vehicle registration date updated');
  }

  /// Update loan term
  void updateLoanTerm(ConfigModel termModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan term to: ${termModel.id}');
    _loanTermModelSubject.add(termModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan term updated');
  }

  /// Update collateral type
  void updateCollateralType(CollateralCategoryModel collateralTypeModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral type to: ${collateralTypeModel.id}');
    _collateralTypeModelSubject.add(collateralTypeModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral type updated');
  }


  /// Update collateral condition
  void updateCollateralCondition(ConfigModel conditionModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating collateral condition to: ${conditionModel.id}');
    _collateralConditionModelSubject.add(conditionModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Collateral condition updated');
  }


  /// Update loan method
  void updateLoanMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan method to: ${methodModel.id}');
    _loanMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan method updated');
  }

  /// Update loan purpose
  void updateLoanPurpose(ConfigModel purposeModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating loan purpose to: ${purposeModel.id}');
    _loanPurposeModelSubject.add(purposeModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Loan purpose updated');
  }

  /// Update repayment method
  void updateRepaymentMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating repayment method to: ${methodModel.id}');
    _repaymentMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Repayment method updated');
  }

  /// Update disbursement method
  void updateDisbursementMethod(ConfigModel methodModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating disbursement method to: ${methodModel.id}');
    _disbursementMethodModelSubject.add(methodModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Disbursement method updated');
  }


  /// Update income source
  void updateIncomeSource(ConfigModel sourceModel) {
    appLogger.d('🔄 InstallmentLoanFormBloc: Updating income source to: ${sourceModel.id}');
    _incomeSourceModelSubject.add(sourceModel);
    updateValidationState();
    appLogger.d('✅ InstallmentLoanFormBloc: Income source updated');
  }







  // ===== OVERRIDE SHARED METHODS =====
  
  @override
  void initializeWithCustomer(CustomerModel? customer) {
    super.initializeWithCustomer(customer);
    
    // Set installment loan specific defaults
    // Co-borrower current same permanent is now handled by base form bloc
    
    appLogger.d('✅ InstallmentLoanFormBloc: Initialized with installment loan defaults');
  }

  // ===== IMPLEMENT ABSTRACT METHODS =====
  
  @override
  List<String> validateForm() {
    final errors = <String>[];
    
    // Installment loan specific validation - only for dropdown fields
    if (loanTermModel == null) {
      errors.add('Vui lòng chọn kỳ hạn vay');
    }
    
    if (collateralTypeModel == null) {
      errors.add('Vui lòng chọn loại tài sản thế chấp');
    }
    
    // Text field validation now handled by form data validation
    
    return errors;
  }

  @override
  void syncToParentBloc() {
    // TODO: Implement sync to parent TransactionFormBloc
    appLogger.d('🔄 InstallmentLoanFormBloc: Syncing to parent bloc');
  }

  // ===== OVERRIDE DISPOSE =====
  
  @override
  void dispose() {
    appLogger.d('🔄 InstallmentLoanFormBloc: Disposing installment loan specific subjects');
    
    // Dispose installment loan specific subjects
    _loanTermModelSubject.close();
    _collateralTypeModelSubject.close();
    _collateralConditionModelSubject.close();
    _vehicleConditionAtHandoverModelSubject.close();
    _vehicleRegistrationDateSubject.close();
    _collateralOwnerBirthYearSubject.close();
    
    // Dispose additional dropdown subjects
    _loanMethodModelSubject.close();
    _loanPurposeModelSubject.close();
    _repaymentMethodModelSubject.close();
    _disbursementMethodModelSubject.close();
    _incomeSourceModelSubject.close();
    
    // Dispose base subjects
    super.dispose();
    
    appLogger.d('✅ InstallmentLoanFormBloc: All subjects disposed');
  }
}