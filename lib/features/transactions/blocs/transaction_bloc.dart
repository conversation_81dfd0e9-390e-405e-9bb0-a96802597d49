import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kiloba_biz/shared/utils/app_logger_interface.dart';
import 'package:kiloba_biz/shared/services/service_locator.dart';
import '../services/transaction_service.dart';
import '../models/proposal_list.dart';
import '../models/get_proposals_request.dart';
import '../models/transaction_exception.dart';
import 'transaction_event.dart';
import 'transaction_state.dart';

/// Bloc để quản lý transaction/proposal state và xử lý events
class TransactionBloc extends Bloc<TransactionEvent, TransactionState> {
  final TransactionService _transactionService;
  final IAppLogger _logger;
  
  // Store last used filter to maintain filter state across refreshes
  GetProposalsRequest? _lastUsedFilter;

  TransactionBloc({
    TransactionService? transactionService,
    IAppLogger? logger,
  }) : _transactionService = transactionService ?? TransactionService(),
       _logger = logger ?? getIt.get<IAppLogger>(),
       super(const TransactionInitial()) {
    
    // Register event handlers
    on<LoadTransactionList>(_onLoadTransactionList);
    on<RefreshTransactionList>(_onRefreshTransactionList);
    on<LoadMoreTransactions>(_onLoadMoreTransactions);
    on<FilterTransactions>(_onFilterTransactions);
    on<SearchTransactions>(_onSearchTransactions);
    on<ClearTransactionCache>(_onClearTransactionCache);
    on<RetryLoadTransactions>(_onRetryLoadTransactions);
    on<CreateProposal>(_onCreateProposal);
    on<ValidateProposalData>(_onValidateProposalData);
  }

  /// Load initial transaction list
  Future<void> _onLoadTransactionList(
    LoadTransactionList event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('=== START: TransactionBloc.LoadTransactionList ===');
      emit(const TransactionLoading());

      // Create default request for initial load
      final request = GetProposalsRequest(
        limit: 20,
        offset: 0,
        // createdFrom: DateTime.now().subtract(const Duration(days: 30)),
        // createdTo: DateTime.now(),
      );

      // Store the filter for future refreshes
      _lastUsedFilter = request;

      final response = await _transactionService.getProposals(request: request);

      if (response.success && response.data != null) {
        final proposalsData = response.data!;
        final proposals = proposalsData.proposals ?? [];
        
        _logger.i('Transaction list loaded successfully: ${proposals.length} items');
        
        if (proposals.isEmpty) {
          emit(const TransactionEmpty());
        } else {
          emit(TransactionLoaded(
            transactions: proposals,
            totalCount: proposalsData.totalCount ?? 0,
            hasMoreData: proposals.length >= request.limit && 
                         proposals.length < (proposalsData.totalCount ?? 0),
            currentLimit: request.limit,
            currentOffset: request.offset,
          ));
        }
      } else {
        _logger.e('Failed to load transaction list: ${response.message}');
        emit(TransactionError(
          message: response.message,
          type: TransactionExceptionType.apiError,
        ));
      }

      _logger.i('=== END: TransactionBloc.LoadTransactionList ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in LoadTransactionList: ${e.message}');
      emit(TransactionError(
        message: e.message,
        type: e.type,
        originalError: e,
      ));
    } catch (e) {
      _logger.e('Unknown error in LoadTransactionList: $e');
      emit(TransactionError(
        message: 'Lỗi không xác định khi tải danh sách giao dịch',
        type: TransactionExceptionType.serverError,
        originalError: e,
      ));
    }
  }

  /// Refresh transaction list
  Future<void> _onRefreshTransactionList(
    RefreshTransactionList event,
    Emitter<TransactionState> emit,
  ) async {
    // Store current filter and transactions before emitting loading state
    GetProposalsRequest? currentFilter;
    List<ProposalItem>? previousTransactions;
    
    if (state is TransactionLoaded) {
      final currentState = state as TransactionLoaded;
      currentFilter = currentState.currentFilter;
      previousTransactions = currentState.transactions;
      emit(currentState.copyWith(isRefreshing: true));
    } else {
      emit(const TransactionLoading(isRefreshing: true));
    }

    try {
      _logger.i('=== START: TransactionBloc.RefreshTransactionList ===');

      // Create request for refresh - prioritize current filter, then last used filter
      GetProposalsRequest request;
      final filterToUse = currentFilter ?? _lastUsedFilter;
      
      if (filterToUse != null) {
        // Use existing filter and reset offset for refresh
        request = GetProposalsRequest(
          limit: filterToUse.limit,
          offset: 0,
          createdFrom: filterToUse.createdFrom,
          createdTo: filterToUse.createdTo,
          status: filterToUse.status,
          productId: filterToUse.productId,
          branchIds: filterToUse.branchIds,
          regionIds: filterToUse.regionIds,
          assignedEmployeeIds: filterToUse.assignedEmployeeIds,
          pKeySearch: filterToUse.pKeySearch,
        );
      } else {
        // Default request if no filter available
        request = GetProposalsRequest(
          limit: 20,
          offset: 0,
          createdFrom: DateTime.now().subtract(const Duration(days: 30)),
          createdTo: DateTime.now(),
        );
      }

      final response = await _transactionService.getProposals(request: request);

      if (response.success && response.data != null) {
        final proposalsData = response.data!;
        final proposals = proposalsData.proposals ?? [];
        
        _logger.i('Transaction list refreshed successfully: ${proposals.length} items');
        
        if (proposals.isEmpty) {
          emit(const TransactionEmpty());
        } else {
          emit(TransactionLoaded(
            transactions: proposals,
            totalCount: proposalsData.totalCount ?? 0,
            hasMoreData: proposals.length >= request.limit && 
                         proposals.length < (proposalsData.totalCount ?? 0),
            currentLimit: request.limit,
            currentOffset: request.offset,
            currentFilter: request,
            isRefreshing: false,
          ));
        }
      } else {
        _logger.e('Failed to refresh transaction list: ${response.message}');
        emit(TransactionError(
          message: response.message,
          type: TransactionExceptionType.apiError,
          previousTransactions: previousTransactions,
        ));
      }

      _logger.i('=== END: TransactionBloc.RefreshTransactionList ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in RefreshTransactionList: ${e.message}');
      emit(TransactionError(
        message: e.message,
        type: e.type,
        originalError: e,
        previousTransactions: previousTransactions,
      ));
    } catch (e) {
      _logger.e('Unknown error in RefreshTransactionList: $e');
      emit(TransactionError(
        message: 'Lỗi không xác định khi làm mới danh sách giao dịch',
        type: TransactionExceptionType.serverError,
        originalError: e,
        previousTransactions: previousTransactions,
      ));
    }
  }

  /// Load more transactions (pagination)
  Future<void> _onLoadMoreTransactions(
    LoadMoreTransactions event,
    Emitter<TransactionState> emit,
  ) async {
    if (state is! TransactionLoaded) return;
    
    final currentState = state as TransactionLoaded;
    
    // Don't load more if already loading or no more data
    if (currentState.isLoadingMore || !currentState.hasMoreData) return;

    try {
      _logger.i('=== START: TransactionBloc.LoadMoreTransactions ===');
      
      emit(currentState.copyWith(isLoadingMore: true));

      // Create request for load more with current offset
      final request = currentState.currentFilter ?? GetProposalsRequest(
        limit: event.limit,
        offset: currentState.transactions.length, // Use current length as offset
        createdFrom: DateTime.now().subtract(const Duration(days: 30)),
        createdTo: DateTime.now(),
      );
      
      // Update request with new offset
      final loadMoreRequest = GetProposalsRequest(
        limit: request.limit,
        offset: currentState.transactions.length,
        createdFrom: request.createdFrom,
        createdTo: request.createdTo,
        status: request.status,
        productId: request.productId,
        branchIds: request.branchIds,
        regionIds: request.regionIds,
        assignedEmployeeIds: request.assignedEmployeeIds,
      );

      final response = await _transactionService.getProposals(request: loadMoreRequest);

      if (response.success && response.data != null) {
        final proposalsData = response.data!;
        final newProposals = proposalsData.proposals ?? [];
        final newTransactions = [...currentState.transactions, ...newProposals];
        
        _logger.i('Load more completed: ${newProposals.length} new items');
        
        emit(TransactionLoaded(
          transactions: newTransactions,
          totalCount: proposalsData.totalCount ?? 0,
          hasMoreData: newTransactions.length < (proposalsData.totalCount ?? 0),
          currentLimit: loadMoreRequest.limit,
          currentOffset: loadMoreRequest.offset,
          currentFilter: loadMoreRequest,
          currentSearchQuery: currentState.currentSearchQuery,
          isLoadingMore: false,
        ));
      } else {
        _logger.e('Failed to load more transactions: ${response.message}');
        emit(currentState.copyWith(isLoadingMore: false));
      }

      _logger.i('=== END: TransactionBloc.LoadMoreTransactions ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in LoadMoreTransactions: ${e.message}');
      emit(currentState.copyWith(isLoadingMore: false));
    } catch (e) {
      _logger.e('Unknown error in LoadMoreTransactions: $e');
      emit(currentState.copyWith(isLoadingMore: false));
    }
  }

  /// Filter transactions
  Future<void> _onFilterTransactions(
    FilterTransactions event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('=== START: TransactionBloc.FilterTransactions ===');
      emit(const TransactionLoading());

      final request = event.request ?? GetProposalsRequest(
        limit: 20,
        offset: 0,
        createdFrom: DateTime.now().subtract(const Duration(days: 30)),
        createdTo: DateTime.now(),
      );

      // Store the filter for future refreshes
      _lastUsedFilter = request;

      final response = await _transactionService.getProposals(request: request);

      if (response.success && response.data != null) {
        final proposalsData = response.data!;
        final proposals = proposalsData.proposals ?? [];
        
        _logger.i('Filtered transactions loaded: ${proposals.length} items');
        
        if (proposals.isEmpty) {
          emit(const TransactionEmpty(hasFilters: true));
        } else {
          emit(TransactionLoaded(
            transactions: proposals,
            totalCount: proposalsData.totalCount ?? 0,
            hasMoreData: proposals.length >= request.limit && 
                         proposals.length < (proposalsData.totalCount ?? 0),
            currentLimit: request.limit,
            currentOffset: request.offset,
            currentFilter: request,
          ));
        }
      } else {
        _logger.e('Failed to filter transactions: ${response.message}');
        emit(TransactionError(
          message: response.message,
          type: TransactionExceptionType.apiError,
        ));
      }

      _logger.i('=== END: TransactionBloc.FilterTransactions ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in FilterTransactions: ${e.message}');
      emit(TransactionError(
        message: e.message,
        type: e.type,
        originalError: e,
      ));
    } catch (e) {
      _logger.e('Unknown error in FilterTransactions: $e');
      emit(TransactionError(
        message: 'Lỗi không xác định khi lọc giao dịch',
        type: TransactionExceptionType.serverError,
        originalError: e,
      ));
    }
  }

  /// Search transactions
  Future<void> _onSearchTransactions(
    SearchTransactions event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('=== START: TransactionBloc.SearchTransactions ===');
      _logger.i('Search query: ${event.searchQuery}');
      
      emit(const TransactionLoading());

      // Create search request - for now just use basic request
      // In future can add search parameters to GetProposalsRequest
      final request = GetProposalsRequest(
        limit: 20,
        offset: 0,
        createdFrom: DateTime.now().subtract(const Duration(days: 30)),
        createdTo: DateTime.now(),
      );

      final response = await _transactionService.getProposals(request: request);

      if (response.success && response.data != null) {
        final proposalsData = response.data!;
        final proposals = proposalsData.proposals ?? [];
        
        // For now, do client-side search filtering
        // In future, this should be handled by API
        final filteredTransactions = proposals.where((transaction) {
          final searchLower = event.searchQuery.toLowerCase();
          return transaction.borrowerName?.toLowerCase().contains(searchLower) == true ||
                 transaction.productName?.toLowerCase().contains(searchLower) == true ||
                 transaction.borrowerPhone?.toLowerCase().contains(searchLower) == true;
        }).toList();
        
        _logger.i('Search completed: ${filteredTransactions.length} results found');
        
        if (filteredTransactions.isEmpty) {
          emit(const TransactionEmpty(hasFilters: true));
        } else {
          emit(TransactionLoaded(
            transactions: filteredTransactions,
            totalCount: filteredTransactions.length,
            hasMoreData: false, // No pagination for search results for now
            currentLimit: request.limit,
            currentOffset: request.offset,
            currentSearchQuery: event.searchQuery,
          ));
        }
      } else {
        _logger.e('Failed to search transactions: ${response.message}');
        emit(TransactionError(
          message: response.message,
          type: TransactionExceptionType.apiError,
        ));
      }

      _logger.i('=== END: TransactionBloc.SearchTransactions ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in SearchTransactions: ${e.message}');
      emit(TransactionError(
        message: e.message,
        type: e.type,
        originalError: e,
      ));
    } catch (e) {
      _logger.e('Unknown error in SearchTransactions: $e');
      emit(TransactionError(
        message: 'Lỗi không xác định khi tìm kiếm giao dịch',
        type: TransactionExceptionType.serverError,
        originalError: e,
      ));
    }
  }

  /// Clear transaction cache
  Future<void> _onClearTransactionCache(
    ClearTransactionCache event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('Clearing transaction cache');
      // TransactionService doesn't have cache method yet, but we can add it later
    } catch (e) {
      _logger.e('Error clearing transaction cache: $e');
    }
  }

  /// Retry loading transactions
  Future<void> _onRetryLoadTransactions(
    RetryLoadTransactions event,
    Emitter<TransactionState> emit,
  ) async {
    // Nếu có filter request, sử dụng FilterTransactions
    if (event.request != null) {
      add(FilterTransactions(request: event.request));
    } else {
      // Nếu không có filter, load danh sách mặc định
      add(const LoadTransactionList());
    }
  }

  /// Create new proposal
  Future<void> _onCreateProposal(
    CreateProposal event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('=== START: TransactionBloc.CreateProposal ===');
      emit(const ProposalCreating());

      final response = await _transactionService.createProposal(request: event.request);

      if (response.success && response.data != null) {
        final proposalData = response.data!;
        
        _logger.i('Proposal created successfully: ${proposalData.proposalId}');
        
        emit(ProposalCreated(
          proposalId: proposalData.proposalId ?? '',
          status: proposalData.status,
          message: proposalData.message,
          createdAt: proposalData.createdAt,
        ));
      } else {
        _logger.e('Failed to create proposal: ${response.message}');
        emit(ProposalError(
          message: response.message,
          type: TransactionExceptionType.apiError,
        ));
      }

      _logger.i('=== END: TransactionBloc.CreateProposal ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in CreateProposal: ${e.message}');
      emit(ProposalError(
        message: e.message,
        type: e.type,
        originalError: e,
      ));
    } catch (e) {
      _logger.e('Unknown error in CreateProposal: $e');
      emit(ProposalError(
        message: 'Lỗi không xác định khi tạo proposal',
        type: TransactionExceptionType.serverError,
        originalError: e,
      ));
    }
  }

  /// Validate proposal data
  Future<void> _onValidateProposalData(
    ValidateProposalData event,
    Emitter<TransactionState> emit,
  ) async {
    try {
      _logger.i('=== START: TransactionBloc.ValidateProposalData ===');

      final isValid = _transactionService.validateProposalData(event.request);
      
      _logger.i('Proposal data validation result: $isValid');
      
      emit(ProposalValidated(
        isValid: isValid,
        errors: null,
      ));

      _logger.i('=== END: TransactionBloc.ValidateProposalData ===');
    } on TransactionException catch (e) {
      _logger.e('TransactionException in ValidateProposalData: ${e.message}');
      emit(ProposalValidated(
        isValid: false,
        errors: [e.message],
      ));
    } catch (e) {
      _logger.e('Unknown error in ValidateProposalData: $e');
      emit(ProposalValidated(
        isValid: false,
        errors: ['Lỗi không xác định khi validate dữ liệu'],
      ));
    }
  }

  @override
  Future<void> close() {
    _logger.i('TransactionBloc disposed');
    return super.close();
  }
}
