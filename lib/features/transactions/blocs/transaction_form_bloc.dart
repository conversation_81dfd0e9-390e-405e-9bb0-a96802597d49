import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../shared/utils/app_logger.dart';
import '../../products/models/product_model.dart';
import '../../customers/models/customer_model.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import '../models/document_model.dart';

// Events với type safety
abstract class TransactionFormEvent {}

class SelectProduct extends TransactionFormEvent {
  final ProductModel product;
  SelectProduct(this.product);
}

class SelectCustomer extends TransactionFormEvent {
  final CustomerModel customer;
  SelectCustomer(this.customer);
}

class UpdateFormData extends TransactionFormEvent {
  final BaseFormData formData; // InstallmentLoanFormData hoặc subclass khác
  UpdateFormData(this.formData);
}

class SetDefaults extends TransactionFormEvent {
  final CustomerModel? customer; // Optional customer data for pre-filling
  SetDefaults({this.customer});
}

class ValidateForm extends TransactionFormEvent {}

class ClearForm extends TransactionFormEvent {}

class UpdateDocuments extends TransactionFormEvent {
  final Map<String, List<DocumentModel>> documents;
  UpdateDocuments(this.documents);
}

// States với typed models
abstract class TransactionFormState {}

class TransactionFormInitial extends TransactionFormState {}

class TransactionFormLoaded extends TransactionFormState {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final BaseFormData? formData; // InstallmentLoanFormData hoặc PersonalLoanFormData
  final Map<String, List<DocumentModel>> documents;
  final bool isValid;
  final List<String> validationErrors;

  TransactionFormLoaded({
    this.selectedProduct,
    this.selectedCustomer,
    this.formData,
    this.documents = const {},
    this.isValid = false,
    this.validationErrors = const [],
  });

  TransactionFormLoaded copyWith({
    ProductModel? selectedProduct,
    CustomerModel? selectedCustomer,
    BaseFormData? formData,
    Map<String, List<DocumentModel>>? documents,
    bool? isValid,
    List<String>? validationErrors,
  }) {
    return TransactionFormLoaded(
      selectedProduct: selectedProduct ?? this.selectedProduct,
      selectedCustomer: selectedCustomer ?? this.selectedCustomer,
      formData: formData ?? this.formData,
      documents: documents ?? this.documents,
      isValid: isValid ?? this.isValid,
      validationErrors: validationErrors ?? this.validationErrors,
    );
  }

  @override
  String toString() {
    return 'TransactionFormLoaded(product: ${selectedProduct?.code}, customer: ${selectedCustomer?.fullName}, formDataType: ${formData.runtimeType}, isValid: $isValid)';
  }
}

class TransactionFormBloc extends Bloc<TransactionFormEvent, TransactionFormState> {
  TransactionFormBloc() : super(TransactionFormInitial()) {
    on<SelectProduct>(_onSelectProduct);
    on<SelectCustomer>(_onSelectCustomer);
    on<UpdateFormData>(_onUpdateFormData);
    on<SetDefaults>(_onSetDefaults);
    on<ValidateForm>(_onValidateForm);
    on<ClearForm>(_onClearForm);
    on<UpdateDocuments>(_onUpdateDocuments);
  }

  void _onSelectProduct(SelectProduct event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Selecting product ${event.product.code}');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      // Create appropriate form data based on product type
      BaseFormData? newFormData;
      switch (event.product.code) {
        case 'GOLD_LOAN':
        case 'MANGO':
          newFormData = InstallmentLoanFormData();
          appLogger.d('✅ Created InstallmentLoanFormData for ${event.product.code}');
          break;
        case 'PERSONAL_LOAN':
          // TODO: newFormData = PersonalLoanFormData();
          appLogger.w('⚠️ PersonalLoanFormData not implemented yet');
          break;
        default:
          appLogger.w('⚠️ Unknown product code: ${event.product.code}');
      }
      
      emit(currentState.copyWith(
        selectedProduct: event.product,
        formData: newFormData,
        isValid: false,
        validationErrors: [],
      ));
    } else {
      // Initial state
      BaseFormData? initialFormData;
      switch (event.product.code) {
        case 'GOLD_LOAN':
        case 'MANGO':
          initialFormData = InstallmentLoanFormData();
          appLogger.d('✅ Created initial InstallmentLoanFormData for ${event.product.code}');
          break;
        case 'PERSONAL_LOAN':
          // TODO: initialFormData = PersonalLoanFormData();
          appLogger.w('⚠️ PersonalLoanFormData not implemented yet');
          break;
        default:
          appLogger.w('⚠️ Unknown product code: ${event.product.code}');
      }
      
      emit(TransactionFormLoaded(
        selectedProduct: event.product,
        formData: initialFormData,
      ));
    }
    
    appLogger.d('✅ Product selected: ${event.product.code}');
  }

  void _onSelectCustomer(SelectCustomer event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Selecting customer ${event.customer.fullName}');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      // Auto-fill form data với customer information
      BaseFormData? updatedFormData = currentState.formData;
      if (updatedFormData is InstallmentLoanFormData) {
        updatedFormData = _mapCustomerToInstallmentForm(updatedFormData, event.customer);
        appLogger.d('✅ Mapped customer data to InstallmentLoanFormData');
      }
      
      emit(currentState.copyWith(
        selectedCustomer: event.customer,
        formData: updatedFormData,
      ));
      
      appLogger.d('✅ Customer selected and data mapped: ${event.customer.fullName}');
    } else {
      // Initial state - create new loaded state with customer
      emit(TransactionFormLoaded(
        selectedCustomer: event.customer,
      ));
      
      appLogger.d('✅ Customer selected in initial state: ${event.customer.fullName}');
    }
  }

  void _onUpdateFormData(UpdateFormData event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Updating form data (${event.formData.runtimeType})');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      // Validate form data
      final isValid = event.formData.isValid;
      final errors = event.formData.validationErrors;
      
      emit(currentState.copyWith(
        formData: event.formData,
        isValid: isValid,
        validationErrors: errors,
      ));
      
      appLogger.d('✅ Form data updated - isValid: $isValid, errors: ${errors.length}');
    }
  }

  void _onSetDefaults(SetDefaults event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Setting defaults based on product type');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      final productCode = currentState.selectedProduct?.code;
      
      if (productCode == null) {
        appLogger.w('⚠️ Cannot set defaults: No product selected');
        return;
      }
      
      BaseFormData? updatedFormData = currentState.formData;
      
      // Set defaults based on product type
      switch (productCode) {
        case 'GOLD_LOAN':
        case 'MANGO':
          if (updatedFormData is InstallmentLoanFormData) {
            updatedFormData = updatedFormData.copyWith(
              hasCoBorrower: true,
              borrowerCurrentSamePermanent: true,
              coBorrowerCurrentSamePermanent: true,
            );
            
            appLogger.d('✅ Set installment loan defaults - hasCoBorrower: true, currentSamePermanent: true');
            
            // Map customer data if available
            if (event.customer != null) {
              updatedFormData = _mapCustomerToInstallmentForm(updatedFormData, event.customer!);
              appLogger.d('✅ Mapped customer data: ${event.customer!.fullName}');
            }
          }
          break;
          
        case 'PERSONAL_LOAN':
          // TODO: Implement when PersonalLoanFormData is ready
          appLogger.w('⚠️ PersonalLoanFormData not implemented yet');
          break;
          
        default:
          appLogger.w('⚠️ Unknown product code for defaults: $productCode');
      }
      
      if (updatedFormData != null) {
        emit(currentState.copyWith(formData: updatedFormData));
        appLogger.d('✅ Defaults set successfully for product: $productCode');
      }
    } else {
      appLogger.w('⚠️ Cannot set defaults: No form loaded');
    }
  }

  void _onValidateForm(ValidateForm event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Validating form');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      if (currentState.formData != null) {
        final isValid = currentState.formData!.isValid;
        final errors = currentState.formData!.validationErrors;
        
        emit(currentState.copyWith(
          isValid: isValid,
          validationErrors: errors,
        ));
        
        appLogger.d('✅ Form validation completed - isValid: $isValid, errors: ${errors.length}');
      }
    }
  }

  void _onClearForm(ClearForm event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Clearing form');
    emit(TransactionFormInitial());
    appLogger.d('✅ Form cleared');
  }

  void _onUpdateDocuments(UpdateDocuments event, Emitter<TransactionFormState> emit) {
    appLogger.d('🔄 TransactionFormBloc: Updating documents');
    
    if (state is TransactionFormLoaded) {
      final currentState = state as TransactionFormLoaded;
      
      emit(currentState.copyWith(
        documents: event.documents,
      ));
      
      appLogger.d('✅ Documents updated - ${event.documents.keys.length} categories');
    }
  }

  // Helper method to map customer to InstallmentLoanFormData
  InstallmentLoanFormData _mapCustomerToInstallmentForm(
    InstallmentLoanFormData formData, 
    CustomerModel customer
  ) {
    appLogger.d('🔄 Mapping customer data to InstallmentLoanFormData');
    appLogger.d('   - Customer: ${customer.fullName}');
    appLogger.d('   - Province: ${customer.province?.id} (${customer.province?.name})');
    appLogger.d('   - Ward: ${customer.ward?.id} (${customer.ward?.name})');
    
    final result = formData.copyWith(
      borrowerName: customer.fullName.isNotEmpty ? customer.fullName : null,
      borrowerPhone: customer.phoneNumber?.isNotEmpty == true ? customer.phoneNumber : null,
      borrowerIdNumber: customer.idCardNumber?.isNotEmpty == true ? customer.idCardNumber : null,
      borrowerIdIssueDate: customer.idCardIssueDate != null ? _formatDateForForm(customer.idCardIssueDate!) : null,
      borrowerIdExpiryDate: customer.idCardExpiryDate != null ? _formatDateForForm(customer.idCardExpiryDate!) : null,
      borrowerIdIssuePlace: customer.idCardIssuePlace?.isNotEmpty == true ? customer.idCardIssuePlace : null,
      borrowerBirthDate: customer.birthDate != null ? _formatDateForForm(customer.birthDate!) : null,
      borrowerGender: customer.gender?.id?.isNotEmpty == true ? customer.gender!.id : null,
      borrowerGenderModel: customer.gender?.id?.isNotEmpty == true ? customer.gender : null,
      borrowerPermanentAddress: customer.permanentAddress?.isNotEmpty == true ? customer.permanentAddress : null,
      borrowerPermanentProvinceId: customer.province?.id.isNotEmpty == true ? customer.province!.id : null,
      borrowerPermanentProvinceModel: customer.province?.id.isNotEmpty == true ? customer.province : null,
      borrowerPermanentWardId: customer.ward?.id?.isNotEmpty == true ? customer.ward!.id : null,
      borrowerPermanentWardModel: customer.ward?.id?.isNotEmpty == true ? customer.ward : null,
      borrowerCurrentAddress: customer.currentAddress?.isNotEmpty == true ? customer.currentAddress : (customer.sameAddress && customer.permanentAddress?.isNotEmpty == true ? customer.permanentAddress : null),
      borrowerCurrentSamePermanent: customer.sameAddress,
      // Map collateral info - auto-fill from customer name and birth date
      collateralOwner: customer.fullName.isNotEmpty ? customer.fullName : null,
      collateralOwnerBirthYear: customer.birthDate != null ? _formatDateForForm(customer.birthDate!) : null,
    );
    
    appLogger.d('✅ Mapped result:');
    appLogger.d('   - borrowerName: ${result.borrowerName}');
    appLogger.d('   - borrowerGender: ${result.borrowerGender}');
    appLogger.d('   - borrowerPermanentProvinceId: ${result.borrowerPermanentProvinceId}');
    appLogger.d('   - borrowerPermanentWardId: ${result.borrowerPermanentWardId}');
    appLogger.d('   - collateralOwner: ${result.collateralOwner}');
    appLogger.d('   - collateralOwnerBirthYear: ${result.collateralOwnerBirthYear}');
    
    return result;
  }

  String _formatDateForForm(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
