
/// Abstract base class for all form data types
/// Provides common interface for form data operations
abstract class BaseFormData {
  /// Convert form data to Map for API calls
  Map<String, dynamic> toMap();
  
  /// Create a copy of form data with updated fields
  /// This method should be implemented by concrete classes with type-safe parameters
  BaseFormData copyWith();
  
  /// Get field value by key (generic method for dynamic access)
  T? getFieldValue<T>(String key) {
    return toMap()[key] as T?;
  }
  
  /// Set field value by key (generic method for dynamic access)
  /// This method is deprecated - use type-safe copyWith instead
  @Deprecated('Use type-safe copyWith method instead')
  BaseFormData setFieldValue<T>(String key, T value) {
    throw UnimplementedError('Use type-safe copyWith method instead');
  }
  
  /// Check if form data is valid (to be implemented by concrete classes)
  bool get isValid;
  
  /// Get validation errors (to be implemented by concrete classes)
  List<String> get validationErrors;
}
