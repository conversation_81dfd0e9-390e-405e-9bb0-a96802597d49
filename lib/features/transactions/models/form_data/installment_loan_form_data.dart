import 'package:json_annotation/json_annotation.dart';
import 'base_form_data.dart';
import '../enums/loan_type_enum.dart';
import '../../../../shared/models/province_model.dart';
import '../../../../shared/models/ward_model.dart';
import '../../../../shared/models/config_model.dart';
import '../../../../shared/models/collateral_category_model.dart';

part 'installment_loan_form_data.g.dart';

/// Form data for Installment Loan products (Gold Loan, Mango, etc.)
/// This covers all installment loan products that may require collateral
@JsonSerializable(fieldRename: FieldRename.snake)
class InstallmentLoanFormData extends BaseFormData {
  // ===== BORROWER INFORMATION =====
  // Maps to BorrowerInfo in proposal_request.dart
  
  /// Full name of the main borrower (maps to BorrowerInfo.fullName)
  final String? borrowerName;
  
  /// Type of ID document (CCCD, Passport, etc.) - not used in API
  final String? borrowerIdType;
  
  // Config model for borrower ID type (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? borrowerIdTypeModel;
  
  /// ID number (maps to BorrowerInfo.idNo)
  final String? borrowerIdNumber;
  
  /// ID issue date (maps to BorrowerInfo.issueDate)
  final String? borrowerIdIssueDate;
  
  /// ID expiry date (maps to BorrowerInfo.expiryDate)
  final String? borrowerIdExpiryDate;
  
  /// ID issue place (maps to BorrowerInfo.issuePlace)
  final String? borrowerIdIssuePlace;
  
  /// Date of birth (maps to BorrowerInfo.dob)
  final String? borrowerBirthDate;
  
  /// Gender ID from config (maps to BorrowerInfo.sex)
  final String? borrowerGender;
  
  // Config models for borrower info (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? borrowerGenderModel;
  
  /// Phone number (maps to BorrowerInfo.phoneNumber)
  final String? borrowerPhone;
  
  /// Permanent province ID (maps to BorrowerInfo.permanentProvinceId)
  final String? borrowerPermanentProvinceId;
  
  /// Permanent ward ID (maps to BorrowerInfo.permanentWardId)
  final String? borrowerPermanentWardId;
  
  /// Permanent address detail (maps to BorrowerInfo.permanentAddressDetail)
  final String? borrowerPermanentAddress;
  
  // Province and Ward models for borrower permanent address (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ProvinceModel? borrowerPermanentProvinceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final WardModel? borrowerPermanentWardModel;
  
  /// Marital status ID from config (maps to BorrowerInfo.maritalStatusId)
  final String? borrowerMaritalStatusId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? borrowerMaritalStatusModel;
  
  /// Whether current address is same as permanent address
  final bool borrowerCurrentSamePermanent;
  
  /// Current province ID (maps to BorrowerInfo.currentProvinceId)
  final String? borrowerCurrentProvinceId;
  
  /// Current ward ID (maps to BorrowerInfo.currentWardId)
  final String? borrowerCurrentWardId;
  
  /// Current address detail (maps to BorrowerInfo.currentAddressDetail)
  final String? borrowerCurrentAddress;
  
  // Province and Ward models for borrower current address (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ProvinceModel? borrowerCurrentProvinceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final WardModel? borrowerCurrentWardModel;
  
  // ===== CO-BORROWER INFORMATION =====
  // Maps to BorrowerInfo in proposal_request.dart (coBorrower field)
  
  /// Whether co-borrower exists
  final bool hasCoBorrower;
  
  /// Full name of co-borrower (maps to BorrowerInfo.fullName)
  final String? coBorrowerName;
  
  /// Type of ID document for co-borrower - not used in API
  final String? coBorrowerIdType;
  
  // Config model for co-borrower ID type (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? coBorrowerIdTypeModel;
  
  /// ID number of co-borrower (maps to BorrowerInfo.idNo)
  final String? coBorrowerIdNumber;
  
  /// ID issue date of co-borrower (maps to BorrowerInfo.issueDate)
  final String? coBorrowerIdIssueDate;
  
  /// ID expiry date of co-borrower (maps to BorrowerInfo.expiryDate)
  final String? coBorrowerIdExpiryDate;
  
  /// ID issue place of co-borrower (maps to BorrowerInfo.issuePlace)
  final String? coBorrowerIdIssuePlace;
  
  /// Date of birth of co-borrower (maps to BorrowerInfo.dob)
  final String? coBorrowerBirthDate;
  
  /// Gender ID of co-borrower (maps to BorrowerInfo.sex)
  final String? coBorrowerGender;
  
  /// Phone number of co-borrower (maps to BorrowerInfo.phoneNumber)
  final String? coBorrowerPhone;
  
  /// Permanent province ID of co-borrower (maps to BorrowerInfo.permanentProvinceId)
  final String? coBorrowerPermanentProvinceId;
  
  /// Permanent ward ID of co-borrower (maps to BorrowerInfo.permanentWardId)
  final String? coBorrowerPermanentWardId;
  
  /// Permanent address detail of co-borrower (maps to BorrowerInfo.permanentAddressDetail)
  final String? coBorrowerPermanentAddress;
  
  // Province and Ward models for co-borrower permanent address (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ProvinceModel? coBorrowerPermanentProvinceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final WardModel? coBorrowerPermanentWardModel;
  
  /// Marital status ID of co-borrower (maps to BorrowerInfo.maritalStatusId)
  final String? coBorrowerMaritalStatusId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? coBorrowerMaritalStatusModel;
  
  /// Whether co-borrower current address is same as permanent address
  final bool coBorrowerCurrentSamePermanent;
  
  /// Current province ID of co-borrower (maps to BorrowerInfo.currentProvinceId)
  final String? coBorrowerCurrentProvinceId;
  
  /// Current ward ID of co-borrower (maps to BorrowerInfo.currentWardId)
  final String? coBorrowerCurrentWardId;
  
  /// Current address detail of co-borrower (maps to BorrowerInfo.currentAddressDetail)
  final String? coBorrowerCurrentAddress;
  
  // Province and Ward models for co-borrower current address (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ProvinceModel? coBorrowerCurrentProvinceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final WardModel? coBorrowerCurrentWardModel;
  
  // ===== LOAN PROPOSAL =====
  // Maps to LoanPlan in proposal_request.dart
  
  /// Loan type (with/without collateral)
  @JsonKey(name: 'loan_type')
  final LoanType? loanType;
  
  /// Own capital amount (maps to LoanPlan.ownCapital)
  final int? ownCapital;
  
  /// Requested loan amount (maps to LoanPlan.requestedAmount)
  final int? loanAmount;
  
  /// Loan term ID from config (maps to LoanPlan.loanTermId)
  final String? loanTermId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? loanTermModel;
  
  /// Loan method ID from config (maps to LoanPlan.loanMethodId)
  final String? loanMethodId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? loanMethodModel;
  
  /// Loan purpose ID from config (maps to LoanPlan.loanPurposeId)
  final String? loanPurposeId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? loanPurposeModel;
  
  /// Loan purpose name (maps to LoanPlan.loanPurposeName)
  final String? loanPurposeName;
  
  /// Other loan purpose description
  final String? loanPurposeOther;
  
  /// Repayment method ID from config (maps to LoanPlan.repaymentMethodId)
  final String? repaymentMethodId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? repaymentMethodModel;
  
  /// Disbursement method ID from config (maps to LoanPlan.disbursementMethodId)
  final String? disbursementMethodId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? disbursementMethodModel;
  
  /// Receiving account number (maps to LoanPlan.receivingAccountNumber)
  final String? disbursementAccount;
  
  /// Total capital need (calculated field)
  final int? totalCapitalNeed;
  
  // ===== FINANCIAL INFORMATION =====
  // Maps to FinancialInfo in proposal_request.dart
  
  /// Income source ID from config (maps to FinancialInfo.incomeSourceId)
  final String? incomeSourceId;
  
  /// Average revenue per day (maps to FinancialInfo.averageRevenuePerDay)
  final int? dailyRevenue;
  
  /// Average income per day (maps to FinancialInfo.averageIncomePerDay)
  final int? dailyIncome;
  
  /// Business location province ID (maps to FinancialInfo.businessProvinceId)
  final String? businessLocationProvinceId;
  
  /// Business location ward ID (maps to FinancialInfo.businessWardId)
  final String? businessLocationWardId;
  
  /// Business location address detail (maps to FinancialInfo.businessAddressDetail)
  final String? businessLocationAddress;
  
  /// Employer name - not used in current API
  final String? employerName;
  
  /// Position - not used in current API
  final String? position;
  
  /// Work experience - not used in current API
  final String? workExperience;
  
  /// Monthly salary - not used in current API
  final int? monthlySalary;
  
  // Config models for financial information (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? incomeSourceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ProvinceModel? businessLocationProvinceModel;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final WardModel? businessLocationWardModel;
  
  // ===== COLLATERAL INFORMATION =====
  // Maps to CollateralInfo in proposal_request.dart
  
  /// Collateral type ID (maps to CollateralInfo.typeId)
  final String? collateralTypeId;
  
  /// Collateral type name - not used in API
  final String? collateralType;
  
  /// Collateral value (maps to CollateralInfo.value)
  final int? collateralValue;
  
  /// Collateral value text - not used in API
  final String? collateralValueText;
  
  /// Asset condition ID (maps to CollateralInfo.condition)
  final String? collateralConditionId;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? collateralConditionModel;
  
  /// Asset owner name (maps to CollateralInfo.assetOwner)
  final String? collateralOwner;
  
  /// Asset owner birth year (maps to CollateralInfo.ownerDob)
  final String? collateralOwnerBirthYear;
  
  // Collateral category model (not sent to API)
  @JsonKey(includeFromJson: false, includeToJson: false)
  final CollateralCategoryModel? collateralTypeModel;
  
  /// Vehicle/Asset name (maps to CollateralInfo.name)
  final String? vehicleName;
  
  /// License plate number (maps to CollateralInfo.licensePlate)
  final String? vehiclePlateNumber;
  
  /// Chassis number (maps to CollateralInfo.chassisNumber)
  final String? vehicleFrameNumber;
  
  /// Engine number (maps to CollateralInfo.engineNumber)
  final String? vehicleEngineNumber;
  
  /// Registration certificate number (maps to CollateralInfo.registrationCertificateNumber)
  final String? vehicleRegistrationNumber;
  
  /// Registration issue place (maps to CollateralInfo.registrationIssuePlace)
  final String? vehicleRegistrationPlace;
  
  /// Registration issue date (maps to CollateralInfo.registrationIssueDate)
  final String? vehicleRegistrationDate;
  
  /// Handover condition ID from config (maps to CollateralInfo.handoverConditionId)
  final String? vehicleConditionAtHandover;
  
  @JsonKey(includeFromJson: false, includeToJson: false)
  final ConfigModel? vehicleConditionAtHandoverModel;
  
  /// Total collateral value (maps to CollateralInfo.totalValue)
  final int? totalCollateralValue;
  
  // Branch information
  final String? branchCode;
  
  InstallmentLoanFormData({
    // Borrower information
    this.borrowerName,
    this.borrowerIdType,
    this.borrowerIdTypeModel,
    this.borrowerIdNumber,
    this.borrowerIdIssueDate,
    this.borrowerIdExpiryDate,
    this.borrowerIdIssuePlace,
    this.borrowerBirthDate,
    this.borrowerGender,
    this.borrowerGenderModel,
    this.borrowerPhone,
    this.borrowerPermanentProvinceId,
    this.borrowerPermanentWardId,
    this.borrowerPermanentAddress,
    this.borrowerPermanentProvinceModel,
    this.borrowerPermanentWardModel,
    this.borrowerMaritalStatusId,
    this.borrowerMaritalStatusModel,
    this.borrowerCurrentSamePermanent = true,
    this.borrowerCurrentProvinceId,
    this.borrowerCurrentWardId,
    this.borrowerCurrentAddress,
    this.borrowerCurrentProvinceModel,
    this.borrowerCurrentWardModel,
    
    // Co-borrower information
    this.hasCoBorrower = true,
    this.coBorrowerName,
    this.coBorrowerIdType,
    this.coBorrowerIdTypeModel,
    this.coBorrowerIdNumber,
    this.coBorrowerIdIssueDate,
    this.coBorrowerIdExpiryDate,
    this.coBorrowerIdIssuePlace,
    this.coBorrowerBirthDate,
    this.coBorrowerGender,
    this.coBorrowerPhone,
    this.coBorrowerPermanentProvinceId,
    this.coBorrowerPermanentWardId,
    this.coBorrowerPermanentAddress,
    this.coBorrowerPermanentProvinceModel,
    this.coBorrowerPermanentWardModel,
    this.coBorrowerMaritalStatusId,
    this.coBorrowerMaritalStatusModel,
    this.coBorrowerCurrentSamePermanent = true,
    this.coBorrowerCurrentProvinceId,
    this.coBorrowerCurrentWardId,
    this.coBorrowerCurrentAddress,
    this.coBorrowerCurrentProvinceModel,
    this.coBorrowerCurrentWardModel,
    
    // Loan proposal
    this.loanType = LoanType.withCollateral,
    this.ownCapital,
    this.loanAmount,
    this.loanTermId,
    this.loanTermModel,
    this.loanMethodId,
    this.loanMethodModel,
    this.loanPurposeId,
    this.loanPurposeModel,
    this.loanPurposeName,
    this.loanPurposeOther,
    this.repaymentMethodId,
    this.repaymentMethodModel,
    this.disbursementMethodId,
    this.disbursementMethodModel,
    this.disbursementAccount,
    this.totalCapitalNeed,
    
    // Financial information
    this.incomeSourceId,
    this.dailyRevenue,
    this.dailyIncome,
    this.businessLocationProvinceId,
    this.businessLocationWardId,
    this.businessLocationAddress,
    this.employerName,
    this.position,
    this.workExperience,
    this.monthlySalary,
    this.incomeSourceModel,
    this.businessLocationProvinceModel,
    this.businessLocationWardModel,
    
    // Collateral information
    this.collateralTypeId,
    this.collateralType,
    this.collateralValue,
    this.collateralValueText,
    this.collateralConditionId,
    this.collateralConditionModel,
    this.collateralOwner,
    this.collateralOwnerBirthYear,
    this.collateralTypeModel,
    this.vehicleName,
    this.vehiclePlateNumber,
    this.vehicleFrameNumber,
    this.vehicleEngineNumber,
    this.vehicleRegistrationNumber,
    this.vehicleRegistrationPlace,
    this.vehicleRegistrationDate,
    this.vehicleConditionAtHandover,
    this.vehicleConditionAtHandoverModel,
    this.totalCollateralValue,
    
    // Branch information
    this.branchCode,
  });
  
  @override
  Map<String, dynamic> toMap() {
    return toJson();
  }
  
  /// Convert to JSON using JsonSerializable
  Map<String, dynamic> toJson() => _$InstallmentLoanFormDataToJson(this);
  
  InstallmentLoanFormData fromMap(Map<String, dynamic> map) {
    return InstallmentLoanFormData.fromJson(map);
  }
  
  /// Create from JSON using JsonSerializable
  factory InstallmentLoanFormData.fromJson(Map<String, dynamic> json) =>
      _$InstallmentLoanFormDataFromJson(json);
  
  /// Type-safe copyWith method with named parameters
  @override
  InstallmentLoanFormData copyWith({
    // Borrower information
    String? borrowerName,
    String? borrowerIdType,
    ConfigModel? borrowerIdTypeModel,
    String? borrowerIdNumber,
    String? borrowerIdIssueDate,
    String? borrowerIdExpiryDate,
    String? borrowerIdIssuePlace,
    String? borrowerBirthDate,
    String? borrowerGender,
    ConfigModel? borrowerGenderModel,
    String? borrowerPhone,
    String? borrowerPermanentProvinceId,
    String? borrowerPermanentWardId,
    String? borrowerPermanentAddress,
    ProvinceModel? borrowerPermanentProvinceModel,
    WardModel? borrowerPermanentWardModel,
    String? borrowerMaritalStatusId,
    ConfigModel? borrowerMaritalStatusModel,
    bool? borrowerCurrentSamePermanent,
    String? borrowerCurrentProvinceId,
    String? borrowerCurrentWardId,
    String? borrowerCurrentAddress,
    ProvinceModel? borrowerCurrentProvinceModel,
    WardModel? borrowerCurrentWardModel,
    
    // Co-borrower information
    bool? hasCoBorrower,
    String? coBorrowerName,
    String? coBorrowerIdType,
    ConfigModel? coBorrowerIdTypeModel,
    String? coBorrowerIdNumber,
    String? coBorrowerIdIssueDate,
    String? coBorrowerIdExpiryDate,
    String? coBorrowerIdIssuePlace,
    String? coBorrowerBirthDate,
    String? coBorrowerGender,
    String? coBorrowerPhone,
    String? coBorrowerPermanentProvinceId,
    String? coBorrowerPermanentWardId,
    String? coBorrowerPermanentAddress,
    ProvinceModel? coBorrowerPermanentProvinceModel,
    WardModel? coBorrowerPermanentWardModel,
    String? coBorrowerMaritalStatusId,
    ConfigModel? coBorrowerMaritalStatusModel,
    bool? coBorrowerCurrentSamePermanent,
    String? coBorrowerCurrentProvinceId,
    String? coBorrowerCurrentWardId,
    String? coBorrowerCurrentAddress,
    ProvinceModel? coBorrowerCurrentProvinceModel,
    WardModel? coBorrowerCurrentWardModel,
    
    // Loan proposal
    LoanType? loanType,
    int? ownCapital,
    int? loanAmount,
    String? loanTermId,
    ConfigModel? loanTermModel,
    String? loanMethodId,
    ConfigModel? loanMethodModel,
    String? loanPurposeId,
    ConfigModel? loanPurposeModel,
    String? loanPurposeName,
    String? loanPurposeOther,
    String? repaymentMethodId,
    ConfigModel? repaymentMethodModel,
    String? disbursementMethodId,
    ConfigModel? disbursementMethodModel,
    String? disbursementAccount,
    int? totalCapitalNeed,
    
    // Financial information
    String? incomeSourceId,
    int? dailyRevenue,
    int? dailyIncome,
    String? businessLocationProvinceId,
    String? businessLocationWardId,
    String? businessLocationAddress,
    String? employerName,
    String? position,
    String? workExperience,
    int? monthlySalary,
    ConfigModel? incomeSourceModel,
    ProvinceModel? businessLocationProvinceModel,
    WardModel? businessLocationWardModel,
    
    // Collateral information
    String? collateralTypeId,
    String? collateralType,
    int? collateralValue,
    String? collateralValueText,
    String? collateralConditionId,
    ConfigModel? collateralConditionModel,
    String? collateralOwner,
    String? collateralOwnerBirthYear,
    CollateralCategoryModel? collateralTypeModel,
    String? vehicleName,
    String? vehiclePlateNumber,
    String? vehicleFrameNumber,
    String? vehicleEngineNumber,
    String? vehicleRegistrationNumber,
    String? vehicleRegistrationPlace,
    String? vehicleRegistrationDate,
    String? vehicleConditionAtHandover,
    ConfigModel? vehicleConditionAtHandoverModel,
    int? totalCollateralValue,
    
    // Branch information
    String? branchCode,
  }) {
    return InstallmentLoanFormData(
      // Borrower information
      borrowerName: borrowerName ?? this.borrowerName,
      borrowerIdType: borrowerIdType ?? this.borrowerIdType,
      borrowerIdTypeModel: borrowerIdTypeModel ?? this.borrowerIdTypeModel,
      borrowerIdNumber: borrowerIdNumber ?? this.borrowerIdNumber,
      borrowerIdIssueDate: borrowerIdIssueDate ?? this.borrowerIdIssueDate,
      borrowerIdExpiryDate: borrowerIdExpiryDate ?? this.borrowerIdExpiryDate,
      borrowerIdIssuePlace: borrowerIdIssuePlace ?? this.borrowerIdIssuePlace,
      borrowerBirthDate: borrowerBirthDate ?? this.borrowerBirthDate,
      borrowerGender: borrowerGender ?? this.borrowerGender,
      borrowerGenderModel: borrowerGenderModel ?? this.borrowerGenderModel,
      borrowerPhone: borrowerPhone ?? this.borrowerPhone,
      borrowerPermanentProvinceId: borrowerPermanentProvinceId ?? this.borrowerPermanentProvinceId,
      borrowerPermanentWardId: borrowerPermanentWardId ?? this.borrowerPermanentWardId,
      borrowerPermanentAddress: borrowerPermanentAddress ?? this.borrowerPermanentAddress,
      borrowerPermanentProvinceModel: borrowerPermanentProvinceModel ?? this.borrowerPermanentProvinceModel,
      borrowerPermanentWardModel: borrowerPermanentWardModel ?? this.borrowerPermanentWardModel,
      borrowerMaritalStatusId: borrowerMaritalStatusId ?? this.borrowerMaritalStatusId,
      borrowerMaritalStatusModel: borrowerMaritalStatusModel ?? this.borrowerMaritalStatusModel,
      borrowerCurrentSamePermanent: borrowerCurrentSamePermanent ?? this.borrowerCurrentSamePermanent,
      borrowerCurrentProvinceId: borrowerCurrentProvinceId ?? this.borrowerCurrentProvinceId,
      borrowerCurrentWardId: borrowerCurrentWardId ?? this.borrowerCurrentWardId,
      borrowerCurrentAddress: borrowerCurrentAddress ?? this.borrowerCurrentAddress,
      borrowerCurrentProvinceModel: borrowerCurrentProvinceModel ?? this.borrowerCurrentProvinceModel,
      borrowerCurrentWardModel: borrowerCurrentWardModel ?? this.borrowerCurrentWardModel,
      
      // Co-borrower information
      hasCoBorrower: hasCoBorrower ?? this.hasCoBorrower,
      coBorrowerName: coBorrowerName ?? this.coBorrowerName,
      coBorrowerIdType: coBorrowerIdType ?? this.coBorrowerIdType,
      coBorrowerIdTypeModel: coBorrowerIdTypeModel ?? this.coBorrowerIdTypeModel,
      coBorrowerIdNumber: coBorrowerIdNumber ?? this.coBorrowerIdNumber,
      coBorrowerIdIssueDate: coBorrowerIdIssueDate ?? this.coBorrowerIdIssueDate,
      coBorrowerIdExpiryDate: coBorrowerIdExpiryDate ?? this.coBorrowerIdExpiryDate,
      coBorrowerIdIssuePlace: coBorrowerIdIssuePlace ?? this.coBorrowerIdIssuePlace,
      coBorrowerBirthDate: coBorrowerBirthDate ?? this.coBorrowerBirthDate,
      coBorrowerGender: coBorrowerGender ?? this.coBorrowerGender,
      coBorrowerPhone: coBorrowerPhone ?? this.coBorrowerPhone,
      coBorrowerPermanentProvinceId: coBorrowerPermanentProvinceId ?? this.coBorrowerPermanentProvinceId,
      coBorrowerPermanentWardId: coBorrowerPermanentWardId ?? this.coBorrowerPermanentWardId,
      coBorrowerPermanentAddress: coBorrowerPermanentAddress ?? this.coBorrowerPermanentAddress,
      coBorrowerPermanentProvinceModel: coBorrowerPermanentProvinceModel ?? this.coBorrowerPermanentProvinceModel,
      coBorrowerPermanentWardModel: coBorrowerPermanentWardModel ?? this.coBorrowerPermanentWardModel,
      coBorrowerMaritalStatusId: coBorrowerMaritalStatusId ?? this.coBorrowerMaritalStatusId,
      coBorrowerMaritalStatusModel: coBorrowerMaritalStatusModel ?? this.coBorrowerMaritalStatusModel,
      coBorrowerCurrentSamePermanent: coBorrowerCurrentSamePermanent ?? this.coBorrowerCurrentSamePermanent,
      coBorrowerCurrentProvinceId: coBorrowerCurrentProvinceId ?? this.coBorrowerCurrentProvinceId,
      coBorrowerCurrentWardId: coBorrowerCurrentWardId ?? this.coBorrowerCurrentWardId,
      coBorrowerCurrentAddress: coBorrowerCurrentAddress ?? this.coBorrowerCurrentAddress,
      coBorrowerCurrentProvinceModel: coBorrowerCurrentProvinceModel ?? this.coBorrowerCurrentProvinceModel,
      coBorrowerCurrentWardModel: coBorrowerCurrentWardModel ?? this.coBorrowerCurrentWardModel,
      
      // Loan proposal
      loanType: loanType ?? this.loanType,
      ownCapital: ownCapital ?? this.ownCapital,
      loanAmount: loanAmount ?? this.loanAmount,
      loanTermId: loanTermId ?? this.loanTermId,
      loanTermModel: loanTermModel ?? this.loanTermModel,
      loanMethodId: loanMethodId ?? this.loanMethodId,
      loanMethodModel: loanMethodModel ?? this.loanMethodModel,
      loanPurposeId: loanPurposeId ?? this.loanPurposeId,
      loanPurposeModel: loanPurposeModel ?? this.loanPurposeModel,
      loanPurposeName: loanPurposeName ?? this.loanPurposeName,
      loanPurposeOther: loanPurposeOther ?? this.loanPurposeOther,
      repaymentMethodId: repaymentMethodId ?? this.repaymentMethodId,
      repaymentMethodModel: repaymentMethodModel ?? this.repaymentMethodModel,
      disbursementMethodId: disbursementMethodId ?? this.disbursementMethodId,
      disbursementMethodModel: disbursementMethodModel ?? this.disbursementMethodModel,
      disbursementAccount: disbursementAccount ?? this.disbursementAccount,
      totalCapitalNeed: totalCapitalNeed ?? this.totalCapitalNeed,
      
      // Financial information
      incomeSourceId: incomeSourceId ?? this.incomeSourceId,
      dailyRevenue: dailyRevenue ?? this.dailyRevenue,
      dailyIncome: dailyIncome ?? this.dailyIncome,
      businessLocationProvinceId: businessLocationProvinceId ?? this.businessLocationProvinceId,
      businessLocationWardId: businessLocationWardId ?? this.businessLocationWardId,
      businessLocationAddress: businessLocationAddress ?? this.businessLocationAddress,
      employerName: employerName ?? this.employerName,
      position: position ?? this.position,
      workExperience: workExperience ?? this.workExperience,
      monthlySalary: monthlySalary ?? this.monthlySalary,
      incomeSourceModel: incomeSourceModel ?? this.incomeSourceModel,
      businessLocationProvinceModel: businessLocationProvinceModel ?? this.businessLocationProvinceModel,
      businessLocationWardModel: businessLocationWardModel ?? this.businessLocationWardModel,
      
      // Collateral information
      collateralTypeId: collateralTypeId ?? this.collateralTypeId,
      collateralType: collateralType ?? this.collateralType,
      collateralValue: collateralValue ?? this.collateralValue,
      collateralValueText: collateralValueText ?? this.collateralValueText,
      collateralConditionId: collateralConditionId ?? this.collateralConditionId,
      collateralConditionModel: collateralConditionModel ?? this.collateralConditionModel,
      collateralOwner: collateralOwner ?? this.collateralOwner,
      collateralOwnerBirthYear: collateralOwnerBirthYear ?? this.collateralOwnerBirthYear,
      collateralTypeModel: collateralTypeModel ?? this.collateralTypeModel,
      vehicleName: vehicleName ?? this.vehicleName,
      vehiclePlateNumber: vehiclePlateNumber ?? this.vehiclePlateNumber,
      vehicleFrameNumber: vehicleFrameNumber ?? this.vehicleFrameNumber,
      vehicleEngineNumber: vehicleEngineNumber ?? this.vehicleEngineNumber,
      vehicleRegistrationNumber: vehicleRegistrationNumber ?? this.vehicleRegistrationNumber,
      vehicleRegistrationPlace: vehicleRegistrationPlace ?? this.vehicleRegistrationPlace,
      vehicleRegistrationDate: vehicleRegistrationDate ?? this.vehicleRegistrationDate,
      vehicleConditionAtHandover: vehicleConditionAtHandover ?? this.vehicleConditionAtHandover,
      vehicleConditionAtHandoverModel: vehicleConditionAtHandoverModel ?? this.vehicleConditionAtHandoverModel,
      totalCollateralValue: totalCollateralValue ?? this.totalCollateralValue,
      
      // Branch information
      branchCode: branchCode ?? this.branchCode,
    );
  }
  
  @override
  bool get isValid {
    return validationErrors.isEmpty;
  }
  
  @override
  List<String> get validationErrors {
    final errors = <String>[];
    
    // Required field validations
    if (borrowerName?.isEmpty ?? true) {
      errors.add('Tên người vay chính là bắt buộc');
    }
    if (borrowerIdType?.isEmpty ?? true) {
      errors.add('Loại giấy tờ tùy thân người vay chính là bắt buộc');
    }
    if (borrowerIdNumber?.isEmpty ?? true) {
      errors.add('Số CCCD người vay chính là bắt buộc');
    }
    if (borrowerBirthDate?.isEmpty ?? true) {
      errors.add('Ngày sinh người vay chính là bắt buộc');
    }
    if (borrowerGender?.isEmpty ?? true) {
      errors.add('Giới tính người vay chính là bắt buộc');
    }
    if (borrowerPhone?.isEmpty ?? true) {
      errors.add('Số điện thoại người vay chính là bắt buộc');
    }
    if (borrowerPermanentProvinceId?.isEmpty ?? true) {
      errors.add('Tỉnh/thành phố thường trú là bắt buộc');
    }
    if (borrowerPermanentWardId?.isEmpty ?? true) {
      errors.add('Quận/huyện thường trú là bắt buộc');
    }
    if (borrowerPermanentAddress?.isEmpty ?? true) {
      errors.add('Địa chỉ thường trú là bắt buộc');
    }
    
    // Co-borrower validations
    if (hasCoBorrower) {
      if (coBorrowerName?.isEmpty ?? true) {
        errors.add('Tên người đồng vay là bắt buộc');
      }
      if (coBorrowerIdType?.isEmpty ?? true) {
        errors.add('Loại giấy tờ tùy thân người đồng vay là bắt buộc');
      }
      if (coBorrowerIdNumber?.isEmpty ?? true) {
        errors.add('Số CCCD người đồng vay là bắt buộc');
      }
      if (coBorrowerIdIssueDate?.isEmpty ?? true) {
        errors.add('Ngày cấp GTTT người đồng vay là bắt buộc');
      }
      if (coBorrowerIdExpiryDate?.isEmpty ?? true) {
        errors.add('Ngày hết hạn GTTT người đồng vay là bắt buộc');
      }
      if (coBorrowerIdIssuePlace?.isEmpty ?? true) {
        errors.add('Nơi cấp GTTT người đồng vay là bắt buộc');
      }
      if (coBorrowerBirthDate?.isEmpty ?? true) {
        errors.add('Ngày sinh người đồng vay là bắt buộc');
      }
      if (coBorrowerGender?.isEmpty ?? true) {
        errors.add('Giới tính người đồng vay là bắt buộc');
      }
      if (coBorrowerPhone?.isEmpty ?? true) {
        errors.add('Số điện thoại người đồng vay là bắt buộc');
      }
    }
    
    // Loan proposal validations
    if (ownCapital == null || ownCapital! < 0) {
      errors.add('Vốn tự có phải lớn hơn hoặc bằng 0');
    }
    if (loanAmount == null || loanAmount! < 1000000) {
      errors.add('Số tiền vay tối thiểu 1.000.000 VNĐ');
    }
    if (loanAmount != null && loanAmount! > 500000000) {
      errors.add('Số tiền vay tối đa 500.000.000 VNĐ');
    }
    if (loanTermId?.isEmpty ?? true) {
      errors.add('Thời hạn vay là bắt buộc');
    }
    if (loanMethodId?.isEmpty ?? true) {
      errors.add('Phương thức vay là bắt buộc');
    }
    if (loanPurposeId?.isEmpty ?? true) {
      errors.add('Mục đích vay là bắt buộc');
    }
    if (repaymentMethodId?.isEmpty ?? true) {
      errors.add('Hình thức trả nợ là bắt buộc');
    }
    
    // Collateral validations (for loan type with collateral)
    if (loanType == LoanType.withCollateral) {
      if (collateralValue == null || collateralValue! <= 0) {
        errors.add('Giá trị tài sản bảo đảm phải lớn hơn 0');
      }
      if (collateralTypeId?.isEmpty ?? true) {
        errors.add('Loại tài sản bảo đảm là bắt buộc');
      }
      if (collateralOwner?.isEmpty ?? true) {
        errors.add('Tên chủ sở hữu tài sản là bắt buộc');
      }
      if (collateralOwnerBirthYear?.isEmpty ?? true) {
        errors.add('Ngày tháng năm sinh của chủ tài sản là bắt buộc');
      }
      if (vehicleName?.isEmpty ?? true) {
        errors.add('Tên tài sản bảo đảm là bắt buộc');
      }
    }
    
    return errors;
  }

  // Document mapping constants
  static const Map<String, String> _documentMapping = {
    'gttt_nguoi_vay_chinh': 'mainBorrowerIdentityImages',
    'gttt_nguoi_dong_vay': 'coBorrowerIdentityImages',
    'giay_to_tinh_trang_hon_nhan': 'maritalRelationshipDocuments',
    'giay_to_chung_minh_noi_cu_tru': 'residenceProofDocuments',
    'to_trinh_tham_dinh_xe': 'motoAppraisalDocuments',
    'giay_dang_ky_xe': 'vehicleRegistrationDocuments',
  };
  
  static const Map<String, String> _documentNames = {
    'gttt_nguoi_vay_chinh': 'GTTT người vay chính',
    'gttt_nguoi_dong_vay': 'GTTT người đồng vay',
    'giay_to_tinh_trang_hon_nhan': 'Giấy tờ tình trạng hôn nhân, mối quan hệ',
    'giay_to_chung_minh_noi_cu_tru': 'Giấy tờ chứng minh nơi cư trú',
    'to_trinh_tham_dinh_xe': 'Tờ trình thẩm định xe mô tô, gắn máy',
    'giay_dang_ky_xe': 'Giấy đăng ký xe',
  };
  
  /// Get required documents based on current form state
  List<Map<String, String>> getRequiredDocuments() {
    final requiredDocs = <Map<String, String>>[];
    
    // Luôn cần GTTT người vay chính
    requiredDocs.add({
      'id': 'gttt_nguoi_vay_chinh',
      'name': _documentNames['gttt_nguoi_vay_chinh']!,
      'mappingKey': _documentMapping['gttt_nguoi_vay_chinh']!,
    });
    
    // Chỉ cần GTTT người đồng vay khi có co-borrower
    if (hasCoBorrower) {
      requiredDocs.add({
        'id': 'gttt_nguoi_dong_vay',
        'name': _documentNames['gttt_nguoi_dong_vay']!,
        'mappingKey': _documentMapping['gttt_nguoi_dong_vay']!,
      });
    }
    
    // Luôn cần giấy tờ hôn nhân và cư trú
    requiredDocs.add({
      'id': 'giay_to_tinh_trang_hon_nhan',
      'name': _documentNames['giay_to_tinh_trang_hon_nhan']!,
      'mappingKey': _documentMapping['giay_to_tinh_trang_hon_nhan']!,
    });
    requiredDocs.add({
      'id': 'giay_to_chung_minh_noi_cu_tru',
      'name': _documentNames['giay_to_chung_minh_noi_cu_tru']!,
      'mappingKey': _documentMapping['giay_to_chung_minh_noi_cu_tru']!,
    });
    
    // Chỉ cần tài liệu TSBĐ khi có thế chấp
    if (loanType?.displayName == 'Có TSBĐ') {
      requiredDocs.add({
        'id': 'to_trinh_tham_dinh_xe',
        'name': _documentNames['to_trinh_tham_dinh_xe']!,
        'mappingKey': _documentMapping['to_trinh_tham_dinh_xe']!,
      });
      requiredDocs.add({
        'id': 'giay_dang_ky_xe',
        'name': _documentNames['giay_dang_ky_xe']!,
        'mappingKey': _documentMapping['giay_dang_ky_xe']!,
      });
    }
    
    return requiredDocs;
  }
}
