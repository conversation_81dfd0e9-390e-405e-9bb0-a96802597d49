/// Models for transaction/proposal creation
class TransactionProposalRequest {
  final String? productId;
  final String? mode;
  final TransactionData? data;

  TransactionProposalRequest({
    this.productId,
    this.mode,
    this.data,
  });

  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      if (mode != null) 'mode': mode,
      'data': data?.toJson(),
    };
  }
}

class TransactionData {
  final String? customerId;
  final BorrowerInfo? mainBorrower;
  final BorrowerInfo? coBorrower;
  final LoanPlan? loanPlan;
  final FinancialInfo? financialInfo;
  final CollateralInfo? collateralInfo;
  final DocumentsInfo? documents;

  TransactionData({
    this.customerId,
    this.mainBorrower,
    this.coBorrower,
    this.loanPlan,
    this.financialInfo,
    this.collateralInfo,
    this.documents,
  });

  Map<String, dynamic> toJson() {
    return {
      'customerId': customerId,
      'mainBorrower': mainBorrower?.toJson(),
      if (coBorrower != null) 'coBorrower': coBorrower!.toJson(),
      'loanPlan': loanPlan?.toJson(),
      'financialInfo': financialInfo?.toJson(),
      'collateralInfo': collateralInfo?.toJson(),
      'documents': documents?.toJson(),
    };
  }
}

class BorrowerInfo {
  final String? fullName;
  final String? idNo;
  final String? issueDate;
  final String? expiryDate;
  final String? issuePlace;
  final String? dob;
  final String? sex;
  final String? permanentProvinceId;
  final String? permanentWardId;
  final String? permanentAddressDetail;
  final String? maritalStatusId;
  final String? phoneNumber;
  final String? currentProvinceId;
  final String? currentWardId;
  final String? currentAddressDetail;

  BorrowerInfo({
    this.fullName,
    this.idNo,
    this.issueDate,
    this.expiryDate,
    this.issuePlace,
    this.dob,
    this.sex,
    this.permanentProvinceId,
    this.permanentWardId,
    this.permanentAddressDetail,
    this.maritalStatusId,
    this.phoneNumber,
    this.currentProvinceId,
    this.currentWardId,
    this.currentAddressDetail,
  });

  Map<String, dynamic> toJson() {
    return {
      if (fullName != null) 'fullName': fullName,
      if (idNo != null) 'idNo': idNo,
      if (issueDate != null) 'issueDate': issueDate,
      if (expiryDate != null) 'expiryDate': expiryDate,
      if (issuePlace != null) 'issuePlace': issuePlace,
      if (dob != null) 'dob': dob,
      if (sex != null) 'sex': sex,
      if (permanentProvinceId != null) 'permanentProvinceId': permanentProvinceId,
      if (permanentWardId != null) 'permanentWardId': permanentWardId,
      if (permanentAddressDetail != null) 'permanentAddressDetail': permanentAddressDetail,
      if (maritalStatusId != null) 'maritalStatusId': maritalStatusId,
      if (phoneNumber != null) 'phoneNumber': phoneNumber,
      if (currentProvinceId != null) 'currentProvinceId': currentProvinceId,
      if (currentWardId != null) 'currentWardId': currentWardId,
      if (currentAddressDetail != null) 'currentAddressDetail': currentAddressDetail,
    };
  }
}

class LoanPlan {
  final int? ownCapital;
  final int? requestedAmount;
  final String? loanTermId;
  final String? loanMethodId;
  final String? loanPurposeId;
  final String? loanPurposeName;
  final String? repaymentMethodId;
  final String? disbursementMethodId;
  final String? receivingAccountNumber;

  LoanPlan({
    this.ownCapital,
    this.requestedAmount,
    this.loanTermId,
    this.loanMethodId,
    this.loanPurposeId,
    this.loanPurposeName,
    this.repaymentMethodId,
    this.disbursementMethodId,
    this.receivingAccountNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      if (ownCapital != null) 'ownCapital': ownCapital,
      if (requestedAmount != null) 'requestedAmount': requestedAmount,
      if (loanTermId != null) 'loanTermId': loanTermId,
      if (loanMethodId != null) 'loanMethodId': loanMethodId,
      if (loanPurposeId != null) 'loanPurposeId': loanPurposeId,
      if (loanPurposeName != null) 'loanPurposeName': loanPurposeName,
      if (repaymentMethodId != null) 'repaymentMethodId': repaymentMethodId,
      if (disbursementMethodId != null) 'disbursementMethodId': disbursementMethodId,
      if (receivingAccountNumber != null) 'receivingAccountNumber': receivingAccountNumber,
    };
  }
}

class FinancialInfo {
  final String? incomeSourceId;
  final int? averageRevenuePerDay;
  final int? averageIncomePerDay;
  final String? businessProvinceId;
  final String? businessWardId;
  final String? businessAddressDetail;

  FinancialInfo({
    this.incomeSourceId,
    this.averageRevenuePerDay,
    this.averageIncomePerDay,
    this.businessProvinceId,
    this.businessWardId,
    this.businessAddressDetail,
  });

  Map<String, dynamic> toJson() {
    return {
      if (incomeSourceId != null) 'incomeSourceId': incomeSourceId,
      if (averageRevenuePerDay != null) 'averageRevenuePerDay': averageRevenuePerDay,
      if (averageIncomePerDay != null) 'averageIncomePerDay': averageIncomePerDay,
      if (businessProvinceId != null) 'businessProvinceId': businessProvinceId,
      if (businessWardId != null) 'businessWardId': businessWardId,
      if (businessAddressDetail != null) 'businessAddressDetail': businessAddressDetail,
    };
  }
}

class CollateralInfo {
  final String? typeId;
  final int? value;
  final String? assetDescription;
  final String? assetLocation;
  final String? assetOwner;
  final String? condition;
  final String? ownerName;
  final String? ownerDob;
  final String? name;
  final String? licensePlate;
  final String? chassisNumber;
  final String? engineNumber;
  final String? registrationCertificateNumber;
  final String? registrationIssuePlace;
  final String? registrationIssueDate;
  final String? handoverConditionId;
  final int? totalValue;

  CollateralInfo({
    this.typeId,
    this.value,
    this.assetDescription,
    this.assetLocation,
    this.assetOwner,
    this.condition,
    this.ownerName,
    this.ownerDob,
    this.name,
    this.licensePlate,
    this.chassisNumber,
    this.engineNumber,
    this.registrationCertificateNumber,
    this.registrationIssuePlace,
    this.registrationIssueDate,
    this.handoverConditionId,
    this.totalValue,
  });

  Map<String, dynamic> toJson() {
    return {
      if (typeId != null) 'typeId': typeId,
      if (value != null) 'value': value,
      if (assetDescription != null) 'assetDescription': assetDescription,
      if (assetLocation != null) 'assetLocation': assetLocation,
      if (assetOwner != null) 'assetOwner': assetOwner,
      if (condition != null) 'condition': condition,
      if (ownerName != null) 'ownerName': ownerName,
      if (ownerDob != null) 'ownerDob': ownerDob,
      if (name != null) 'name': name,
      if (licensePlate != null) 'licensePlate': licensePlate,
      if (chassisNumber != null) 'chassisNumber': chassisNumber,
      if (engineNumber != null) 'engineNumber': engineNumber,
      if (registrationCertificateNumber != null) 'registrationCertificateNumber': registrationCertificateNumber,
      if (registrationIssuePlace != null) 'registrationIssuePlace': registrationIssuePlace,
      if (registrationIssueDate != null) 'registrationIssueDate': registrationIssueDate,
      if (handoverConditionId != null) 'handoverConditionId': handoverConditionId,
      if (totalValue != null) 'totalValue': totalValue,
    };
  }
}

class DocumentsInfo {
  final List<DocumentFile>? mainBorrowerIdentityImages;
  final List<DocumentFile>? coBorrowerIdentityImages;
  final List<DocumentFile>? maritalRelationshipDocuments;
  final List<DocumentFile>? residenceProofDocuments;
  final List<DocumentFile>? motoAppraisalDocuments;
  final List<DocumentFile>? vehicleRegistrationDocuments;
  final List<DocumentFile>? optionalBusinessCertificates;

  DocumentsInfo({
    this.mainBorrowerIdentityImages,
    this.coBorrowerIdentityImages,
    this.maritalRelationshipDocuments,
    this.residenceProofDocuments,
    this.motoAppraisalDocuments,
    this.vehicleRegistrationDocuments,
    this.optionalBusinessCertificates,
  });

  Map<String, dynamic> toJson() {
    return {
      if (mainBorrowerIdentityImages != null) 
        'mainBorrowerIdentityImages': mainBorrowerIdentityImages!.map((item) => item.toJson()).toList(),
      if (coBorrowerIdentityImages != null) 
        'coBorrowerIdentityImages': coBorrowerIdentityImages!.map((item) => item.toJson()).toList(),
      if (maritalRelationshipDocuments != null) 
        'maritalRelationshipDocuments': maritalRelationshipDocuments!.map((item) => item.toJson()).toList(),
      if (residenceProofDocuments != null) 
        'residenceProofDocuments': residenceProofDocuments!.map((item) => item.toJson()).toList(),
      if (motoAppraisalDocuments != null) 
        'motoAppraisalDocuments': motoAppraisalDocuments!.map((item) => item.toJson()).toList(),
      if (vehicleRegistrationDocuments != null) 
        'vehicleRegistrationDocuments': vehicleRegistrationDocuments!.map((item) => item.toJson()).toList(),
      if (optionalBusinessCertificates != null) 
        'optionalBusinessCertificates': optionalBusinessCertificates!.map((item) => item.toJson()).toList(),
    };
  }
}

class DocumentFile {
  final String? fileName;
  final int? fileSize;
  final String? mimeType;
  final String? fileUrl;

  DocumentFile({
    this.fileName,
    this.fileSize,
    this.mimeType,
    this.fileUrl,
  });

  factory DocumentFile.fromJson(Map<String, dynamic> json) {
    return DocumentFile(
      fileName: json['file_name'],
      fileSize: json['file_size'],
      mimeType: json['mime_type'],
      fileUrl: json['file_url'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (fileName != null) 'fileName': fileName,
      if (fileSize != null) 'fileSize': fileSize,
      if (mimeType != null) 'mimeType': mimeType,
      if (fileUrl != null) 'fileUrl': fileUrl,
    };
  }
}
