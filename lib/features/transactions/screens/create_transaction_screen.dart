import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../shared/blocs/file_upload_bloc.dart';
import '../widgets/index.dart';
import '../models/index.dart';
import '../blocs/index.dart';
import '../utils/product_form_validator.dart';
import '../utils/document_validator.dart';
import '../../products/index.dart';
import '../../customers/index.dart';
import '../../auth/blocs/master_data_bloc.dart';


class CreateTransactionScreen extends StatefulWidget {
  final CustomerModel? preselectedCustomer;
  final ProductModel? preselectedProduct;

  const CreateTransactionScreen({
    super.key,
    this.preselectedCustomer,
    this.preselectedProduct,
  });

  @override
  State<CreateTransactionScreen> createState() => _CreateTransactionScreenState();
}

class _CreateTransactionScreenState extends State<CreateTransactionScreen> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ProductBloc()..add(LoadProducts()),
        ),
        BlocProvider(
          create: (context) => CustomerListBloc(
            customerService: CustomerService(),
          ),
        ),
        BlocProvider(
          create: (context) => MasterDataBloc(),
        ),
        BlocProvider(
          create: (context) => FileUploadBloc(),
        ),
        BlocProvider(
          create: (context) => TransactionBloc(),
        ),
      ],
      child: _CreateTransactionScreenContent(
        preselectedCustomer: widget.preselectedCustomer,
        preselectedProduct: widget.preselectedProduct,
      ),
    );
  }
}

class _CreateTransactionScreenContent extends StatefulWidget {
  final CustomerModel? preselectedCustomer;
  final ProductModel? preselectedProduct;

  const _CreateTransactionScreenContent({
    this.preselectedCustomer,
    this.preselectedProduct,
  });

  @override
  State<_CreateTransactionScreenContent> createState() => _CreateTransactionScreenContentState();
}

class _CreateTransactionScreenContentState extends State<_CreateTransactionScreenContent> {
  final PageController _pageController = PageController();
  final ScrollController _stepScrollController = ScrollController();
  int _currentStep = 0;
  
  // Transaction data
  ProductModel? _selectedProduct;
  CustomerModel? _selectedCustomer;
  final Map<String, dynamic> _transactionDetails = <String, dynamic>{};
  final List<DocumentModel> _documents = <DocumentModel>[];
  final Map<String, List<DocumentModel>> _documentsMapping = <String, List<DocumentModel>>{};
  
  // VALIDATION STRATEGY:
  // - Chỉ validate CHI TIẾT khi nhấn "Xác nhận tạo" (step 4)
  // - Các step khác chỉ kiểm tra cơ bản để cho phép navigation và lưu nháp
  // - ProductFormValidator được sử dụng TẬP TRUNG tại đây thay vì phân tán

  final List<String> _stepTitles = [
    'Chọn sản phẩm',
    'Chọn khách hàng', 
    'Chi tiết sản phẩm',
    'Tài liệu yêu cầu',
    'Xem lại & Xác nhận',
  ];

  @override
  void initState() {
    super.initState();
    // Pre-fill data if provided
    if (widget.preselectedCustomer != null) {
      _selectedCustomer = widget.preselectedCustomer;
    }
    if (widget.preselectedProduct != null) {
      _selectedProduct = widget.preselectedProduct;
    }
    
    // Initial scroll to current step
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToCurrentStep();
    });
  }

  @override
  void dispose() {
    _pageController.dispose();
    _stepScrollController.dispose();
    super.dispose();
  }

  void _scrollToCurrentStep() {
    if (!_stepScrollController.hasClients) return;
    
    // Calculate position of current step
    // Each step (circle + text) takes about 80px width
    // Each connection line takes 40px width  
    // Total per step: 120px
    final stepWidth = 120.0;
    final targetPosition = _currentStep * stepWidth;
    
    // Get scroll view width to center the step
    final scrollViewWidth = MediaQuery.of(context).size.width - 32; // Account for padding
    final centerOffset = (scrollViewWidth / 2) - 40; // Center the step circle
    
    final scrollPosition = (targetPosition - centerOffset).clamp(
      0.0, 
      _stepScrollController.position.maxScrollExtent,
    );
    
    _stepScrollController.animateTo(
      scrollPosition,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Future<void> _nextStep() async {
    // Ẩn bàn phím trước khi chuyển step
    FocusScope.of(context).unfocus();
    
    // Validate current step before proceeding
    if (!(await _validateCurrentStep())) {
      return;
    }
    
    if (_currentStep < _stepTitles.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _goToStep(int stepIndex) {
    if (stepIndex >= 0 && stepIndex < _stepTitles.length) {
      setState(() {
        _currentStep = stepIndex;
      });
      _pageController.animateToPage(
        stepIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  void _jumpToStep(int step) {
    if (step >= 0 && step < _stepTitles.length) {
      setState(() {
        _currentStep = step;
      });
      _pageController.animateToPage(
        step,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      // Auto scroll to current step
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _scrollToCurrentStep();
      });
    }
  }

  bool _canProceedToNext() {
    switch (_currentStep) {
      case 0: // Product selection
        return _selectedProduct != null;
      case 1: // Customer selection
        return _selectedCustomer != null;
      case 2: // Product details
        // Cho phép tiếp tục nếu có dữ liệu cơ bản (để support lưu nháp)
        // Validation chi tiết sẽ được thực hiện khi nhấn "Xác nhận tạo"
        return _selectedProduct != null && _transactionDetails.isNotEmpty;
      case 3: // Documents
        return true; // Documents are optional initially
      case 4: // Review
        return true;
      default:
        return false;
    }
  }

  Future<bool> _validateCurrentStep() async {
    switch (_currentStep) {
      case 0: // Product selection
        return _selectedProduct != null;
      case 1: // Customer selection
        return _selectedCustomer != null;
      case 2: // Product details
        // Cho phép tiếp tục với validation cơ bản (để support lưu nháp)
        // Validation chi tiết sẽ được thực hiện khi nhấn "Xác nhận tạo"
        return _selectedProduct != null && _transactionDetails.isNotEmpty;
      case 3: // Documents
        return true; // Documents are optional initially
      case 4: // Review
        // Validate tất cả steps tại đây - CHỈ KHI NHẤN "XÁC NHẬN TẠO"
        return await _validateAllSteps();
      default:
        return false;
    }
  }

  /// Validate tất cả các steps và jump về step có lỗi đầu tiên
  /// CHỈ ĐƯỢC GỌI KHI NHẤN "XÁC NHẬN TẠO" - đúng nghiệp vụ
  /// Sử dụng ProductFormValidator với _transactionDetails Map để tương thích với typed form data
  Future<bool> _validateAllSteps() async {
    debugPrint('=== START: _validateAllSteps (FINAL VALIDATION) ===');

    // Step 0: Product selection
    if (_selectedProduct == null) {
      debugPrint('Validation failed at step 0: No product selected');
      _jumpToInvalidStep(0, 'Vui lòng chọn sản phẩm');
      return false;
    }

    // Step 1: Customer selection  
    if (_selectedCustomer == null) {
      debugPrint('Validation failed at step 1: No customer selected');
      _jumpToInvalidStep(1, 'Vui lòng chọn khách hàng');
      return false;
    }

    // Step 2: Product details - sử dụng ProductFormValidator
    if (_selectedProduct == null) {
      debugPrint('Validation failed at step 2: No product for details validation');
      _jumpToInvalidStep(2, 'Vui lòng chọn sản phẩm trước khi điền thông tin');
      return false;
    }

    // Validate product details using ProductFormValidator
    debugPrint('🔍 DEBUG: _transactionDetails keys: ${_transactionDetails.keys.toList()}');
    debugPrint('🔍 DEBUG: _transactionDetails data:');
    _transactionDetails.forEach((key, value) {
      debugPrint('  $key: $value (${value.runtimeType})');
    });
    
    // Debug: Check specific borrower_id_number field
    final borrowerIdNumber = _transactionDetails['borrower_id_number'];
    debugPrint('🔍 DEBUG: borrower_id_number specifically: "$borrowerIdNumber" (${borrowerIdNumber.runtimeType})');
    debugPrint('🔍 DEBUG: borrower_id_number isEmpty: ${borrowerIdNumber?.toString().isEmpty ?? true}');
    debugPrint('🔍 DEBUG: borrower_id_number isNotEmpty: ${borrowerIdNumber?.toString().isNotEmpty ?? false}');
    
    final validator = ProductFormValidator(
      formData: _transactionDetails,
      product: _selectedProduct,
      selectedCustomer: _selectedCustomer,
    );
    
    final validationErrors = validator.validateAll();
    if (validationErrors.isNotEmpty) {
      debugPrint('❌ Product details validation failed:');
      for (int i = 0; i < validationErrors.length; i++) {
        debugPrint('  ${i + 1}. ${validationErrors[i]}');
      }
      _jumpToInvalidStep(2, 'Vui lòng điền đầy đủ thông tin sản phẩm: ${validationErrors.first}');
      return false;
    }

    debugPrint('Product details validation passed');

    // Step 3: Documents - validate required documents
    debugPrint('🔍 Validating required documents...');
    debugPrint('🔍 Documents count: ${_documents.length}');
    debugPrint('🔍 Documents mapping keys: ${_documentsMapping.keys.toList()}');
    
    final documentValidator = DocumentValidator(
      documents: _documents,
      productCode: _selectedProduct?.code,
      productDetails: _transactionDetails,
    );
    
    final documentErrors = documentValidator.validateAll();
    if (documentErrors.isNotEmpty) {
      debugPrint('❌ Documents validation failed:');
      for (int i = 0; i < documentErrors.length; i++) {
        debugPrint('  ${i + 1}. ${documentErrors[i]}');
      }
      _jumpToInvalidStep(3, 'Vui lòng upload đầy đủ tài liệu bắt buộc: ${documentErrors.first}');
      return false;
    }

    debugPrint('Documents validation passed');

    debugPrint('All steps validation passed');
    debugPrint('=== END: _validateAllSteps ===');
    return true;
  }

  /// Jump về step có lỗi validation
  void _jumpToInvalidStep(int stepIndex, String errorMessage) {
    debugPrint('=== START: _jumpToInvalidStep to step $stepIndex ===');
    
    // Hiển thị thông báo lỗi
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(errorMessage),
        backgroundColor: AppColors.error,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Đi đến',
          textColor: Colors.white,
          onPressed: () {
            _goToStep(stepIndex);
          },
        ),
      ),
    );

    // Jump về step có lỗi
    _goToStep(stepIndex);
    
    debugPrint('=== END: _jumpToInvalidStep ===');
  }

  void _saveDraft() {
    // Kiểm tra xem có dữ liệu để lưu không
    if (!_canSaveDraft()) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Chưa có dữ liệu để lưu nháp'),
          backgroundColor: AppColors.warning,
        ),
      );
      return;
    }

    // Tạo proposal request với mode DRAFT
    final proposalRequest = _createDraftProposalRequest();
    
    // Gọi API để lưu nháp
    context.read<TransactionBloc>().add(CreateProposal(request: proposalRequest));
  }

  /// Kiểm tra có thể lưu nháp không - chỉ cho phép lưu khi có ít nhất một số dữ liệu
  bool _canSaveDraft() {
    // Phải có sản phẩm được chọn
    if (_selectedProduct == null) return false;
    
    // Phải có ít nhất một trong các điều kiện sau:
    // 1. Có khách hàng được chọn
    // 2. Có dữ liệu transaction details
    // 3. Có tài liệu được upload
    return _selectedCustomer != null || 
           _transactionDetails.isNotEmpty || 
           _documents.isNotEmpty ||
           _documentsMapping.isNotEmpty;
  }

  /// Tạo proposal request cho lưu nháp với mode DRAFT
  TransactionProposalRequest _createDraftProposalRequest() {
    final details = _transactionDetails;
    
    // Tạo main borrower chỉ với dữ liệu có sẵn từ _transactionDetails
    final mainBorrower = _createDraftBorrowerInfo(details, 'borrower', null);
    
    // Tạo co-borrower nếu có dữ liệu
    BorrowerInfo? coBorrower;
    if (details['has_co_borrower'] == true) {
      coBorrower = _createDraftBorrowerInfo(details, 'co_borrower', null);
    }
    
    // Tạo loan plan chỉ với dữ liệu có sẵn
    final loanPlan = _createDraftLoanPlan(details);
    
    // Tạo financial info chỉ nếu có dữ liệu
    final financialInfo = _createDraftFinancialInfo(details);
    
    // Tạo collateral info chỉ nếu có dữ liệu
    final collateralInfo = _createDraftCollateralInfo(details);
    
    // Tạo documents info chỉ nếu có dữ liệu
    final documentsInfo = _createDocumentsInfo();
    
    return TransactionProposalRequest(
      productId: _selectedProduct?.id,
      mode: "DRAFT", // Mode DRAFT cho lưu nháp
      data: TransactionData(
        customerId: _selectedCustomer?.id,
        mainBorrower: mainBorrower,
        coBorrower: coBorrower,
        loanPlan: loanPlan,
        financialInfo: financialInfo,
        collateralInfo: collateralInfo,
        documents: documentsInfo,
      ),
    );
  }

  /// Tạo BorrowerInfo cho draft - chỉ lấy dữ liệu có sẵn từ _transactionDetails, cho phép null
  BorrowerInfo? _createDraftBorrowerInfo(
    Map<String, dynamic> details,
    String prefix,
    CustomerModel? fallbackCustomer, // Không sử dụng fallback customer
  ) {
    // Kiểm tra có dữ liệu borrower trong _transactionDetails không
    final detailsName = details['${prefix}_name']?.toString();
    final hasName = detailsName != null && detailsName.isNotEmpty;
    
    if (!hasName) return null;
    
    return BorrowerInfo(
      fullName: details['${prefix}_name'],
      idNo: details['${prefix}_id_number'],
      issueDate: details['${prefix}_id_issue_date'],
      expiryDate: details['${prefix}_id_expiry_date'],
      issuePlace: details['${prefix}_id_issue_place'],
      dob: details['${prefix}_birth_date'],
      sex: details['${prefix}_gender'],
      permanentProvinceId: details['${prefix}_permanent_province'],
      permanentWardId: details['${prefix}_permanent_district'],
      permanentAddressDetail: details['${prefix}_permanent_address'],
      maritalStatusId: details['${prefix}_marital_status'],
      phoneNumber: details['${prefix}_phone'],
      currentProvinceId: details['${prefix}_current_same_permanent'] == true 
          ? details['${prefix}_permanent_province']
          : details['${prefix}_current_province'],
      currentWardId: details['${prefix}_current_same_permanent'] == true 
          ? details['${prefix}_permanent_district']
          : details['${prefix}_current_district'],
      currentAddressDetail: details['${prefix}_current_same_permanent'] == true 
          ? details['${prefix}_permanent_address']
          : details['${prefix}_current_address'],
    );
  }

  /// Tạo LoanPlan cho draft - chỉ lấy dữ liệu có sẵn
  LoanPlan? _createDraftLoanPlan(Map<String, dynamic> details) {
    // Kiểm tra có dữ liệu loan plan không
    final hasLoanData = details['loan_amount'] != null || 
                        details['own_capital'] != null ||
                        details['loan_purpose'] != null;
    
    if (!hasLoanData) return null;
    
    return LoanPlan(
      ownCapital: _parseAmount(details['own_capital'])?.toInt(),
      requestedAmount: _parseAmount(details['loan_amount'])?.toInt(),
      loanTermId: details['loan_term'],
      loanMethodId: details['loan_method'],
      loanPurposeId: details['loan_purpose'],
      loanPurposeName: details['loan_purpose'],
      repaymentMethodId: details['repayment_method'],
      disbursementMethodId: details['disbursement_method'],
      receivingAccountNumber: details['disbursement_account'],
    );
  }

  /// Tạo FinancialInfo cho draft - chỉ lấy dữ liệu có sẵn
  FinancialInfo? _createDraftFinancialInfo(Map<String, dynamic> details) {
    final hasFinancialData = details['income_source'] != null || 
                             details['daily_revenue'] != null ||
                             details['daily_income'] != null;
    
    if (!hasFinancialData) return null;
    
    return FinancialInfo(
      incomeSourceId: details['income_source'],
      averageRevenuePerDay: _parseAmount(details['daily_revenue'])?.toInt(),
      averageIncomePerDay: _parseAmount(details['daily_income'])?.toInt(),
      businessProvinceId: details['business_location_province'],
      businessWardId: details['business_location_district'],
      businessAddressDetail: details['business_location_address'],
    );
  }

  /// Tạo CollateralInfo cho draft - chỉ lấy dữ liệu có sẵn
  CollateralInfo? _createDraftCollateralInfo(Map<String, dynamic> details) {
    final loanType = details['loan_type'];
    final hasCollateral = loanType == 'Có TSBĐ';
    final collateralValue = _parseAmount(details['collateral_value']);
    
    if (!hasCollateral || collateralValue == null) {
      return null;
    }
    
    return CollateralInfo(
      typeId: details['collateral_type_id'],
      value: collateralValue.toInt(),
      condition: details['collateral_condition'],
      ownerName: details['collateral_owner'] ?? details['borrower_name'],
      ownerDob: details['collateral_owner_birth_year'] ?? details['borrower_birth_date'],
      name: details['vehicle_name'],
      licensePlate: details['vehicle_plate_number'],
      chassisNumber: details['vehicle_frame_number'],
      engineNumber: details['vehicle_engine_number'],
      registrationCertificateNumber: details['vehicle_registration_number'],
      registrationIssuePlace: details['vehicle_registration_place'],
      registrationIssueDate: details['vehicle_registration_date'],
      handoverConditionId: details['vehicle_condition_at_handover'],
      totalValue: _parseAmount(details['total_collateral_value'])?.toInt() ?? collateralValue.toInt(),
    );
  }


  /// Create proposal request from current data - use direct keys from _transactionDetails
  TransactionProposalRequest _createProposalRequest() {
    final details = _transactionDetails;
    
    // Debug: Log transaction details để kiểm tra dữ liệu
    debugPrint('=== TRANSACTION DETAILS DEBUG ===');
    debugPrint('Transaction details keys: ${details.keys.toList()}');
    debugPrint('Transaction details values:');
    details.forEach((key, value) {
      debugPrint('  $key: $value');
    });
    debugPrint('=== END TRANSACTION DETAILS DEBUG ===');
    
    // Create main borrower using helper - chỉ lấy từ _transactionDetails
    final mainBorrower = _createBorrowerInfo(details, 'borrower', null);
    
    // Create co-borrower if exists
    BorrowerInfo? coBorrower;
    if (details['has_co_borrower'] == true) {
      coBorrower = _createBorrowerInfo(details, 'co_borrower', null);
    }
    
    // Create loan plan using helper
    final loanPlan = _createLoanPlan(details, null, null, '');
    
    // Create financial info using helper
    final financialInfo = _createFinancialInfo(details);
    
    // Create collateral info using helper
    final collateralInfo = _createCollateralInfo(details);
    
    // Create documents info using helper  
    final documentsInfo = _createDocumentsInfo();
    
    return TransactionProposalRequest(
      productId: _selectedProduct?.id,
      mode: "FULL",
      data: TransactionData(
        customerId: _selectedCustomer?.id,
        mainBorrower: mainBorrower,
        coBorrower: coBorrower,
        loanPlan: loanPlan,
        financialInfo: financialInfo,
        collateralInfo: collateralInfo,
        documents: documentsInfo,
      ),
    );
  }

  /// Create BorrowerInfo from form data - chỉ lấy từ _transactionDetails, không fallback sang customer
  BorrowerInfo _createBorrowerInfo(
    Map<String, dynamic> details,
    String prefix, // 'borrower' or 'co_borrower'
    CustomerModel? fallbackCustomer, // Không sử dụng fallback customer
  ) {
    return BorrowerInfo(
      fullName: details['${prefix}_name'] ?? '',
      idNo: details['${prefix}_id_number'] ?? '',
      issueDate: details['${prefix}_id_issue_date'] ?? '',
      expiryDate: details['${prefix}_id_expiry_date'] ?? '',
      issuePlace: details['${prefix}_id_issue_place'] ?? '',
      dob: details['${prefix}_birth_date'] ?? '',
      sex: details['${prefix}_gender'] ?? '',
      permanentProvinceId: details['${prefix}_permanent_province'] ?? '',
      permanentWardId: details['${prefix}_permanent_district'] ?? '',
      permanentAddressDetail: details['${prefix}_permanent_address'] ?? '',
      maritalStatusId: details['${prefix}_marital_status'] ?? '',
      phoneNumber: details['${prefix}_phone'] ?? '',
      currentProvinceId: details['${prefix}_current_same_permanent'] == true 
          ? (details['${prefix}_permanent_province'] ?? '')
          : (details['${prefix}_current_province'] ?? ''),
      currentWardId: details['${prefix}_current_same_permanent'] == true 
          ? (details['${prefix}_permanent_district'] ?? '')
          : (details['${prefix}_current_district'] ?? ''),
      currentAddressDetail: details['${prefix}_current_same_permanent'] == true 
          ? (details['${prefix}_permanent_address'] ?? '')
          : (details['${prefix}_current_address'] ?? ''),
    );
  }


  /// Create LoanPlan from form data - use direct keys from _transactionDetails
  LoanPlan _createLoanPlan(
    Map<String, dynamic> details,
    double? loanAmount,
    double? ownCapital,
    String loanPurpose,
  ) {
    return LoanPlan(
      ownCapital: _parseAmount(details['own_capital'])?.toInt() ?? ownCapital?.toInt() ?? 0,
      requestedAmount: _parseAmount(details['loan_amount'])?.toInt() ?? loanAmount?.toInt() ?? 0,
      loanTermId: details['loan_term'] ?? '',
      loanMethodId: details['loan_method'] ?? '',
      loanPurposeId: details['loan_purpose'] ?? loanPurpose,
      loanPurposeName: details['loan_purpose'] ?? loanPurpose,
      repaymentMethodId: details['repayment_method'] ?? '',
      disbursementMethodId: details['disbursement_method'] ?? '',
      receivingAccountNumber: details['disbursement_account'] ?? '',
    );
  }

  /// Create FinancialInfo from form data - use direct keys from _transactionDetails
  FinancialInfo? _createFinancialInfo(Map<String, dynamic> details) {
    final incomeSource = details['income_source'] ?? '';
    final dailyRevenue = _parseAmount(details['daily_revenue']);
    final dailyIncome = _parseAmount(details['daily_income']);
    
    if (incomeSource.isEmpty && dailyRevenue == null && dailyIncome == null) {
      return null;
    }
    
    return FinancialInfo(
      incomeSourceId: incomeSource,
      averageRevenuePerDay: dailyRevenue?.toInt() ?? 0,
      averageIncomePerDay: dailyIncome?.toInt() ?? 0,
      businessProvinceId: details['business_location_province'] ?? '',
      businessWardId: details['business_location_district'] ?? '',
      businessAddressDetail: details['business_location_address'] ?? '',
    );
  }

  /// Create CollateralInfo from form data - use direct keys from _transactionDetails
  CollateralInfo? _createCollateralInfo(Map<String, dynamic> details) {
    final loanType = details['loan_type'];
    final hasCollateral = loanType == 'Có TSBĐ';
    final collateralValue = _parseAmount(details['collateral_value']);
    
    if (!hasCollateral || collateralValue == null) {
      return null;
    }
    
    return CollateralInfo(
      typeId: details['collateral_type_id'] ?? '',
      value: collateralValue.toInt(),
      condition: details['collateral_condition'] ?? '',
      ownerName: details['collateral_owner'] ?? details['borrower_name'] ?? '',
      ownerDob: details['collateral_owner_birth_year'] ?? details['borrower_birth_date'] ?? '',
      name: details['vehicle_name'] ?? '',
      licensePlate: details['vehicle_plate_number'] ?? '',
      chassisNumber: details['vehicle_frame_number'] ?? '',
      engineNumber: details['vehicle_engine_number'] ?? '',
      registrationCertificateNumber: details['vehicle_registration_number'] ?? '',
      registrationIssuePlace: details['vehicle_registration_place'] ?? '',
      registrationIssueDate: details['vehicle_registration_date'] ?? '',
      handoverConditionId: details['vehicle_condition_at_handover'] ?? '',
      totalValue: _parseAmount(details['total_collateral_value'])?.toInt() ?? collateralValue.toInt(),
    );
  }

  /// Create DocumentsInfo from uploaded documents using mapping data
  DocumentsInfo? _createDocumentsInfo() {
    if (_documentsMapping.isEmpty) return null;
    
    // Use mapping data directly from DocumentsStep - no complex parsing needed
    return DocumentsInfo(
      mainBorrowerIdentityImages: _getDocumentsByMappingKey('mainBorrowerIdentityImages'),
      coBorrowerIdentityImages: _getDocumentsByMappingKey('coBorrowerIdentityImages'),
      maritalRelationshipDocuments: _getDocumentsByMappingKey('maritalRelationshipDocuments'),
      residenceProofDocuments: _getDocumentsByMappingKey('residenceProofDocuments'),
      motoAppraisalDocuments: _getDocumentsByMappingKey('motoAppraisalDocuments'),
      vehicleRegistrationDocuments: _getDocumentsByMappingKey('vehicleRegistrationDocuments'),
      optionalBusinessCertificates: _getDocumentsByMappingKey('optionalBusinessCertificates'),
    );
  }

  /// Get documents by mapping key - use mapping data from DocumentsStep
  List<DocumentFile>? _getDocumentsByMappingKey(String mappingKey) {
    final documents = <DocumentFile>[];
    
    // Get documents from mapping data
    final mappedDocuments = _documentsMapping[mappingKey] ?? [];
    
    for (final doc in mappedDocuments) {
      if (!doc.hasFiles) continue;
      
      // Convert each uploaded file to DocumentFile
      for (final uploadedFile in doc.files) {
        documents.add(DocumentFile(
          fileName: uploadedFile.name,
          fileSize: uploadedFile.size,
          mimeType: _getMimeType(uploadedFile),
          fileUrl: uploadedFile.url ?? 'local://${uploadedFile.path}',
        ));
      }
    }
    
    return documents.isNotEmpty ? documents : null;
  }
  

  /// Get MIME type from uploaded file
  String _getMimeType(UploadedFile uploadedFile) {
    if (uploadedFile.isPdf) return 'application/pdf';
    if (uploadedFile.isImage) return 'image/jpeg';
    return 'application/octet-stream';
  }


  /// Parse amount from string to double
  double? _parseAmount(dynamic amount) {
    if (amount == null) return null;
    final cleanValue = amount.toString().replaceAll(RegExp(r'[^\d]'), '');
    return double.tryParse(cleanValue);
  }


  /// Handle confirm transaction
  Future<void> _confirmTransaction() async {
    debugPrint('=== START: _confirmTransaction ===');
    
    // Validate all steps before creating transaction
    if (!(await _validateAllSteps())) {
      debugPrint('Transaction creation cancelled due to validation failure');
      return;
    }
    
    debugPrint('All validation passed, creating proposal...');
    // Create proposal using bloc
    final proposalRequest = _createProposalRequest();
    if (mounted) {
      context.read<TransactionBloc>().add(CreateProposal(request: proposalRequest));
    }
    
    debugPrint('=== END: _confirmTransaction ===');
  }


  /// Show error dialog
  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusL),
        ),
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: AppColors.error,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _confirmTransaction(); // Retry
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Show snackbar when draft is saved successfully
  void _showDraftSavedSnackBar(BuildContext context, String proposalId) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã lưu nháp giao dịch: $proposalId'),
        backgroundColor: AppColors.success,
        duration: const Duration(seconds: 3),
        action: SnackBarAction(
          label: 'Xem danh sách',
          textColor: Colors.white,
          onPressed: () {
            // TODO: Navigate to drafts list
            // Navigator.pushNamed(context, '/transactions/drafts');
          },
        ),
      ),
    );
  }


  /// Build step content với error boundary
  Widget _buildStepContent(int index) {
    try {
      switch (index) {
        case 0:
          return ProductSelectionStep(
            selectedProduct: _selectedProduct,
            onProductSelected: (product) {
              setState(() {
                _selectedProduct = product;
                // Clear transaction details when product changes
                _transactionDetails.clear();
                _documents.clear();
                _documentsMapping.clear();
              });
            },
          );
        case 1:
          return CustomerSelectionStep(
            selectedCustomer: _selectedCustomer,
            preSelectedCustomer: widget.preselectedCustomer,
            onCustomerSelected: (customer) {
              setState(() {
                _selectedCustomer = customer;
              });
            },
          );
        case 2:
          return NewProductDetailsStepV3(
            product: _selectedProduct,
            selectedCustomer: _selectedCustomer,
            // onDetailsChanged: (details) {
            //   debugPrint('🔄 Transaction details changed: ${details.keys}');
            //   if (mounted) {
            //     setState(() {
            //       _transactionDetails.addAll(details);
            //     });
            //   }
            // },
          );
        case 3:
          return DocumentsStep(
            product: _selectedProduct?.code,
            productDetails: Map<String, dynamic>.from(_transactionDetails),
            documents: List<DocumentModel>.from(_documents),
            onDocumentsChanged: (documents) {
              if (mounted) {
                setState(() {
                  _documents.clear();
                  _documents.addAll(documents);
                });
              }
            },
            onDocumentsMappingChanged: (mapping) {
              if (mounted) {
                setState(() {
                  _documentsMapping.clear();
                  _documentsMapping.addAll(mapping);
                });
              }
            },
          );
        case 4:
          return ReviewConfirmStep(
            selectedProduct: _selectedProduct,
            selectedCustomer: _selectedCustomer,
            transactionDetails: Map<String, dynamic>.from(_transactionDetails),
            documents: List<DocumentModel>.from(_documents),
            onConfirm: null, // Let ReviewConfirmStep handle the bloc integration
            onEditCustomer: () => _goToStep(1), // Quay lại step chọn khách hàng
            onEditProduct: () => _goToStep(2), // Quay lại step chọn sản phẩm
            onEditDocuments: () => _goToStep(3), // Quay lại step tài liệu
          );
        default:
          return _buildErrorStep('Step không tồn tại');
      }
    } catch (e, stackTrace) {
      debugPrint('❌ Error building step $index: $e');
      debugPrint('Stack trace: $stackTrace');
      return _buildErrorStep('Lỗi tải step: $e');
    }
  }

  /// Build error step widget
  Widget _buildErrorStep(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_triangle,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // Force rebuild
                });
              },
              icon: Icon(TablerIcons.refresh),
              label: Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TransactionBloc, TransactionState>(
      listener: (context, state) {
        if (state is ProposalCreating) {
          // Show loading dialog
          showDialog(
            context: context,
            barrierDismissible: false,
            builder: (context) => const Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (state is ProposalCreated) {
          // Close loading dialog if open
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          
          // Kiểm tra xem đây là lưu nháp hay tạo giao dịch hoàn chỉnh
          if (_currentStep == _stepTitles.length - 1) {
            // Đây là tạo giao dịch hoàn chỉnh - chỉ pop về màn danh sách
            Navigator.of(context).pop(true);
          } else {
            // Đây là lưu nháp
            _showDraftSavedSnackBar(context, state.proposalId);
          }
        } else if (state is ProposalError) {
          // Close loading dialog if open
          if (Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
          // Show error dialog
          _showErrorDialog(context, state.message);
        }
      },
      child: Scaffold(
       appBar: AppNavHeader(
          title: 'Tạo giao dịch mới',
          showBackButton: true,
          actions: [
            InkWell(
              onTap: _canSaveDraft() ? _saveDraft : null,
              child: Text(
                _canSaveDraft() ? 'Tạo nhanh ' : '',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: _canSaveDraft()
                      ? Colors.white
                      : Colors.white.withValues(alpha: 0.5),
                ),
              ),
            ),
          ],
        ),
      body: GestureDetector(
        onTap: () {
          // Ẩn bàn phím khi tap ra ngoài
          FocusScope.of(context).unfocus();
        },
        child: Column(
          children: [
            // Step Indicator
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  bottom: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: _buildStepIndicator(),
            ),

            // Page Content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _stepTitles.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                  // Auto scroll to current step when user swipes
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    _scrollToCurrentStep();
                  });
                },
                itemBuilder: (context, index) {
                  return _buildStepContent(index);
                },
              ),
            ),

            // Navigation Controls
            Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: Theme.of(context).cardColor,
                border: Border(
                  top: BorderSide(
                    color: AppColors.borderLight,
                    width: 1,
                  ),
                ),
              ),
              child: SafeArea(
                child: _buildNavigationControls(),
              ),
            ),
          ],
        ),
      ),
      ),
    );
  }

  Widget _buildStepIndicator() {
    return SingleChildScrollView(
      controller: _stepScrollController,
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate(_stepTitles.length * 2 - 1, (index) {
          if (index % 2 == 0) {
            // Step circle
            final stepIndex = index ~/ 2;
            final isCompleted = stepIndex < _currentStep;
            final isCurrent = stepIndex == _currentStep;
            final isEnabled = stepIndex <= _currentStep || _canProceedToNext();

            return GestureDetector(
              onTap: isEnabled ? () => _jumpToStep(stepIndex) : null,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Step Circle
                  Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: isCompleted
                          ? AppColors.success
                          : isCurrent
                              ? AppColors.kienlongOrange
                              : AppColors.neutral300,
                      shape: BoxShape.circle,
                      boxShadow: isCurrent ? [
                        BoxShadow(
                          color: AppColors.kienlongOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Center(
                      child: isCompleted
                          ? Icon(
                              TablerIcons.check,
                              color: Colors.white,
                              size: 16,
                            )
                          : Text(
                              '${stepIndex + 1}',
                              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                    ),
                  ),
                  
                  SizedBox(height: AppDimensions.spacingS),
                  
                  // Step Title
                  SizedBox(
                    width: 80,
                    child: Text(
                      _stepTitles[stepIndex],
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isCurrent
                            ? AppColors.kienlongOrange
                            : isCompleted
                                ? AppColors.success
                                : AppColors.textSecondary,
                        fontWeight: isCurrent ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 11,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            );
          } else {
            // Connection line
            final lineIndex = index ~/ 2;
            final isLineCompleted = lineIndex < _currentStep;
            
            return Container(
              width: 40,
              height: 2,
              margin: EdgeInsets.only(top: 15), // Align with circle center (32/2 - 1)
              decoration: BoxDecoration(
                color: isLineCompleted
                    ? AppColors.success
                    : AppColors.neutral300,
                borderRadius: BorderRadius.circular(1),
              ),
            );
          }
        }),
      ),
    );
  }

  Widget _buildNavigationControls() {
    return Row(
      children: [
        // Previous Button
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _previousStep,
              icon: const Icon(TablerIcons.arrow_left),
              label: const Text('Quay lại'),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.kienlongSkyBlue,
                side: BorderSide(color: AppColors.kienlongSkyBlue),
                padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              ),
            ),
          ),
        
        if (_currentStep > 0)
          SizedBox(width: AppDimensions.spacingM),
        
        // Next/Confirm Button
        Expanded(
          flex: _currentStep == 0 ? 1 : 2,
          child: ElevatedButton.icon(
            onPressed: _canProceedToNext()
                ? (_currentStep == _stepTitles.length - 1
                    ? _confirmTransaction
                    : _nextStep)
                : null,
            icon: Icon(
              _currentStep == _stepTitles.length - 1
                  ? TablerIcons.check
                  : TablerIcons.arrow_right,
            ),
            label: Text(
              _currentStep == _stepTitles.length - 1
                  ? 'Xác nhận tạo'
                  : 'Tiếp tục',
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.kienlongOrange,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(vertical: AppDimensions.paddingM),
              disabledBackgroundColor: AppColors.neutral300,
            ),
          ),
        ),
      ],
    );
  }
} 