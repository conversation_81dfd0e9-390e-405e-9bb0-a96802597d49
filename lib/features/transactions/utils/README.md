# Product Form Management System

Hệ thống quản lý form động cho các loại sản phẩm khác nhau trong giao dịch.

## Tổng quan

Hệ thống này cho phép:
- <PERSON><PERSON><PERSON> nghĩa cấu hình form động cho từng loại sản phẩm
- Validation linh hoạt dựa trên cấu hình
- Quản lý dữ liệu form với type safety
- Hỗ trợ field dependencies và conditional visibility

## Các thành phần chính

### 1. ProductFormConfig
Định nghĩa cấu hình form cho từng loại sản phẩm:
- Field definitions với validation rules
- Default values
- Field groups và dependencies
- Required field groups

### 2. ProductFormFieldDefinition
Định nghĩa chi tiết cho từng field:
- Key, label, hint
- Validation rules và messages
- Field type (text, number, date, phone, etc.)
- Dependencies và group

### 3. ProductFormManager
Quản lý dữ liệu form với type safety:
- Dynamic field mapping
- Group-based data management
- Field visibility control
- Default value initialization

### 4. ProductFormValidator
Validation linh hoạt dựa trên cấu hình:
- Type-specific validation
- Dependency-based validation
- Group validation
- Visible field validation

## Cách sử dụng

### 1. Tạo ProductFormManager

```dart
// Tạo manager với product (tự động tạo config)
final manager = ProductFormManager(
  initialData: existingData,
  product: selectedProduct,
  selectedCustomer: selectedCustomer,
);

// Hoặc tạo với config tùy chỉnh
final config = ProductFormConfigFactory.createInstallmentLoanConfig();
final manager = ProductFormManager(
  initialData: existingData,
  product: selectedProduct,
  selectedCustomer: selectedCustomer,
  config: config,
);
```

### 2. Khởi tạo dữ liệu mặc định

```dart
// Khởi tạo với default values từ config
manager.initializeWithDefaults();
```

### 3. Làm việc với groups

```dart
// Lấy dữ liệu của một group
final borrowerData = manager.getGroupData('borrower_info');

// Set dữ liệu cho group
manager.setGroupData('borrower_info', {
  'borrower_name': 'Nguyễn Văn A',
  'borrower_phone': '0123456789',
});

// Kiểm tra group có dữ liệu không
final hasBorrowerData = manager.hasGroupData('borrower_info');
```

### 4. Làm việc với fields

```dart
// Lấy field definition
final fieldDef = manager.getFieldDefinition('borrower_name');

// Kiểm tra field có required không
final isRequired = manager.isFieldRequired('borrower_name');

// Kiểm tra field có visible không (dựa trên dependencies)
final isVisible = manager.isFieldVisible('co_borrower_name');

// Lấy validation message
final message = manager.getValidationMessage('borrower_name');
```

### 5. Validation

```dart
// Validate tất cả
final errors = manager.validate();

// Validate specific field
final fieldErrors = manager.validator.validateField('borrower_name');

// Validate specific group
final groupErrors = manager.validator.validateGroup('borrower_info');

// Validate chỉ visible fields
final visibleErrors = manager.validator.validateVisibleFields();

// Kiểm tra field valid
final isValid = manager.validator.isFieldValid('borrower_name');

// Kiểm tra group valid
final isGroupValid = manager.validator.isGroupValid('borrower_info');
```

### 6. Cập nhật product

```dart
// Tạo manager mới với product khác (tự động tạo config mới)
final newManager = manager.withProduct(newProduct);

// Tạo manager mới với customer khác
final newManager = manager.withCustomer(newCustomer);
```

## Các loại sản phẩm được hỗ trợ

### 1. Installment Loans (GOLD_LOAN, MANGO)
- Borrower information (required)
- Co-borrower information (optional)
- Loan proposal (required)
- Financial information (required)
- Collateral information (required)

### 2. Personal Loan (PERSONAL_LOAN)
- Basic loan information (amount, term, purpose, interest rate)

### 3. Mortgage Loan (MORTGAGE_LOAN)
- Basic loan information
- Property information (collateral type, value, address)

### 4. Auto Loan (AUTO_LOAN)
- Basic loan information
- Vehicle information (brand, model, year)

### 5. Business Loan (BUSINESS_LOAN)
- Basic loan information
- Business information (company name, type, revenue)

### 6. Education Loan (EDUCATION_LOAN)
- Basic loan information
- Education information (school, major, student ID)

### 7. Agriculture Loan (AGRICULTURE_LOAN)
- Basic loan information
- Agriculture information (crop type, farm size, season)

### 8. SME Loan (SME_LOAN)
- Basic loan information
- SME information (company, license, revenue, employees)

### 9. Revolving Credit (REVOLVING_CREDIT)
- Basic loan information
- Credit information (company, limit, usage)

## Field Types được hỗ trợ

- `text`: Text input
- `number`: Number input
- `date`: Date picker
- `phone`: Phone number input
- `idNumber`: ID number input
- `email`: Email input
- `dropdown`: Dropdown selection
- `checkbox`: Checkbox
- `radio`: Radio button
- `textarea`: Multi-line text
- `amount`: Amount input (with min/max validation)
- `percentage`: Percentage input (0-100)

## Field Dependencies

Fields có thể phụ thuộc vào các fields khác:

```dart
ProductFormFieldDefinition(
  key: 'co_borrower_name',
  label: 'Họ và tên người vay phụ',
  isRequired: true,
  dependencies: ['has_co_borrower'], // Chỉ hiển thị khi has_co_borrower = true
  group: 'co_borrower_info',
)
```

## Validation Rules

### Type-specific validation:
- **Phone**: 10-11 chữ số, bắt đầu bằng 0
- **ID Number**: 9 hoặc 12 chữ số
- **Email**: Format email hợp lệ
- **Amount**: Số dương, có thể có min/max từ metadata
- **Percentage**: 0-100%
- **Date**: Format DD/MM/YYYY hoặc ISO format

### Group validation:
- Required groups phải có ít nhất một field có dữ liệu
- Required fields trong group phải có dữ liệu hợp lệ

## Migration từ hệ thống cũ

### Trước:
```dart
// Sử dụng Map trực tiếp
final data = <String, dynamic>{};
data['borrower_name'] = 'Nguyễn Văn A';

// Validation thủ công
if (data['borrower_name']?.isEmpty ?? true) {
  errors.add('Vui lòng nhập họ và tên');
}
```

### Sau:
```dart
// Sử dụng ProductFormManager
final manager = ProductFormManager(
  product: selectedProduct,
  selectedCustomer: selectedCustomer,
);
manager.initializeWithDefaults();

// Set dữ liệu với type safety
manager.setFieldValue<String>('borrower_name', 'Nguyễn Văn A');

// Validation tự động
final errors = manager.validate();
```

## Lợi ích

1. **Type Safety**: Giảm lỗi runtime với type checking
2. **Dynamic Configuration**: Dễ dàng thêm/sửa sản phẩm mới
3. **Consistent Validation**: Validation rules nhất quán
4. **Better UX**: Field dependencies và conditional visibility
5. **Maintainable**: Code dễ bảo trì và mở rộng
6. **Reusable**: Có thể tái sử dụng cho các màn hình khác

## Tương lai

- Hỗ trợ thêm field types mới
- Custom validation rules
- Form builder UI
- Real-time validation
- Form state persistence