# TransactionFormValidator - Hướng dẫn sử dụng

## Tổng quan

`TransactionFormValidator` là validator chuyên dụng cho `create_transaction_screen_v2.dart`, được thiết kế để làm việc với:

- `TransactionFormBloc` với typed models
- `BaseFormData` và `InstallmentLoanFormData`
- Validation strategy riêng cho từng step
- Tích hợp với `DocumentValidator`

## Kiến trúc

### 1. Validation Strategy
- **Step validation**: Validate từng step riêng lẻ cho navigation
- **Full validation**: Validate toàn bộ form khi nhấn "Xác nhận tạo"
- **Draft validation**: Kiểm tra có thể lưu nháp không

### 2. Tích hợp với BaseFormData
```dart
// Sử dụng validation có sẵn từ BaseFormData
if (!formData!.isValid) {
  errors.addAll(formData!.validationErrors);
}
```

### 3. Product-specific Validation
- **InstallmentLoanFormData**: Validation cho sản phẩm "Vay trả góp ngày"
- **Business rules**: Kiểm tra logic nghiệp vụ cụ thể

## Cách sử dụng

### 1. Tạo Validator
```dart
final validator = TransactionFormValidator(
  selectedProduct: selectedProduct,
  selectedCustomer: selectedCustomer,
  formData: formData,
  documents: documents,
);
```

### 2. Validate toàn bộ form
```dart
final errors = validator.validateAll();
if (errors.isNotEmpty) {
  // Handle errors
  print('Validation errors: $errors');
}
```

### 3. Validate từng step
```dart
final stepErrors = validator.validateStep(stepIndex);
if (stepErrors.isEmpty) {
  // Allow navigation to next step
}
```

### 4. Kiểm tra có thể lưu nháp
```dart
if (validator.canSaveDraft()) {
  // Allow saving draft
}
```

## Validation Rules

### Product Selection (Step 0)
- Phải chọn sản phẩm

### Customer Selection (Step 1)
- Phải chọn khách hàng

### Product Details (Step 2)
- **Basic validation**: Tên người vay, số tiền vay
- **Full validation**: Tất cả trường bắt buộc theo SRS

### Documents (Step 3)
- Validate required documents theo product type
- Sử dụng `DocumentValidator`

### Review (Step 4)
- Full validation của tất cả steps

## InstallmentLoanFormData Specific Rules

### Loan Amount
- Tối thiểu: 1,000,000 VNĐ
- Tối đa: 500,000,000 VNĐ

### Collateral (khi có TSBĐ)
- Giá trị tài sản > 0
- Loại tài sản bắt buộc
- Tên tài sản bắt buộc

### Co-borrower (khi có)
- Tên, GTTT, số điện thoại bắt buộc

### Financial Information
- Nguồn thu chính bắt buộc
- Thu nhập bình quân/ngày > 0
- Doanh thu không được âm (nếu kinh doanh)

## Tích hợp với create_transaction_screen_v2.dart

### 1. Import
```dart
import '../utils/transaction_form_validator.dart';
```

### 2. Validate current step
```dart
Future<bool> _validateCurrentStep() async {
  final blocState = context.read<TransactionFormBloc>().state;
  if (blocState is! TransactionFormLoaded) return false;
  
  final validator = TransactionFormValidator(
    selectedProduct: blocState.selectedProduct,
    selectedCustomer: blocState.selectedCustomer,
    formData: blocState.formData,
    documents: _documents,
  );
  
  final stepErrors = validator.validateStep(_currentStep);
  return stepErrors.isEmpty;
}
```

### 3. Full validation
```dart
Future<bool> _validateAllSteps() async {
  final blocState = context.read<TransactionFormBloc>().state;
  if (blocState is! TransactionFormLoaded) return false;
  
  final validator = TransactionFormValidator(
    selectedProduct: blocState.selectedProduct,
    selectedCustomer: blocState.selectedCustomer,
    formData: blocState.formData,
    documents: _documents,
  );
  
  final validationErrors = validator.validateAll();
  if (validationErrors.isNotEmpty) {
    // Handle errors and jump to invalid step
    return false;
  }
  
  return true;
}
```

### 4. Check draft capability
```dart
bool _canSaveDraft() {
  final blocState = context.read<TransactionFormBloc>().state;
  if (blocState is! TransactionFormLoaded) return false;
  
  final validator = TransactionFormValidator(
    selectedProduct: blocState.selectedProduct,
    selectedCustomer: blocState.selectedCustomer,
    formData: blocState.formData,
    documents: _documents,
  );
  
  return validator.canSaveDraft();
}
```

## Lợi ích

1. **Type Safety**: Làm việc với typed models thay vì Map
2. **Separation of Concerns**: Tách biệt validation logic
3. **Reusability**: Có thể tái sử dụng cho các screen khác
4. **Maintainability**: Dễ bảo trì và mở rộng
5. **Performance**: Tận dụng validation có sẵn từ BaseFormData

## So sánh với ProductFormValidator

| Aspect | ProductFormValidator | TransactionFormValidator |
|--------|---------------------|-------------------------|
| Input | Map<String, dynamic> | Typed models (BaseFormData) |
| Usage | Legacy system | create_transaction_screen_v2.dart |
| Strategy | Single validation | Step-by-step + full validation |
| Integration | Manual parsing | Built-in BaseFormData validation |
| Performance | Slower (parsing Map) | Faster (direct property access) |

## Testing

Để test validator, sử dụng:

```dart
// Test với data thực tế
final validator = TransactionFormValidator(
  selectedProduct: mockProduct,
  selectedCustomer: mockCustomer,
  formData: mockInstallmentLoanFormData,
  documents: mockDocuments,
);

// Test validation
final errors = validator.validateAll();
expect(errors, isEmpty);

// Test step validation
final stepErrors = validator.validateStep(2);
expect(stepErrors, isEmpty);
```
