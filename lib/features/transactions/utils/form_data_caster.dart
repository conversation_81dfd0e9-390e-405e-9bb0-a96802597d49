import '../models/form_data/form_data.dart';

/// Utility class để cast BaseFormData sang các form cụ thể
class FormDataCaster {
  /// Cast BaseFormData sang InstallmentLoanFormData
  static InstallmentLoanFormData toInstallmentLoan(BaseFormData formData) {
    if (formData is InstallmentLoanFormData) {
      return formData;
    }
    throw TypeError();
  }
  
  /// Cast BaseFormData sang PersonalLoanFormData
  static PersonalLoanFormData toPersonalLoan(BaseFormData formData) {
    if (formData is PersonalLoanFormData) {
      return formData;
    }
    throw TypeError();
  }
  
  /// Cast BaseFormData sang form cụ thể dựa vào product code
  static T toTypedForm<T extends BaseFormData>(BaseFormData formData, String productCode) {
    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        if (T == InstallmentLoanFormData) {
          return toInstallmentLoan(formData) as T;
        }
        break;
      case 'PERSONAL_LOAN':
        if (T == PersonalLoanFormData) {
          return toPersonalLoan(formData) as T;
        }
        break;
    }
    throw TypeError();
  }
  
  /// Check if formData can be cast to specific type
  static bool canCastTo<T extends BaseFormData>(BaseFormData formData, String productCode) {
    try {
      toTypedForm<T>(formData, productCode);
      return true;
    } catch (e) {
      return false;
    }
  }
}
