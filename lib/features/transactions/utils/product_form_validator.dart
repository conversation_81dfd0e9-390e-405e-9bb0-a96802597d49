import 'package:flutter/foundation.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/document_model.dart';
import '../models/form_data/form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';

/// Helper class để validate form data cho giao dịch sản phẩm
/// Hỗ trợ cả Map và BaseFormData
class ProductFormValidator {
  final Map<String, dynamic> _formData;
  final ProductModel? _product;
  final CustomerModel? _selectedCustomer;

  ProductFormValidator({
    required Map<String, dynamic> formData,
    ProductModel? product,
    CustomerModel? selectedCustomer,
  }) : _formData = formData,
       _product = product,
       _selectedCustomer = selectedCustomer;

  /// Factory constructor để tạo validator từ BaseFormData
  factory ProductFormValidator.fromFormData({
    required BaseFormData formData,
    ProductModel? product,
    CustomerModel? selectedCustomer,
  }) {
    return ProductFormValidator(
      formData: formData.toMap(),
      product: product,
      selectedCustomer: selectedCustomer,
    );
  }

  /// Validate tất cả các trường bắt buộc
  List<String> validateAll() {
    final errors = <String>[];
    
    debugPrint('=== START: ProductFormValidator.validateAll() ===');
    debugPrint('📋 Form data keys: ${_formData.keys.toList()}');
    debugPrint('📋 Product: ${_product?.code}');
    debugPrint('📋 Customer: ${_selectedCustomer?.fullName}');
    
    // Validate product selection
    errors.addAll(_validateProductSelection());
    
    // Validate customer selection
    errors.addAll(_validateCustomerSelection());
    
    // Validate based on product type
    final productCode = _product?.code;
    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
      case 'Vay trả góp ngày':
        debugPrint('🔍 Validating InstallmentLoanFormData');
        errors.addAll(_validateInstallmentLoanForm());
        break;
      case 'PERSONAL_LOAN':
        debugPrint('🔍 Validating PersonalLoanFormData');
        errors.addAll(_validatePersonalLoanForm());
        break;
      default:
        debugPrint('🔍 Using legacy validation for unknown product: $productCode');
        errors.addAll(_validateBorrowerInfo());
        errors.addAll(_validateLoanInfo());
        errors.addAll(_validateFinancialInfo());
        errors.addAll(_validateCollateralInfo());
        errors.addAll(_validateCoBorrowerInfo());
    }
    
    debugPrint('❌ Total validation errors: ${errors.length}');
    for (int i = 0; i < errors.length; i++) {
      debugPrint('  ${i + 1}. ${errors[i]}');
    }
    debugPrint('=== END: ProductFormValidator.validateAll() ===');
    
    return errors;
  }



  /// Validate InstallmentLoanFormData (GOLD_LOAN, MANGO)
  List<String> _validateInstallmentLoanForm() {
    final errors = <String>[];
    
    // Borrower information
    errors.addAll(_validateBorrowerInfo());
    
    // Co-borrower information
    errors.addAll(_validateCoBorrowerInfo());
    
    // Loan proposal
    errors.addAll(_validateLoanProposal());
    
    // Financial information
    errors.addAll(_validateFinancialInfo());
    
    // Collateral information (if loan type has collateral)
    final loanType = _getString('loan_type');
    if (loanType == 'Có TSBĐ' || loanType == 'withCollateral') {
      errors.addAll(_validateCollateralInfo());
    }
    
    return errors;
  }

  /// Validate PersonalLoanFormData (PERSONAL_LOAN)
  List<String> _validatePersonalLoanForm() {
    final errors = <String>[];
    
    // Borrower information
    errors.addAll(_validateBorrowerInfo());
    
    // Co-borrower information
    errors.addAll(_validateCoBorrowerInfo());
    
    // Loan proposal (no collateral for personal loan)
    errors.addAll(_validateLoanProposal());
    
    // Financial information
    errors.addAll(_validateFinancialInfo());
    
    return errors;
  }

  /// Validate loan proposal information
  List<String> _validateLoanProposal() {
    final errors = <String>[];
    
    // Loan amount - Required, minimum 1 million, maximum 500 million
    final loanAmount = _getInt('loan_amount');
    if (loanAmount == null || loanAmount <= 0) {
      errors.add('Vui lòng nhập số tiền vay');
    } else if (loanAmount < 1000000) {
      errors.add('Số tiền vay tối thiểu là 1 triệu VNĐ');
    } else if (loanAmount > 500000000) {
      errors.add('Số tiền vay tối đa là 500 triệu VNĐ');
    }
    
    // Loan term - Required
    if (_getString('loan_term_id')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn thời hạn vay');
    }
    
    // Loan purpose - Required
    if (_getString('loan_purpose_id')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn mục đích sử dụng vốn');
    }
    
    // Loan type - Required
    if (_getString('loan_type')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn hình thức vay vốn');
    }
    
    return errors;
  }

  /// Validate product selection
  List<String> _validateProductSelection() {
    final errors = <String>[];
    
    if (_product == null) {
      errors.add('Vui lòng chọn sản phẩm');
    }
    
    return errors;
  }

  /// Validate customer selection
  List<String> _validateCustomerSelection() {
    final errors = <String>[];
    
    if (_selectedCustomer == null) {
      errors.add('Vui lòng chọn khách hàng');
    }
    
    return errors;
  }

  /// Validate borrower information (thông tin người vay chính)
  List<String> _validateBorrowerInfo() {
    final errors = <String>[];
    
    // Họ và tên - Bắt buộc
    if (_getString('borrower_name')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập họ và tên người vay chính');
    }
    
    // Số GTTT - Bắt buộc
    final idNumber = _getString('borrower_id_number');
    if (idNumber?.isEmpty ?? true) {
      errors.add('Vui lòng nhập số GTTT người vay chính');
    } else if (!_isValidIdNumber(idNumber!)) {
      errors.add('Số GTTT không hợp lệ (phải có 9 hoặc 12 chữ số)');
    }
    
    // Ngày cấp - Bắt buộc
    if (_getString('borrower_id_issue_date')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập ngày cấp GTTT');
    }
    
    // Ngày hết hạn - Bắt buộc
    if (_getString('borrower_id_expiry_date')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập ngày hết hạn GTTT');
    }
    
    // Nơi cấp - Bắt buộc
    if (_getString('borrower_id_issue_place')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập nơi cấp GTTT');
    }
    
    // Ngày sinh - Bắt buộc
    if (_getString('borrower_birth_date')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập ngày sinh người vay chính');
    }
    
    // Giới tính - Bắt buộc
    if (_getString('borrower_gender')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn giới tính người vay chính');
    }
    
    // Tỉnh/Thành phố thường trú - Bắt buộc
    if (_getString('borrower_permanent_province')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn tỉnh/thành phố thường trú');
    }
    
    // Phường/Xã thường trú - Bắt buộc
    if (_getString('borrower_permanent_district')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn phường/xã thường trú');
    }
    
    // Địa chỉ cụ thể thường trú - Bắt buộc
    if (_getString('borrower_permanent_address')?.isEmpty ?? true) {
      errors.add('Vui lòng nhập địa chỉ cụ thể thường trú');
    }
    
    // Tình trạng hôn nhân - Bắt buộc
    if (_getString('borrower_marital_status')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn tình trạng hôn nhân');
    }
    
    // Số điện thoại - Bắt buộc
    final phone = _getString('borrower_phone');
    if (phone?.isEmpty ?? true) {
      errors.add('Vui lòng nhập số điện thoại người vay chính');
    } else if (!_isValidPhoneNumber(phone!)) {
      errors.add('Số điện thoại không hợp lệ');
    }
    
    return errors;
  }

  /// Validate loan information (thông tin khoản vay)
  List<String> _validateLoanInfo() {
    final errors = <String>[];
    
    // Số tiền vay - Bắt buộc, tối thiểu 1 triệu, tối đa 500 triệu
    final loanAmount = _getDouble(_formData['loan_amount']);
    if (loanAmount == null || loanAmount <= 0) {
      errors.add('Vui lòng nhập số tiền vay');
    } else if (loanAmount < 1000000) {
      errors.add('Số tiền vay tối thiểu là 1 triệu VNĐ');
    } else if (loanAmount > 500000000) {
      errors.add('Số tiền vay tối đa là 500 triệu VNĐ');
    }
    
    // Thời hạn vay - Bắt buộc
    if (_getString('loan_term')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn thời hạn vay');
    }
    
    // Mục đích sử dụng vốn - Bắt buộc
    if (_getString('loan_purpose')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn mục đích sử dụng vốn');
    }
    
    // Hình thức vay vốn - Bắt buộc
    if (_getString('loan_type')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn hình thức vay vốn');
    }
    
    // Hình thức trả nợ - Bắt buộc
    if (_getString('repayment_method')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn hình thức trả nợ');
    }
    
    return errors;
  }

  /// Validate financial information (tình hình tài chính)
  List<String> _validateFinancialInfo() {
    final errors = <String>[];
    
    // Nguồn thu chính - Bắt buộc
    if (_getString('income_source_id')?.isEmpty ?? true) {
      errors.add('Vui lòng chọn nguồn thu chính');
    }
    
    // Thu nhập bình quân/ngày - Bắt buộc
    final dailyIncome = _getInt('daily_income');
    if (dailyIncome == null || dailyIncome <= 0) {
      errors.add('Vui lòng nhập thu nhập bình quân/ngày');
    }
    
    // Kiểm tra các trường kinh doanh nếu nguồn thu là kinh doanh
    final incomeSourceId = _getString('income_source_id');
    if (_isBusinessIncomeSource(incomeSourceId)) {
      // Doanh thu bình quân/ngày - Không bắt buộc nhưng nên có
      final dailyRevenue = _getInt('daily_revenue');
      if (dailyRevenue != null && dailyRevenue < 0) {
        errors.add('Doanh thu bình quân/ngày không được âm');
      }
      
      // Địa điểm kinh doanh - Không bắt buộc
      // (không cần validate vì đã optional)
    }
    
    return errors;
  }

  /// Validate collateral information (thông tin tài sản đảm bảo)
  List<String> _validateCollateralInfo() {
    final errors = <String>[];
    
    final loanType = _getString('loan_type');
    if (loanType == 'Có TSBĐ' || loanType == 'withCollateral') {
      // Loại tài sản đảm bảo - Bắt buộc khi có TSBĐ
      if (_getString('collateral_type_id')?.isEmpty ?? true) {
        errors.add('Vui lòng chọn loại tài sản đảm bảo');
      }
      
      // Giá trị tài sản đảm bảo - Bắt buộc khi có TSBĐ
      final collateralValue = _getInt('collateral_value');
      if (collateralValue == null || collateralValue <= 0) {
        errors.add('Vui lòng nhập giá trị tài sản đảm bảo');
      }
      
      // Tên tài sản - Bắt buộc khi có TSBĐ
      if (_getString('vehicle_name')?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên tài sản đảm bảo');
      }
      
      // Hiện trạng tài sản - Bắt buộc khi có TSBĐ
      if (_getString('collateral_condition_id')?.isEmpty ?? true) {
        errors.add('Vui lòng chọn hiện trạng tài sản');
      }
      
      // Chủ sở hữu tài sản - Bắt buộc khi có TSBĐ
      if (_getString('collateral_owner')?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên chủ sở hữu tài sản');
      }
    }
    
    return errors;
  }

  /// Validate co-borrower information (thông tin người vay phụ)
  List<String> _validateCoBorrowerInfo() {
    final errors = <String>[];
    
    final hasCoBorrower = _getBool('has_co_borrower');
    if (hasCoBorrower == true) {
      // Họ và tên người vay phụ - Bắt buộc khi có người vay phụ
      if (_getString('co_borrower_name')?.isEmpty ?? true) {
        errors.add('Vui lòng nhập họ và tên người vay phụ');
      }
      
      // Số GTTT người vay phụ - Bắt buộc khi có người vay phụ
      final coBorrowerIdNumber = _getString('co_borrower_id_number');
      if (coBorrowerIdNumber?.isEmpty ?? true) {
        errors.add('Vui lòng nhập số GTTT người vay phụ');
      } else if (!_isValidIdNumber(coBorrowerIdNumber!)) {
        errors.add('Số GTTT người vay phụ không hợp lệ');
      }
      
      // Số điện thoại người vay phụ - Bắt buộc khi có người vay phụ
      final coBorrowerPhone = _getString('co_borrower_phone');
      if (coBorrowerPhone?.isEmpty ?? true) {
        errors.add('Vui lòng nhập số điện thoại người vay phụ');
      } else if (!_isValidPhoneNumber(coBorrowerPhone!)) {
        errors.add('Số điện thoại người vay phụ không hợp lệ');
      }
    }
    
    return errors;
  }

  /// Check if income source is business-related
  bool _isBusinessIncomeSource(String? incomeSourceId) {
    if (incomeSourceId?.isEmpty ?? true) return false;
    
    // Check using known business income source patterns
    final lowerValue = incomeSourceId!.toLowerCase();
    return lowerValue.contains('kinh doanh') || 
           lowerValue.contains('business') ||
           lowerValue.contains('kinhdoanh') ||
           // Check for known business income source ID patterns
           incomeSourceId == '31a90b85-2954-47c7-889b-2a53f968b21e';
  }

  /// Validate documents (tài liệu)
  List<String> validateDocuments(List<DocumentModel> documents) {
    final errors = <String>[];
    
    // Kiểm tra tài liệu bắt buộc
    final requiredDocs = documents.where((doc) => doc.isRequired).toList();
    for (final doc in requiredDocs) {
      if (!doc.hasFiles) {
        errors.add('Vui lòng upload tài liệu: ${doc.name}');
      }
    }
    
    return errors;
  }

  /// Helper methods để lấy dữ liệu từ form data
  String? _getString(String key) => _formData[key]?.toString();
  
  int? _getInt(String key) {
    final value = _formData[key];
    if (value == null) return null;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanValue.isEmpty) return null;
      return int.tryParse(cleanValue);
    }
    return null;
  }
  
  double? _getDouble(dynamic value) {
    if (value == null) return null;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      final cleanValue = value.replaceAll(RegExp(r'[^\d.]'), '');
      if (cleanValue.isEmpty) return null;
      return double.tryParse(cleanValue);
    }
    return null;
  }
  
  bool? _getBool(String key) {
    final value = _formData[key];
    if (value is bool) return value;
    if (value is String) {
      return value.toLowerCase() == 'true';
    }
    return null;
  }

  /// Validate số GTTT (9 hoặc 12 chữ số)
  bool _isValidIdNumber(String idNumber) {
    debugPrint('🔍 ID validation for: "$idNumber"');
    
    // For testing/demo purposes, accept any non-empty string
    // In production, this should be stricter
    if (idNumber.trim().isEmpty) {
      debugPrint('❌ ID validation failed: empty string');
      return false;
    }
    
    // Accept any ID with reasonable length (for demo/testing)
    final isValid = idNumber.trim().length >= 3;
    debugPrint('🔍 ID validation result: $isValid');
    
    // TODO: Implement stricter validation for production
    // final cleanId = idNumber.replaceAll(RegExp(r'[^\d]'), '');
    // return cleanId.length == 9 || cleanId.length == 12;
    
    return isValid;
  }

  /// Validate số điện thoại (10-11 chữ số, bắt đầu bằng 0)
  bool _isValidPhoneNumber(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    return cleanPhone.length >= 10 && 
           cleanPhone.length <= 11 && 
           cleanPhone.startsWith('0');
  }


  /// Kiểm tra có thể lưu nháp không
  bool canSaveDraft() {
    return _product != null || 
           _selectedCustomer != null ||
           (_getString('borrower_name')?.isNotEmpty ?? false) ||
           (_getDouble(_formData['loan_amount']) ?? 0) > 0;
  }

  /// Kiểm tra form có hợp lệ không
  bool isValid() {
    return validateAll().isEmpty;
  }

}

/// Extension để dễ dàng sử dụng ProductFormValidator
extension ProductFormValidatorExtension on Map<String, dynamic> {
  ProductFormValidator validator({
    ProductModel? product,
    CustomerModel? selectedCustomer,
  }) {
    return ProductFormValidator(
      formData: this,
      product: product,
      selectedCustomer: selectedCustomer,
    );
  }
}

/// Extension để dễ dàng sử dụng ProductFormValidator với BaseFormData
extension BaseFormDataValidatorExtension on BaseFormData {
  ProductFormValidator validator({
    ProductModel? product,
    CustomerModel? selectedCustomer,
  }) {
    return ProductFormValidator.fromFormData(
      formData: this,
      product: product,
      selectedCustomer: selectedCustomer,
    );
  }
}
