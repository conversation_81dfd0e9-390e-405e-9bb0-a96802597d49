import 'package:flutter/foundation.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/document_model.dart';
import '../models/form_data/base_form_data.dart';
import '../models/form_data/installment_loan_form_data.dart';
import 'document_validator.dart';

/// TransactionFormValidator - Validator chuyên dụng cho create_transaction_screen_v2.dart
/// Tích hợp với TransactionFormBloc và typed form data models
class TransactionFormValidator {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final BaseFormData? formData;
  final List<DocumentModel> documents;

  TransactionFormValidator({
    this.selectedProduct,
    this.selectedCustomer,
    this.formData,
    this.documents = const [],
  });

  /// Validate toàn bộ form - CHỈ ĐƯỢC GỌI KHI NHẤN "XÁC NHẬN TẠO"
  List<String> validateAll() {
    final errors = <String>[];
    
    debugPrint('=== START: TransactionFormValidator.validateAll() ===');
    debugPrint('📋 Product: ${selectedProduct?.code}');
    debugPrint('📋 Customer: ${selectedCustomer?.fullName}');
    debugPrint('📋 FormData: ${formData?.runtimeType}');
    debugPrint('📋 Documents: ${documents.length}');
    
    // Step 1: Validate product selection
    errors.addAll(_validateProductSelection());
    
    // Step 2: Validate customer selection
    errors.addAll(_validateCustomerSelection());
    
    // Step 3: Validate form data based on product type
    if (selectedProduct != null && formData != null) {
      errors.addAll(_validateFormData());
    }
    
    // Step 4: Validate documents
    if (selectedProduct != null) {
      errors.addAll(_validateDocuments());
    }
    
    debugPrint('❌ Total validation errors: ${errors.length}');
    for (int i = 0; i < errors.length; i++) {
      debugPrint('  ${i + 1}. ${errors[i]}');
    }
    debugPrint('=== END: TransactionFormValidator.validateAll() ===');
    
    return errors;
  }

  /// Validate từng step riêng lẻ - dùng cho navigation giữa các steps
  List<String> validateStep(int stepIndex) {
    switch (stepIndex) {
      case 0: // Product selection
        return _validateProductSelection();
      case 1: // Customer selection
        return _validateCustomerSelection();
      case 2: // Product details
        return _validateFormDataBasic();
      case 3: // Documents
        return []; // Documents optional initially
      case 4: // Review
        return validateAll(); // Full validation
      default:
        return ['Step không hợp lệ'];
    }
  }

  /// Kiểm tra có thể lưu nháp không
  bool canSaveDraft() {
    // Phải có sản phẩm được chọn
    if (selectedProduct == null) return false;
    
    // Phải có ít nhất một trong các điều kiện sau:
    // 1. Có khách hàng được chọn
    // 2. Có dữ liệu form data
    // 3. Có tài liệu được upload
    return selectedCustomer != null || 
           (formData != null && _hasAnyFormData()) || 
           documents.isNotEmpty;
  }

  /// Kiểm tra form có hợp lệ hoàn toàn không
  bool isValid() {
    return validateAll().isEmpty;
  }

  // ===== PRIVATE VALIDATION METHODS =====

  /// Validate product selection
  List<String> _validateProductSelection() {
    final errors = <String>[];
    
    if (selectedProduct == null) {
      errors.add('Vui lòng chọn sản phẩm');
    }
    
    return errors;
  }

  /// Validate customer selection
  List<String> _validateCustomerSelection() {
    final errors = <String>[];
    
    if (selectedCustomer == null) {
      errors.add('Vui lòng chọn khách hàng');
    }
    
    return errors;
  }

  /// Validate form data - full validation
  List<String> _validateFormData() {
    final errors = <String>[];
    
    if (formData == null) {
      errors.add('Thông tin sản phẩm chưa được điền');
      return errors;
    }
    
    // Sử dụng validation có sẵn từ BaseFormData
    if (!formData!.isValid) {
      errors.addAll(formData!.validationErrors);
    }
    
    // Additional product-specific validations
    if (formData is InstallmentLoanFormData) {
      errors.addAll(_validateInstallmentLoanSpecific(formData as InstallmentLoanFormData));
    }
    
    return errors;
  }

  /// Validate form data - basic validation (for navigation)
  List<String> _validateFormDataBasic() {
    final errors = <String>[];
    
    if (formData == null) {
      errors.add('Thông tin sản phẩm chưa được điền');
      return errors;
    }
    
    // Chỉ validate các trường cơ bản nhất cho navigation
    if (formData is InstallmentLoanFormData) {
      final data = formData as InstallmentLoanFormData;
      
      // Chỉ validate tên người vay chính và số tiền vay
      if (data.borrowerName?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên người vay chính');
      }
      
      if (data.loanAmount == null || data.loanAmount! <= 0) {
        errors.add('Vui lòng nhập số tiền vay');
      }
    }
    
    return errors;
  }

  /// Validate InstallmentLoanFormData specific rules
  List<String> _validateInstallmentLoanSpecific(InstallmentLoanFormData data) {
    final errors = <String>[];
    
    // Validate loan amount range
    if (data.loanAmount != null) {
      if (data.loanAmount! < 1000000) {
        errors.add('Số tiền vay tối thiểu là 1 triệu VNĐ');
      } else if (data.loanAmount! > 500000000) {
        errors.add('Số tiền vay tối đa là 500 triệu VNĐ');
      }
    }
    
    // Validate collateral if loan type has collateral
    if (data.loanType?.displayName == 'Có TSBĐ') {
      if (data.collateralValue == null || data.collateralValue! <= 0) {
        errors.add('Vui lòng nhập giá trị tài sản bảo đảm');
      }
      
      if (data.collateralTypeId?.isEmpty ?? true) {
        errors.add('Vui lòng chọn loại tài sản bảo đảm');
      }
      
      if (data.collateralOwner?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên chủ sở hữu tài sản');
      }
      
      if (data.collateralOwnerBirthYear?.isEmpty ?? true) {
        errors.add('Vui lòng nhập ngày tháng năm sinh của chủ tài sản');
      }
      
      if (data.vehicleName?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên tài sản bảo đảm');
      }
    }
    
    // Validate co-borrower if exists
    if (data.hasCoBorrower) {
      if (data.coBorrowerName?.isEmpty ?? true) {
        errors.add('Vui lòng nhập tên người đồng vay');
      }
      
      if (data.coBorrowerIdNumber?.isEmpty ?? true) {
        errors.add('Vui lòng nhập số GTTT người đồng vay');
      }
      
      if (data.coBorrowerIdIssueDate?.isEmpty ?? true) {
        errors.add('Vui lòng nhập ngày cấp GTTT người đồng vay');
      }
      
      if (data.coBorrowerIdExpiryDate?.isEmpty ?? true) {
        errors.add('Vui lòng nhập ngày hết hạn GTTT người đồng vay');
      }
      
      if (data.coBorrowerIdIssuePlace?.isEmpty ?? true) {
        errors.add('Vui lòng nhập nơi cấp GTTT người đồng vay');
      }
      
      if (data.coBorrowerBirthDate?.isEmpty ?? true) {
        errors.add('Vui lòng nhập ngày sinh người đồng vay');
      }
      
      if (data.coBorrowerGender?.isEmpty ?? true) {
        errors.add('Vui lòng chọn giới tính người đồng vay');
      }
      
      if (data.coBorrowerPhone?.isEmpty ?? true) {
        errors.add('Vui lòng nhập số điện thoại người đồng vay');
      }
    }
    
    // Validate financial information
    if (data.incomeSourceId?.isEmpty ?? true) {
      errors.add('Vui lòng chọn nguồn thu chính');
    }
    
    if (data.dailyIncome == null || data.dailyIncome! <= 0) {
      errors.add('Vui lòng nhập thu nhập bình quân/ngày');
    }
    
    // Validate business income specific fields
    if (_isBusinessIncomeSource(data.incomeSourceId)) {
      if (data.dailyRevenue != null && data.dailyRevenue! < 0) {
        errors.add('Doanh thu bình quân/ngày không được âm');
      }
    }
    
    return errors;
  }

  /// Validate documents using DocumentValidator
  List<String> _validateDocuments() {
    if (selectedProduct == null) return [];
    
    final documentValidator = DocumentValidator(
      documents: documents,
      productCode: selectedProduct!.code,
      formData: formData,
    );
    
    return documentValidator.validateAll();
  }

  /// Check if income source is business-related
  bool _isBusinessIncomeSource(String? incomeSourceId) {
    if (incomeSourceId?.isEmpty ?? true) return false;
    
    final lowerValue = incomeSourceId!.toLowerCase();
    return lowerValue.contains('kinh doanh') || 
           lowerValue.contains('business') ||
           lowerValue.contains('kinhdoanh') ||
           incomeSourceId == '31a90b85-2954-47c7-889b-2a53f968b21e';
  }

  /// Check if form data has any meaningful content
  bool _hasAnyFormData() {
    if (formData == null) return false;
    
    if (formData is InstallmentLoanFormData) {
      final data = formData as InstallmentLoanFormData;
      return (data.borrowerName?.isNotEmpty ?? false) ||
             (data.loanAmount != null && data.loanAmount! > 0) ||
             (data.incomeSourceId?.isNotEmpty ?? false);
    }
    
    // Fallback - check if form data has any non-null values
    final map = formData!.toMap();
    return map.values.any((value) => value != null && value.toString().isNotEmpty);
  }
}

/// Extension để dễ dàng sử dụng TransactionFormValidator
extension TransactionFormValidatorExtension on TransactionFormValidator {
  /// Validate specific step
  List<String> validateStep(int stepIndex) => validateStep(stepIndex);
  
  /// Check if can proceed to next step
  bool canProceedToStep(int stepIndex) {
    return validateStep(stepIndex).isEmpty;
  }
}

/// Factory methods cho TransactionFormValidator
class TransactionFormValidatorFactory {
  /// Create validator for create_transaction_screen_v2.dart
  static TransactionFormValidator create({
    ProductModel? selectedProduct,
    CustomerModel? selectedCustomer,
    BaseFormData? formData,
    List<DocumentModel> documents = const [],
  }) {
    return TransactionFormValidator(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documents: documents,
    );
  }
  
  /// Create validator from TransactionFormBloc state
  static TransactionFormValidator fromBlocState({
    required dynamic blocState,
    List<DocumentModel> documents = const [],
  }) {
    // Extract data from bloc state (assuming TransactionFormLoaded state)
    ProductModel? selectedProduct;
    CustomerModel? selectedCustomer;
    BaseFormData? formData;
    
    try {
      // Use reflection or type checking to extract data
      // This is a simplified version - adjust based on actual bloc state structure
      if (blocState.toString().contains('TransactionFormLoaded')) {
        // Extract using dynamic access - adjust based on actual implementation
        selectedProduct = blocState.selectedProduct;
        selectedCustomer = blocState.selectedCustomer;
        formData = blocState.formData;
      }
    } catch (e) {
      debugPrint('Error extracting data from bloc state: $e');
    }
    
    return TransactionFormValidator(
      selectedProduct: selectedProduct,
      selectedCustomer: selectedCustomer,
      formData: formData,
      documents: documents,
    );
  }
}
