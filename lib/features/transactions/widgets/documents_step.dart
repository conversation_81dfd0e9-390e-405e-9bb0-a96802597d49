import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart' as picker;
import 'package:permission_handler/permission_handler.dart';
import 'dart:io';
import '../../../core/theme/index.dart';
import '../../../shared/blocs/file_upload_bloc.dart';
import '../../../shared/utils/app_logger.dart';
import '../models/index.dart';
import '../models/form_data/form_data.dart';

class DocumentsStep extends StatefulWidget {
  final String? product;
  final BaseFormData? formData; // Use typed form data
  final List<DocumentModel> documents;
  final Function(List<DocumentModel>) onDocumentsChanged;
  final Function(Map<String, List<DocumentModel>>)? onDocumentsMappingChanged; // New callback for mapping

  const DocumentsStep({
    super.key,
    required this.product,
    this.formData,
    required this.documents,
    required this.onDocumentsChanged,
    this.onDocumentsMappingChanged,
  });

  @override
  State<DocumentsStep> createState() => _DocumentsStepState();
}

class _DocumentsStepState extends State<DocumentsStep> 
    with AutomaticKeepAliveClientMixin {
  // ignore: prefer_final_fields
  List<DocumentModel> _requiredDocuments = [];
  // ignore: prefer_final_fields
  List<DocumentModel> _optionalDocuments = [];
  
  final ImagePicker _picker = ImagePicker();
  final AppLogger _logger = AppLogger();
  
  // Track upload progress for each document
  final Map<String, double> _uploadProgress = {};
  final Map<String, String> _uploadedDocumentIds = {};
  
  // Track file path to document ID mapping for recent uploads
  final Map<String, String> _filePathToDocumentId = {};
  
  // Track upload queue to prevent concurrent uploads
  final List<Map<String, dynamic>> _uploadQueue = [];
  bool _isUploading = false;
  
  // Track initialization to prevent unnecessary reinitialize
  String? _lastInitializedProduct;
  String? _lastInitializedLoanType;
  bool? _lastInitializedHasCoBorrower;
  String? _lastInitializedBorrowerIdType;

  @override
  void initState() {
    super.initState();
    // Defer initialization to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _initializeDocuments();
      }
    });
  }

  @override
  void didUpdateWidget(DocumentsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Count uploaded files
    final hasUploadedFiles = [..._requiredDocuments, ..._optionalDocuments]
        .any((doc) => doc.hasFiles);
    final uploadedFilesCount = [..._requiredDocuments, ..._optionalDocuments]
        .where((doc) => doc.hasFiles)
        .length;
    
    // Check if product actually changed
    final productChanged = widget.product != oldWidget.product;
    
    // Check if formData content actually changed (deep comparison)
    bool formDataChanged = false;
    if (widget.formData != null && oldWidget.formData != null) {
      // Only check key fields that affect document initialization
      if (widget.formData is InstallmentLoanFormData && oldWidget.formData is InstallmentLoanFormData) {
        final currentData = widget.formData as InstallmentLoanFormData;
        final oldData = oldWidget.formData as InstallmentLoanFormData;
        
        // Compare key fields that affect document structure
        formDataChanged = 
            currentData.loanType != oldData.loanType ||
            currentData.hasCoBorrower != oldData.hasCoBorrower ||
            currentData.borrowerIdType != oldData.borrowerIdType;
      }
    } else if (widget.formData != oldWidget.formData) {
      // One is null, the other is not
      formDataChanged = true;
    }
    
    // Special case: Force reinitialize if co-borrower status changed for GOLD_LOAN/MANGO
    bool forceCoBorrowerReinit = false;
    if (widget.product == 'GOLD_LOAN' || widget.product == 'MANGO') {
      final currentHasCoBorrower = _getHasCoBorrower();
      final oldHasCoBorrower = _getOldHasCoBorrower(oldWidget);
      if (currentHasCoBorrower != oldHasCoBorrower) {
        forceCoBorrowerReinit = true;
        formDataChanged = true;
        debugPrint('🔄 Co-borrower status changed: $oldHasCoBorrower -> $currentHasCoBorrower');
      }
    }
    
    // Decision logic for reinitializing
    final shouldReinitialize = (productChanged || formDataChanged);
    
    if (shouldReinitialize) {
      debugPrint('🔄 DocumentsStep - Reinitializing documents due to changes:');
      debugPrint('  - Product changed: $productChanged');
      debugPrint('  - FormData changed: $formDataChanged');
      debugPrint('  - Has uploaded files: $hasUploadedFiles ($uploadedFilesCount files)');
      debugPrint('  - Force co-borrower reinit: $forceCoBorrowerReinit');
      
      // Always reinitialize but preserve uploaded files via backup/restore mechanism
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _initializeDocuments();
        }
      });
    } else {
      debugPrint('🔄 DocumentsStep - Skipping reinitialize:');
      debugPrint('  - Product changed: $productChanged');
      debugPrint('  - FormData changed: $formDataChanged');
      debugPrint('  - Has uploaded files: $hasUploadedFiles ($uploadedFilesCount files)');
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// Get loan type from form data
  String? _getLoanType() {
    if (widget.formData is InstallmentLoanFormData) {
      final installmentData = widget.formData as InstallmentLoanFormData;
      return installmentData.loanType?.displayName;
    }
    return null;
  }

  /// Get has co-borrower status from form data
  bool _getHasCoBorrower() {
    if (widget.formData is InstallmentLoanFormData) {
      final installmentData = widget.formData as InstallmentLoanFormData;
      return installmentData.hasCoBorrower == true;
    }
    return false;
  }

  /// Get borrower ID type from form data
  String? _getBorrowerIdType() {
    if (widget.formData is InstallmentLoanFormData) {
      final installmentData = widget.formData as InstallmentLoanFormData;
      return installmentData.borrowerIdType;
    }
    return null;
  }

  /// Get old has co-borrower status for comparison
  bool _getOldHasCoBorrower(DocumentsStep oldWidget) {
    if (oldWidget.formData is InstallmentLoanFormData) {
      final installmentData = oldWidget.formData as InstallmentLoanFormData;
      return installmentData.hasCoBorrower == true;
    }
    return false;
  }

  void _initializeDocuments() {
    debugPrint('📋 Initializing documents for product: ${widget.product}');
    debugPrint('📋 FormData: ${widget.formData?.runtimeType}');
    if (widget.formData is InstallmentLoanFormData) {
      final data = widget.formData as InstallmentLoanFormData;
      debugPrint('📋 InstallmentLoanFormData - hasCoBorrower: ${data.hasCoBorrower}, loanType: ${data.loanType?.displayName}');
    }
    
    // Check if already initialized for this exact configuration
    final currentProduct = widget.product;
    final currentLoanType = _getLoanType();
    final currentHasCoBorrower = _getHasCoBorrower();
    final currentBorrowerIdType = _getBorrowerIdType();
    
    if (_lastInitializedProduct == currentProduct &&
        _lastInitializedLoanType == currentLoanType &&
        _lastInitializedHasCoBorrower == currentHasCoBorrower &&
        _lastInitializedBorrowerIdType == currentBorrowerIdType &&
        (_requiredDocuments.isNotEmpty || _optionalDocuments.isNotEmpty)) {
      debugPrint('📋 Documents already initialized for this configuration, skipping');
      return;
    }
    
    // Backup existing documents with files before clearing
    final existingDocumentsWithFiles = <String, DocumentModel>{};
    
    // First backup from current state
    for (final doc in [..._requiredDocuments, ..._optionalDocuments]) {
      if (doc.hasFiles) {
        existingDocumentsWithFiles[doc.id] = doc;
        debugPrint('📋 Backing up document with files from current state: ${doc.id} (${doc.files.length} files)');
      }
    }
    
    // Also backup from widget.documents (passed from parent)
    for (final doc in widget.documents) {
      if (doc.hasFiles && !existingDocumentsWithFiles.containsKey(doc.id)) {
        existingDocumentsWithFiles[doc.id] = doc;
        debugPrint('📋 Backing up document with files from widget: ${doc.id} (${doc.files.length} files)');
      }
    }
    
    // Clear existing documents
    _requiredDocuments.clear();
    _optionalDocuments.clear();
    
    // Special handling for co-borrower documents when co-borrower is disabled
    if (widget.product == 'GOLD_LOAN' || widget.product == 'MANGO') {
      final hasCoBorrower = _getHasCoBorrower();
      if (!hasCoBorrower) {
        debugPrint('📋 Co-borrower disabled, removing co-borrower documents if any');
        // This will be handled by clearing and re-adding documents below
      }
    }
    
    // Return early if no product selected
    if (widget.product == null || widget.product!.isEmpty) {
      debugPrint('📋 No product selected, skipping document initialization');
      _updateDocuments();
      return;
    }
    
    switch (widget.product) {
      case 'Vay tín chấp':
        _requiredDocuments.addAll([
          DocumentModel(
            id: 'cmnd_cccd_1',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'household_book_1',
            type: DocumentType.otherDoc,
            name: 'Sổ hộ khẩu',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'residenceProofDocuments',
          ),
          DocumentModel(
            id: 'income_proof_1',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'bank_statement_1',
            type: DocumentType.otherDoc,
            name: 'Sao kê ngân hàng',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;
      case 'Thẻ tín dụng':
        _requiredDocuments.addAll([
          DocumentModel(
            id: 'cmnd_cccd_2',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'income_proof_2',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'electric_bill_1',
            type: DocumentType.otherDoc,
            name: 'Hóa đơn điện/nước',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'residenceProofDocuments',
          ),
        ]);
        break;
      case 'Vay thế chấp':
        _requiredDocuments.addAll([
          DocumentModel(
            id: 'cmnd_cccd_3',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
          DocumentModel(
            id: 'household_book_2',
            type: DocumentType.otherDoc,
            name: 'Sổ hộ khẩu',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'residenceProofDocuments',
          ),
          DocumentModel(
            id: 'income_proof_3',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
          DocumentModel(
            id: 'property_papers_1',
            type: DocumentType.collateralDoc,
            name: 'Giấy tờ tài sản',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'location_map_1',
            type: DocumentType.otherDoc,
            name: 'Bản đồ vị trí',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;
      case 'Gửi tiết kiệm':
        _requiredDocuments.addAll([
          DocumentModel(
            id: 'cmnd_cccd_4',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
        ]);
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'income_proof_4',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
          DocumentModel(
            id: 'bank_statement_2',
            type: DocumentType.otherDoc,
            name: 'Sao kê ngân hàng',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;
      case 'GOLD_LOAN':
      case 'MANGO':
        // Use formData to get required documents if available
        if (widget.formData is InstallmentLoanFormData) {
          final installmentData = widget.formData as InstallmentLoanFormData;
          final requiredDocsData = installmentData.getRequiredDocuments();
          
          _requiredDocuments = requiredDocsData.map((docData) => DocumentModel(
            id: docData['id']!,
            type: DocumentType.otherDoc,
            name: docData['name']!,
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: docData['mappingKey']!,
          )).toList();
          
          debugPrint('📋 Using InstallmentLoanFormData - Total required: ${_requiredDocuments.length}');
          for (final doc in _requiredDocuments) {
            debugPrint('  - ${doc.id}: ${doc.name}');
          }
        } else {
          // Fallback to old logic if formData is not available
          _requiredDocuments.addAll([
            DocumentModel(
              id: 'gttt_nguoi_vay_chinh',
              type: DocumentType.idCard,
              name: 'GTTT người vay chính',
              isRequired: true,
              status: DocumentStatus.pending,
              files: [],
              productCode: widget.product,
              mappingKey: 'mainBorrowerIdentityImages',
            ),
          ]);
          
          // Chỉ thêm GTTT người đồng vay khi có chọn người đồng vay
          final hasCoBorrower = _getHasCoBorrower();
          if (hasCoBorrower) {
            _requiredDocuments.add(
              DocumentModel(
                id: 'gttt_nguoi_dong_vay',
                type: DocumentType.idCard,
                name: 'GTTT người đồng vay',
                isRequired: true,
                status: DocumentStatus.pending,
                files: [],
                productCode: widget.product,
                mappingKey: 'coBorrowerIdentityImages',
              ),
            );
          }
          
          _requiredDocuments.addAll([
            DocumentModel(
              id: 'giay_to_tinh_trang_hon_nhan',
              type: DocumentType.marriageCert,
              name: 'Giấy tờ tình trạng hôn nhân, mối quan hệ',
              isRequired: true,
              status: DocumentStatus.pending,
              files: [],
              productCode: widget.product,
              mappingKey: 'maritalRelationshipDocuments',
            ),
            DocumentModel(
              id: 'giay_to_chung_minh_noi_cu_tru',
              type: DocumentType.otherDoc,
              name: 'Giấy tờ chứng minh nơi cư trú',
              isRequired: true,
              status: DocumentStatus.pending,
              files: [],
              productCode: widget.product,
              mappingKey: 'residenceProofDocuments',
            ),
          ]);
          
          // Thêm tài liệu phụ thuộc vào hình thức vay (có TSBĐ hay không)
          final loanType = _getLoanType();
          debugPrint('🔍 DocumentsStep - Product: ${widget.product}');
          debugPrint('🔍 DocumentsStep - FormData: ${widget.formData?.runtimeType}');
          debugPrint('🔍 DocumentsStep - LoanType: $loanType');
          
          if (loanType == 'Có TSBĐ') {
            debugPrint('🔍 Adding TSBĐ documents for loanType: $loanType');
            _requiredDocuments.addAll([
              DocumentModel(
                id: 'to_trinh_tham_dinh_xe',
                type: DocumentType.otherDoc,
                name: 'Tờ trình thẩm định xe mô tô, gắn máy',
                isRequired: true,
                status: DocumentStatus.pending,
                files: [],
                productCode: widget.product,
                mappingKey: 'motoAppraisalDocuments',
              ),
              DocumentModel(
                id: 'giay_dang_ky_xe',
                type: DocumentType.otherDoc,
                name: 'Giấy đăng ký xe',
                isRequired: true,
                status: DocumentStatus.pending,
                files: [],
                productCode: widget.product,
                mappingKey: 'vehicleRegistrationDocuments',
              ),
            ]);
            debugPrint('📋 Added TSBĐ documents - Total required: ${_requiredDocuments.length}');
            debugPrint('📋 TSBĐ documents added:');
            for (final doc in _requiredDocuments.where((d) => d.id == 'to_trinh_tham_dinh_xe' || d.id == 'giay_dang_ky_xe')) {
              debugPrint('  - ${doc.id}: ${doc.name}');
            }
          } else {
            debugPrint('📋 No TSBĐ documents added - Total required: ${_requiredDocuments.length}');
            debugPrint('📋 LoanType was: $loanType (not Có TSBĐ)');
          }
        }
        
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'chung_nhan_ho_kinh_doanh',
            type: DocumentType.businessLicense,
            name: 'Chứng nhận hộ kinh doanh, khác (nếu có)',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
        break;
      default:
        _requiredDocuments.addAll([
          DocumentModel(
            id: 'cmnd_cccd_default',
            type: DocumentType.idCard,
            name: 'CMND/CCCD',
            isRequired: true,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'mainBorrowerIdentityImages',
          ),
        ]);
        _optionalDocuments.addAll([
          DocumentModel(
            id: 'income_proof_default',
            type: DocumentType.incomeCertificate,
            name: 'Chứng minh thu nhập',
            isRequired: false,
            status: DocumentStatus.pending,
            files: [],
            productCode: widget.product,
            mappingKey: 'optionalBusinessCertificates',
          ),
        ]);
    }
    
    // Restore documents with files if they match the new document structure
    _restoreDocumentsWithFiles(existingDocumentsWithFiles);
    
    // Update tracking variables to prevent unnecessary reinitialize
    _lastInitializedProduct = widget.product;
    _lastInitializedLoanType = _getLoanType();
    _lastInitializedHasCoBorrower = _getHasCoBorrower();
    _lastInitializedBorrowerIdType = _getBorrowerIdType();
    
    // Update UI and notify parent
    if (mounted) {
      setState(() {
        // Trigger rebuild with new documents
      });
      _updateDocuments();
    }
  }

  /// Restore documents with files from backup
  void _restoreDocumentsWithFiles(Map<String, DocumentModel> existingDocumentsWithFiles) {
    if (existingDocumentsWithFiles.isEmpty) {
      debugPrint('📋 No existing documents with files to restore');
      return;
    }
    
    debugPrint('📋 Restoring ${existingDocumentsWithFiles.length} documents with files');
    
    // Try to match and restore files for required documents
    for (int i = 0; i < _requiredDocuments.length; i++) {
      final newDoc = _requiredDocuments[i];
      final existingDoc = _findMatchingDocument(newDoc, existingDocumentsWithFiles);
      
      if (existingDoc != null) {
        debugPrint('📋 Restoring files for required document: ${newDoc.id} -> ${existingDoc.id}');
        _requiredDocuments[i] = newDoc.copyWith(
          files: existingDoc.files,
          status: existingDoc.status,
        );
        existingDocumentsWithFiles.remove(existingDoc.id);
      }
    }
    
    // Try to match and restore files for optional documents
    for (int i = 0; i < _optionalDocuments.length; i++) {
      final newDoc = _optionalDocuments[i];
      final existingDoc = _findMatchingDocument(newDoc, existingDocumentsWithFiles);
      
      if (existingDoc != null) {
        debugPrint('📋 Restoring files for optional document: ${newDoc.id} -> ${existingDoc.id}');
        _optionalDocuments[i] = newDoc.copyWith(
          files: existingDoc.files,
          status: existingDoc.status,
        );
        existingDocumentsWithFiles.remove(existingDoc.id);
      }
    }
    
    // Log any unmatched documents
    for (final unmatchedDoc in existingDocumentsWithFiles.values) {
      debugPrint('⚠️ Could not restore document: ${unmatchedDoc.id} (${unmatchedDoc.name})');
    }
  }
  
  /// Find matching document by type and mapping key
  DocumentModel? _findMatchingDocument(
    DocumentModel newDoc, 
    Map<String, DocumentModel> existingDocuments,
  ) {
    // First try exact ID match
    final exactMatch = existingDocuments[newDoc.id];
    if (exactMatch != null) {
      return exactMatch;
    }
    
    // Then try matching by type and mapping key
    for (final existingDoc in existingDocuments.values) {
      if (existingDoc.type == newDoc.type && 
          existingDoc.mappingKey == newDoc.mappingKey &&
          existingDoc.productCode == newDoc.productCode) {
        return existingDoc;
      }
    }
    
    // Special matching for similar document types (e.g., identity documents)
    for (final existingDoc in existingDocuments.values) {
      // Match identity documents regardless of specific naming
      if (newDoc.type == DocumentType.idCard && 
          existingDoc.type == DocumentType.idCard &&
          newDoc.productCode == existingDoc.productCode) {
        // Check if it's the same category (main borrower vs co-borrower)
        final newIsMainBorrower = newDoc.mappingKey == 'mainBorrowerIdentityImages' || 
                                  newDoc.name.contains('người vay chính');
        final existingIsMainBorrower = existingDoc.mappingKey == 'mainBorrowerIdentityImages' || 
                                       existingDoc.name.contains('người vay chính');
        
        if (newIsMainBorrower == existingIsMainBorrower) {
          return existingDoc;
        }
      }
    }
    
    // Finally try matching by name similarity and product code
    for (final existingDoc in existingDocuments.values) {
      if (_isDocumentNameSimilar(newDoc.name, existingDoc.name) && 
          existingDoc.productCode == newDoc.productCode) {
        return existingDoc;
      }
    }
    
    return null;
  }
  
  /// Check if two document names are similar (for matching purposes)
  bool _isDocumentNameSimilar(String name1, String name2) {
    // Normalize names by removing common variations
    final normalized1 = name1.toLowerCase()
        .replaceAll('gttt', 'cmnd/cccd')
        .replaceAll('giấy tờ tùy thân', 'cmnd/cccd')
        .replaceAll('người vay chính', '')
        .replaceAll('người đồng vay', '')
        .trim();
    
    final normalized2 = name2.toLowerCase()
        .replaceAll('gttt', 'cmnd/cccd')
        .replaceAll('giấy tờ tùy thân', 'cmnd/cccd')
        .replaceAll('người vay chính', '')
        .replaceAll('người đồng vay', '')
        .trim();
    
    return normalized1 == normalized2;
  }


  void _updateDocuments() {
    final allDocs = [..._requiredDocuments, ..._optionalDocuments];
    debugPrint('📋 Updating documents: ${allDocs.length} total documents');
    
    // Update parent immediately
    widget.onDocumentsChanged(allDocs);
    
    // Also send mapping information if callback is provided
    if (widget.onDocumentsMappingChanged != null) {
      final mapping = _createDocumentsMapping(allDocs);
      widget.onDocumentsMappingChanged!(mapping);
    }
  }

  /// Create documents mapping by grouping documents by mappingKey
  Map<String, List<DocumentModel>> _createDocumentsMapping(List<DocumentModel> documents) {
    final mapping = <String, List<DocumentModel>>{};
    
    for (final doc in documents) {
      if (doc.mappingKey != null) {
        if (!mapping.containsKey(doc.mappingKey)) {
          mapping[doc.mappingKey!] = [];
        }
        mapping[doc.mappingKey!]!.add(doc);
      }
    }
    
    debugPrint('📋 Created documents mapping: ${mapping.keys.join(', ')}');
    return mapping;
  }


  Widget _buildCompletionStatus(int uploaded, int total) {
    final percentage = total > 0 ? (uploaded / total) : 0.0;
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: AppColors.kienlongOrange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.kienlongOrange.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: AppColors.kienlongOrange,
              shape: BoxShape.circle,
            ),
            child: Icon(
              TablerIcons.file_text,
              color: Colors.white,
              size: 16,
            ),
          ),
          SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Tiến độ tài liệu',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.kienlongOrange,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXS),
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: percentage,
                        backgroundColor: AppColors.neutral200,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingS),
                    Text(
                      '$uploaded/$total',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppColors.kienlongOrange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    debugPrint('🏗️ DocumentsStep build() called');
    return BlocListener<FileUploadBloc, FileUploadState>(
      listener: _handleFileUploadState,
      child: _buildContent(),
    );
  }
  
  Widget _buildContent() {
    // Show loading state if documents are not initialized yet
    if (_requiredDocuments.isEmpty && _optionalDocuments.isEmpty && widget.product != null) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: AppColors.kienlongOrange,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                'Đang tải danh sách tài liệu...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }
    
    if (widget.product == null) {
      return _buildNoProductSelected();
    }

    final totalDocuments = _requiredDocuments.length + _optionalDocuments.length;
    final uploadedDocuments = [..._requiredDocuments, ..._optionalDocuments]
        .where((doc) => doc.hasFiles)
        .length;
    
    debugPrint('📊 DocumentsStep UI - Required: $uploadedDocuments/${_requiredDocuments.length}, Optional: ${_optionalDocuments.where((d) => d.hasFiles).length}/${_optionalDocuments.length}, Total uploaded: $uploadedDocuments');
    debugPrint('📊 Required documents list:');
    for (int i = 0; i < _requiredDocuments.length; i++) {
      final doc = _requiredDocuments[i];
      debugPrint('  ${i + 1}. ${doc.id}: ${doc.name} (${doc.hasFiles ? 'has files' : 'no files'})');
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Completion Status
          _buildCompletionStatus(uploadedDocuments, totalDocuments),
          SizedBox(height: AppDimensions.spacingL),

          // Required Documents
          if (_requiredDocuments.isNotEmpty) ...[
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 500),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: child,
                  ),
                );
              },
              child: _buildDocumentSection(
              'Tài liệu bắt buộc',
              _requiredDocuments,
              TablerIcons.alert_circle,
              AppColors.error,
              true,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Optional Documents
          if (_optionalDocuments.isNotEmpty) ...[
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 600),
              tween: Tween(begin: 0.0, end: 1.0),
              builder: (context, value, child) {
                return Transform.translate(
                  offset: Offset(0, 30 * (1 - value)),
                  child: Opacity(
                    opacity: value,
                    child: child,
                  ),
                );
              },
              child: _buildDocumentSection(
              'Tài liệu tùy chọn',
              _optionalDocuments,
              TablerIcons.info_circle,
              AppColors.info,
              false,
              ),
            ),
            SizedBox(height: AppDimensions.spacingL),
          ],

          // Note
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.kienlongSkyBlue.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(
                color: AppColors.kienlongSkyBlue.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: AppColors.kienlongSkyBlue,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Lưu ý quan trọng:',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          color: AppColors.kienlongSkyBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: AppDimensions.spacingXS),
                      Text(
                        '• Có thể upload tài liệu sau khi tạo giao dịch\n'
                        '• Hồ sơ sẽ được xử lý khi đủ tài liệu bắt buộc\n'
                        '• Chất lượng ảnh rõ nét, đầy đủ thông tin',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.kienlongSkyBlue,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoProductSelected() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.file_text,
              size: 64,
              color: AppColors.neutral400,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Chưa chọn sản phẩm',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Danh sách tài liệu sẽ hiển thị sau khi chọn sản phẩm',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textTertiary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDocumentSection(
    String title,
    List<DocumentModel> documents,
    IconData titleIcon,
    Color titleColor,
    bool isRequired,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: titleColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(titleIcon, color: titleColor, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: titleColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isRequired ? AppColors.error : AppColors.neutral500,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    isRequired ? 'BẮT BUỘC' : 'TÙY CHỌN',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                      fontSize: 10,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Documents List
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              children: documents.map((doc) => _buildDocumentItem(doc)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentItem(DocumentModel doc) {
    
    return Container(
      margin: EdgeInsets.only(bottom: AppDimensions.spacingL),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Document Header
          Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: AppColors.neutral400,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Container(
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              ),
              SizedBox(width: AppDimensions.spacingS),
              Expanded(
                child: Text(
                  doc.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
              Text(
                doc.status.displayName,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: AppColors.warning,
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          
          SizedBox(height: AppDimensions.spacingM),
          
          // Upload Area
          _buildUploadArea(doc),
          
          // File List (if any)
          AnimatedSize(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
            child: doc.hasFiles 
                ? Column(
                    children: [
                      SizedBox(height: AppDimensions.spacingM),
                      _buildFileList(doc),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadArea(DocumentModel doc) {
    
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.neutral300,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        color: Colors.white,
      ),
      child: InkWell(
        onTap: () => _showUploadOptions(doc),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
        child: Container(
          width: double.infinity,
          padding: EdgeInsets.all(AppDimensions.paddingL),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                TablerIcons.arrow_up,
                size: 40,
                color: AppColors.neutral500,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                'Chạm để tải lên',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.neutral600,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingXS),
              Text(
                'PNG, JPG, PDF tối đa 5MB',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.textTertiary,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileList(DocumentModel doc) {
    if (doc.files.isEmpty) return const SizedBox.shrink();
    
    return SizedBox(
      height: 200, // Fixed height for horizontal scroll
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: doc.files.map((file) => _buildFileItem(file, doc)).toList(),
        ),
      ),
    );
  }
  
  Widget _buildFileItem(UploadedFile file, DocumentModel doc) {
    return Container(
      width: 160, // Fixed width for horizontal layout
      margin: EdgeInsets.only(right: AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: AppColors.borderLight,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // File preview (for images)
          Expanded(
            child: Container(
              width: double.infinity,
              margin: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(
                  color: AppColors.borderLight,
                  width: 1,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                child: Stack(
                  children: [
                    // Image or file icon
                    if (file.isImage && file.localFile != null && file.localFile!.existsSync())
                      GestureDetector(
                        onTap: () => _viewFile(file),
                        child: Image.file(
                          file.localFile!,
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: AppColors.neutral100,
                              child: Center(
                                child: Icon(
                                  TablerIcons.photo,
                                  color: AppColors.neutral400,
                                  size: 32,
                                ),
                              ),
                            );
                          },
                        ),
                      )
                    else
                      GestureDetector(
                        onTap: () => _viewFile(file),
                        child: Container(
                          color: AppColors.neutral100,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _getFileIcon(file.name),
                                  color: AppColors.neutral400,
                                  size: 32,
                                ),
                                SizedBox(height: 4),
                                Text(
                                  file.name.length > 15 
                                      ? '${file.name.substring(0, 15)}...'
                                      : file.name,
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppColors.textSecondary,
                                    fontSize: 10,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    
                    // Success indicator
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.success,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          TablerIcons.check,
                          size: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                    
                    // Tap indicator for images
                    if (file.isImage && file.localFile != null && file.localFile!.existsSync())
                      Positioned(
                        bottom: 4,
                        left: 4,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.black.withValues(alpha: 0.7),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                TablerIcons.eye,
                                size: 10,
                                color: Colors.white,
                              ),
                              SizedBox(width: 2),
                              Text(
                                'Xem',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontSize: 9,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          
          // File info and actions
          Padding(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            child: Column(
              children: [
                // File name
                Text(
                  file.name,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 4),
                
                // File size and time
                Text(
                  '${file.sizeFormatted} • ${_formatTime(file.uploadedAt)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                    fontSize: 10,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: AppDimensions.spacingS),
                
                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    IconButton(
                      onPressed: () => _viewFile(file),
                      icon: Icon(
                        TablerIcons.eye,
                        color: AppColors.kienlongSkyBlue,
                        size: 16,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                      tooltip: 'Xem file',
                    ),
                    IconButton(
                      onPressed: () => _deleteFile(doc, file),
                      icon: Icon(
                        TablerIcons.trash,
                        color: AppColors.error,
                        size: 16,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                      padding: EdgeInsets.zero,
                      tooltip: 'Xóa file',
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }


  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _viewFile(UploadedFile file) {
    if (file.isImage && file.localFile != null && file.localFile!.existsSync()) {
      // Show full screen image viewer for images
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
              title: Text(
                file.name,
                style: TextStyle(color: Colors.white),
              ),
              leading: IconButton(
                icon: Icon(TablerIcons.x, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            body: Center(
              child: InteractiveViewer(
                child: Image.file(
                  file.localFile!,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stackTrace) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            TablerIcons.photo_off,
                            color: Colors.white,
                            size: 64,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Không thể hiển thị ảnh',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      );
    } else {
      // For non-image files, show info
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                TablerIcons.info_circle,
                color: Colors.white,
                size: 16,
              ),
              SizedBox(width: 8),
              Text('File: ${file.name} (${file.sizeFormatted})'),
            ],
          ),
          backgroundColor: AppColors.info,
        ),
      );
    }
  }

  /// Handle FileUploadBloc state changes
  void _handleFileUploadState(BuildContext context, FileUploadState state) {
    debugPrint('🔄 FileUploadBloc state: ${state.runtimeType}');
    
    if (state is FileUploading) {
      debugPrint('📤 FileUploading: ${state.documentType}, progress: ${state.progress}');
      _uploadProgress[state.documentType] = state.progress; // Use documentType as key
      _updateDocumentUploadingStatus(state.filePath, state.documentType);
    } else if (state is FileUploaded) {
      debugPrint('✅ FileUploaded: ${state.documentType}, documentId: ${state.documentId}');
      _uploadProgress.remove(state.documentType); // Use documentType as key
      _uploadedDocumentIds[state.documentType] = state.documentId;
      
      // Find the correct document by matching file path with recent uploads
      final documentId = _findDocumentIdByFilePath(state.filePath);
      if (documentId != null) {
        _updateDocumentUploadedStatus(state.filePath, documentId, state.documentId);
      } else {
        // Fallback to old method if documentId not found
        final docToUpdate = _findDocumentByType(state.documentType);
        if (docToUpdate != null) {
          _updateDocumentUploadedStatus(state.filePath, docToUpdate.id, state.documentId);
        }
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.check,
                  color: Colors.white,
                  size: AppDimensions.iconS,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Text('Tải lên thành công!'),
              ],
            ),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } else if (state is FileUploadError) {
      debugPrint('❌ FileUploadError: ${state.documentType}, message: ${state.message}');
      _uploadProgress.remove(state.documentType); // Use documentType as key
      
      // Find the correct document by matching file path with recent uploads
      final documentId = _findDocumentIdByFilePath(state.filePath);
      if (documentId != null) {
        _updateDocumentErrorStatus(state.filePath, documentId);
      } else {
        // Fallback to old method if documentId not found
        final docToUpdate = _findDocumentByType(state.documentType);
        if (docToUpdate != null) {
          _updateDocumentErrorStatus(state.filePath, docToUpdate.id);
        }
      }
      
      if (mounted) {
        // Show error dialog with retry option instead of just snackbar
        _showUploadErrorDialog(state.message, state.filePath, state.documentType);
      }
    }
  }
  
  /// Update document status when uploading
  void _updateDocumentUploadingStatus(String filePath, String documentType) {
    final docToUpdate = _findDocumentByType(documentType);
    if (docToUpdate != null) {
      final updatedDoc = docToUpdate.updateStatus(DocumentStatus.uploading);
      _updateDocumentInList(updatedDoc);
    }
  }
  
  /// Show upload error dialog with retry option
  void _showUploadErrorDialog(String message, String filePath, String documentType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_circle,
              color: AppColors.error,
              size: 24,
            ),
            SizedBox(width: 8),
            Text('Lỗi tải lên'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Không thể tải lên file. Vui lòng thử lại.'),
            SizedBox(height: 8),
            Text(
              'Lỗi: $message',
              style: TextStyle(
                fontSize: 12,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Đóng'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _retryUpload(filePath, documentType);
            },
            child: Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Retry upload for failed files
  void _retryUpload(String filePath, String documentType) {
    debugPrint('🔄 Retrying upload for file: $filePath, type: $documentType');
    
    // Find the document by type
    final docToUpdate = _findDocumentByType(documentType);
    if (docToUpdate != null) {
      // Add to upload queue instead of direct upload
      _uploadQueue.add({
        'filePath': filePath,
        'documentType': documentType,
        'document': docToUpdate,
      });
      
      // Process queue
      _processUploadQueue();
    }
  }

  /// Process upload queue to prevent concurrent uploads
  Future<void> _processUploadQueue() async {
    if (_isUploading || _uploadQueue.isEmpty) return;
    
    _isUploading = true;
    debugPrint('📤 Processing upload queue: ${_uploadQueue.length} items');
    
    while (_uploadQueue.isNotEmpty) {
      final uploadItem = _uploadQueue.removeAt(0);
      final filePath = uploadItem['filePath'] as String;
      final documentType = uploadItem['documentType'] as String;
      final docToUpdate = uploadItem['document'] as DocumentModel;
      
      try {
        // Add delay between uploads to prevent server overload
        if (_uploadQueue.isNotEmpty) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
        
        // Track file path to document ID mapping
        _filePathToDocumentId[filePath] = docToUpdate.id;
        
        // Upload file
        if (mounted) {
          final mappingKey = _getDocumentMappingKey(docToUpdate);
          debugPrint('📤 Uploading file: $documentType, file: $filePath');
          context.read<FileUploadBloc>().add(
            UploadFileWithProgressEvent(
              filePath,
              'documents/${DateTime.now().year}/${DateTime.now().month}',
              documentType,
              metadata: {
                'source': 'documents_step',
                'documentId': docToUpdate.id,
                'mappingKey': mappingKey,
              },
            ),
          );
        }
      } catch (e) {
        debugPrint('❌ Error processing upload queue item: $e');
      }
    }
    
    _isUploading = false;
  }

  /// Update document status when uploaded successfully
  void _updateDocumentUploadedStatus(String filePath, String documentId, String serverDocumentId) {
    debugPrint('📝 Updating document status for documentId: $documentId, serverId: $serverDocumentId');
    final docToUpdate = _findDocumentById(documentId);
    debugPrint('🔍 Found document to update: ${docToUpdate?.id} (${docToUpdate?.name})');
    
    if (docToUpdate != null) {
      // Get actual file size
      int fileSize = 0;
      try {
        final file = File(filePath);
        if (file.existsSync()) {
          fileSize = file.lengthSync();
        }
      } catch (e) {
        debugPrint('Error getting file size: $e');
      }
      
      // Create uploaded file record with proper data
      final fileName = filePath.split('/').last;
      final fileId = serverDocumentId.isNotEmpty ? serverDocumentId : '${DateTime.now().millisecondsSinceEpoch}_$fileName';
    
      final uploadedFile = UploadedFile(
        id: fileId,
        name: fileName,
        path: filePath,
        type: _getFileTypeFromPath(filePath),
        size: fileSize,
        uploadedAt: DateTime.now(),
        url: 'server://uploads/$fileId',
        localFile: File(filePath), // Add local file reference for preview
      );
      
      final updatedDoc = docToUpdate.addFile(uploadedFile).updateStatus(DocumentStatus.uploaded);
      debugPrint('💾 Updated document: ${updatedDoc.id}, files count: ${updatedDoc.files.length}, status: ${updatedDoc.status}');
      _updateDocumentInList(updatedDoc);
      
      // Clean up file path mapping after successful upload
      _filePathToDocumentId.remove(filePath);
    } else {
      debugPrint('⚠️ No document found for documentId: $documentId');
    }
  }
  
  /// Update document status when upload failed
  void _updateDocumentErrorStatus(String filePath, String documentId) {
    final docToUpdate = _findDocumentById(documentId);
    if (docToUpdate != null) {
      final updatedDoc = docToUpdate.updateStatus(DocumentStatus.failed);
      _updateDocumentInList(updatedDoc);
    }
    
    // Clean up file path mapping after failed upload
    _filePathToDocumentId.remove(filePath);
  }
  
  /// Find document by document type
  DocumentModel? _findDocumentByType(String documentType) {
    debugPrint('🔍 Looking for document type: $documentType');
    debugPrint('📝 Available documents:');
    for (final doc in [..._requiredDocuments, ..._optionalDocuments]) {
      final docTypeCode = _getDocumentTypeCode(doc.type);
      debugPrint('  - ${doc.id}: ${doc.name} (type: $docTypeCode)');
      if (docTypeCode == documentType) {
        debugPrint('✅ Found matching document: ${doc.id}');
        return doc;
      }
    }
    debugPrint('❌ No document found for type: $documentType');
    return null;
  }
  
  /// Find document by document ID
  DocumentModel? _findDocumentById(String documentId) {
    debugPrint('🔍 Looking for document ID: $documentId');
    debugPrint('📝 Available documents:');
    for (final doc in [..._requiredDocuments, ..._optionalDocuments]) {
      debugPrint('  - ${doc.id}: ${doc.name}');
      if (doc.id == documentId) {
        debugPrint('✅ Found matching document: ${doc.id} (${doc.name})');
        return doc;
      }
    }
    debugPrint('❌ No document found for ID: $documentId');
    return null;
  }
  
  /// Find document ID by file path (for recent uploads)
  String? _findDocumentIdByFilePath(String filePath) {
    debugPrint('🔍 Looking for document ID by file path: $filePath');
    final documentId = _filePathToDocumentId[filePath];
    if (documentId != null) {
      debugPrint('✅ Found document ID: $documentId for file: $filePath');
      return documentId;
    }
    debugPrint('❌ No document ID found for file path: $filePath');
    return null;
  }
  
  /// Get document type code for FileUploadBloc
  String _getDocumentTypeCode(DocumentType type) {
    return type.code; // Sử dụng trực tiếp backend code từ enum
  }
  
  /// Get mapping key for document (used for upload identification)
  String? _getDocumentMappingKey(DocumentModel doc) {
    return doc.mappingKey;
  }
  
  /// Get file type from path
  FileType _getFileTypeFromPath(String path) {
    final extension = path.split('.').last.toLowerCase();
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
      return FileType.image;
    } else if (extension == 'pdf') {
      return FileType.pdf;
    } else {
      return FileType.document;
    }
  }

  /// Wait for upload completion to prevent concurrent uploads
  Future<void> _waitForUploadCompletion(String filePath, String documentId) async {
    debugPrint('⏳ Waiting for upload completion: $filePath');
    
    // Wait for either success or error state
    await Future.delayed(const Duration(milliseconds: 100)); // Small delay to let the event process
    
    // Check if file path is still in mapping (means upload is still in progress)
    int attempts = 0;
    const maxAttempts = 30; // 30 seconds timeout
    const delayMs = 1000; // 1 second delay between checks
    
    while (_filePathToDocumentId.containsKey(filePath) && attempts < maxAttempts) {
      await Future.delayed(const Duration(milliseconds: delayMs));
      attempts++;
      debugPrint('⏳ Upload still in progress: $filePath (attempt $attempts/$maxAttempts)');
    }
    
    if (attempts >= maxAttempts) {
      debugPrint('⚠️ Upload timeout for: $filePath');
    } else {
      debugPrint('✅ Upload completed for: $filePath');
    }
  }

  /// Get appropriate icon for file type
  IconData _getFileIcon(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'pdf':
        return TablerIcons.file_type_pdf;
      case 'doc':
      case 'docx':
        return TablerIcons.file_type_doc;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'bmp':
      case 'webp':
        return TablerIcons.photo;
      default:
        return TablerIcons.file;
    }
  }
  
  /// Request camera permission
  Future<bool> _requestCameraPermission() async {
    try {
      final status = await Permission.camera.request();
      await _logger.d('Camera permission status: $status');
      return status == PermissionStatus.granted;
    } catch (e) {
      await _logger.e('Error requesting camera permission: $e');
      return false;
    }
  }
  
  /// Validate file size (max 5MB)
  bool _validateFileSize(File file) {
    final maxSize = 5 * 1024 * 1024; // 5MB
    return file.lengthSync() <= maxSize;
  }

  /// Show permission dialog with option to open app settings
  void _showPermissionDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          content: Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Hủy',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Open app settings
                await openAppSettings();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Cài đặt'),
            ),
          ],
        );
      },
    );
  }

  /// Process picked files from file picker
  Future<void> _processPickedFiles(picker.FilePickerResult result, DocumentModel doc) async {
    debugPrint('📁 Selected ${result.files.length} files from device');
    
    // Filter allowed file types
    final allowedExtensions = {'pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'};
    final validFiles = <picker.PlatformFile>[];
    
    for (final pickedFile in result.files) {
      if (pickedFile.path == null) continue;
      
      final extension = pickedFile.extension?.toLowerCase();
      if (extension != null && allowedExtensions.contains(extension)) {
        validFiles.add(pickedFile);
      } else {
        debugPrint('📁 Skipping unsupported file: ${pickedFile.name}');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File ${pickedFile.name} không được hỗ trợ'),
              backgroundColor: AppColors.warning,
            ),
          );
        }
      }
    }
    
    if (validFiles.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Không có file hợp lệ nào được chọn'),
            backgroundColor: AppColors.warning,
          ),
        );
      }
      return;
    }
    
    // Process each valid file SEQUENTIALLY to avoid 502 errors
    for (int i = 0; i < validFiles.length; i++) {
      final pickedFile = validFiles[i];
      final file = File(pickedFile.path!);
      await _logger.d('File picked: ${pickedFile.path} (${i + 1}/${validFiles.length})');

      // Validate file size
      if (!_validateFileSize(file)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File ${pickedFile.name} quá lớn (tối đa 5MB)'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        continue; // Skip this file and continue with others
      }

      // Add to upload queue instead of direct upload
      _uploadQueue.add({
        'filePath': file.path,
        'documentType': doc.type,
        'document': doc,
      });
      
      // Process queue
      _processUploadQueue();
        context.read<FileUploadBloc>().add(
          UploadFileWithProgressEvent(
            file.path,
            'documents/${DateTime.now().year}/${DateTime.now().month}',
            documentType,
            metadata: {
              'source': 'file_picker',
              'documentId': doc.id,
              'mappingKey': mappingKey ?? '',
              'productCode': doc.productCode ?? '',
              'fileName': pickedFile.name,
              'fileExtension': pickedFile.extension ?? '',
              'uploadIndex': i + 1,
              'totalFiles': validFiles.length,
            },
          ),
        );
        
        // Wait for this upload to complete before starting the next one
        // This prevents concurrent uploads that cause 502 errors
        await _waitForUploadCompletion(file.path, doc.id);
      }
    }

    // Show success message if at least one file was processed
    if (mounted && validFiles.isNotEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Đang tải lên ${validFiles.length} file tuần tự...'),
          backgroundColor: AppColors.kienlongOrange,
        ),
      );
    }
  }
  
  void _deleteFile(DocumentModel doc, UploadedFile file) async {
    try {
      // Check if file exists in document
      final fileExists = doc.files.any((f) => f.id == file.id);
      
      if (!fileExists) {
        debugPrint('❌ File not found in document!');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('File không tồn tại trong tài liệu'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }
      
      // Remove specific file from document
      final updatedDoc = doc.removeFile(file.id);
      _updateDocumentInList(updatedDoc);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã xóa file: ${file.name}'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi xóa file: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _updateDocumentInList(DocumentModel updatedDoc) {
    debugPrint('🔄 Updating document in list: ${updatedDoc.id}');
    setState(() {
      // Update in required documents
      final requiredIndex = _requiredDocuments.indexWhere((d) => d.id == updatedDoc.id);
      if (requiredIndex >= 0) {
        debugPrint('✅ Updated in required documents at index: $requiredIndex');
        _requiredDocuments[requiredIndex] = updatedDoc;
      } else {
        // Update in optional documents
        final optionalIndex = _optionalDocuments.indexWhere((d) => d.id == updatedDoc.id);
        if (optionalIndex >= 0) {
          debugPrint('✅ Updated in optional documents at index: $optionalIndex');
          _optionalDocuments[optionalIndex] = updatedDoc;
        } else {
          debugPrint('⚠️ Document not found in either list: ${updatedDoc.id}');
        }
      }
      _updateDocuments();
    });
  }

  void _handleCameraCapture(DocumentModel? doc) async {
    Navigator.pop(context);
    
    if (doc == null) return;

    try {
      // Request camera permission
      final hasPermission = await _requestCameraPermission();
      if (!hasPermission) {
        throw Exception('Camera permission denied');
      }

      // Capture image from camera
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image == null) return;
      
      final file = File(image.path);
      await _logger.d('Image captured from camera: ${image.path}');

      // Validate file size
      if (!_validateFileSize(file)) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('File quá lớn (tối đa 5MB)'),
              backgroundColor: AppColors.error,
            ),
          );
        }
        return;
      }

      // Track file path to document ID mapping
      _filePathToDocumentId[file.path] = doc.id;
      
      // Upload file using FileUploadBloc
      if (mounted) {
        final documentType = _getDocumentTypeCode(doc.type);
        final mappingKey = _getDocumentMappingKey(doc);
        debugPrint('📤 Adding UploadFileWithProgressEvent (camera): $documentType, mappingKey: $mappingKey, file: ${file.path}');
        context.read<FileUploadBloc>().add(
          UploadFileWithProgressEvent(
            file.path,
            'documents/${DateTime.now().year}/${DateTime.now().month}',
            documentType,
            metadata: {
              'source': 'camera',
              'documentId': doc.id,
              'mappingKey': mappingKey ?? '',
              'productCode': doc.productCode ?? '',
            },
          ),
        );
        debugPrint('✅ UploadFileWithProgressEvent (camera) added successfully');
      }

    } catch (e) {
      await _logger.e('Error capturing image from camera: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi chụp ảnh: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  void _handleGalleryPicker(DocumentModel? doc) async {
    Navigator.pop(context);
    
    if (doc == null) return;

    try {
      // Optimized permission handling for photo library access only
      PermissionStatus photosPermission = await Permission.photos.status;
      
      // Request photos permission if needed
      if (photosPermission.isDenied || photosPermission.isRestricted) {
        photosPermission = await Permission.photos.request();
      }
      
      // Handle permanent denial
      if (photosPermission.isPermanentlyDenied) {
        if (mounted) {
          _showPermissionDialog(
            'Quyền truy cập thư viện ảnh',
            'Ứng dụng cần quyền truy cập thư viện ảnh để chọn ảnh. Vui lòng cấp quyền trong Cài đặt.',
          );
        }
        return;
      }
      
      debugPrint('📸 Photos permission status: $photosPermission');

      // Pick multiple images from gallery - only images, not videos or other media
      List<XFile> images = [];
      
      try {
        // Use pickMultiImage for better performance and photo library access
        images = await _picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1080,
          imageQuality: 85,
        );
      } catch (pickError) {
        debugPrint('📸 pickMultiImage failed: $pickError');
        
        // Fallback to single image picker from gallery
        try {
          final XFile? singleImage = await _picker.pickImage(
            source: ImageSource.gallery,
            maxWidth: 1920,
            maxHeight: 1080,
            imageQuality: 85,
          );
          
          if (singleImage != null) {
            images = [singleImage];
          }
        } catch (singlePickError) {
          debugPrint('📸 Single image picker also failed: $singlePickError');
          rethrow; // Let the outer catch handle this
        }
      }

      if (images.isEmpty) {
        debugPrint('📸 No images selected');
        return;
      }
      
      debugPrint('📸 Selected ${images.length} images from gallery');
      
      // Process each selected image SEQUENTIALLY to avoid 502 errors
      int processedCount = 0;
      for (int i = 0; i < images.length; i++) {
        final image = images[i];
        final file = File(image.path);
        await _logger.d('Image picked from gallery: ${image.path} (${i + 1}/${images.length})');

        // Validate file size
        if (!_validateFileSize(file)) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Ảnh ${image.name} quá lớn (tối đa 5MB)'),
                backgroundColor: AppColors.warning,
              ),
            );
          }
          continue; // Skip this file and continue with others
        }

        // Validate it's actually an image file
        final extension = image.path.split('.').last.toLowerCase();
        if (!['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].contains(extension)) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('File ${image.name} không phải là ảnh hợp lệ'),
                backgroundColor: AppColors.warning,
              ),
            );
          }
          continue;
        }

        // Track file path to document ID mapping
        _filePathToDocumentId[file.path] = doc.id;
        
        // Upload file using FileUploadBloc - SEQUENTIALLY
        if (mounted) {
          final documentType = _getDocumentTypeCode(doc.type);
          final mappingKey = _getDocumentMappingKey(doc);
          debugPrint('📤 Adding UploadFileWithProgressEvent: $documentType, mappingKey: $mappingKey, file: ${file.path} (${i + 1}/${images.length})');
          context.read<FileUploadBloc>().add(
            UploadFileWithProgressEvent(
              file.path,
              'documents/${DateTime.now().year}/${DateTime.now().month}',
              documentType,
              metadata: {
                'source': 'photo_library',
                'documentId': doc.id,
                'mappingKey': mappingKey ?? '',
                'productCode': doc.productCode ?? '',
                'fileName': image.name,
                'fileExtension': extension,
                'uploadIndex': i + 1,
                'totalFiles': images.length,
              },
            ),
          );
          debugPrint('✅ UploadFileWithProgressEvent added successfully');
          
          // Wait for this upload to complete before starting the next one
          // This prevents concurrent uploads that cause 502 errors
          await _waitForUploadCompletion(file.path, doc.id);
          processedCount++;
        }
      }

      // Show success message if at least one file was processed
      if (mounted && processedCount > 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đang tải lên $processedCount ảnh tuần tự...'),
            backgroundColor: AppColors.kienlongOrange,
          ),
        );
      } else if (mounted && processedCount == 0 && images.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không có ảnh hợp lệ nào được xử lý'),
            backgroundColor: AppColors.warning,
          ),
        );
      }

    } catch (e) {
      await _logger.e('Error picking images from photo library: $e');
      
      // Check if it's a permission error
      if (e.toString().contains('permission') || e.toString().contains('Permission')) {
        if (mounted) {
          _showPermissionDialog(
            'Quyền truy cập thư viện ảnh',
            'Không thể truy cập thư viện ảnh. Vui lòng cấp quyền trong Cài đặt.',
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Lỗi chọn ảnh: $e'),
              backgroundColor: AppColors.error,
            ),
          );
        }
      }
    }
  }

  void _handleFilePicker(DocumentModel? doc) async {
    Navigator.pop(context);
    
    if (doc == null) return;

    try {
      // For Android 13+ (API 33+), we don't need storage permission for file picker
      // But for older versions, we still need it
      
      // Check storage permission for older Android versions
      PermissionStatus storageStatus = await Permission.storage.status;
      if (storageStatus.isDenied) {
        storageStatus = await Permission.storage.request();
      }
      
      debugPrint('📁 Storage permission status: $storageStatus');
      
      // For Android 11+ (API 30+), also check manage external storage
      PermissionStatus manageStorageStatus = await Permission.manageExternalStorage.status;
      if (manageStorageStatus.isDenied) {
        manageStorageStatus = await Permission.manageExternalStorage.request();
      }
      
      debugPrint('📁 Manage storage permission status: $manageStorageStatus');
      
      // Check if we have any necessary permissions
      // For modern Android, file picker works without explicit storage permission
      // But we still check for compatibility
      if (storageStatus.isPermanentlyDenied && manageStorageStatus.isPermanentlyDenied) {
        if (mounted) {
          _showPermissionDialog(
            'Quyền truy cập bộ nhớ',
            'Ứng dụng cần quyền truy cập bộ nhớ để chọn file. Vui lòng cấp quyền trong Cài đặt.',
          );
        }
        return;
      }

      // Pick multiple files using file_picker with support for documents and images
      final picker.FilePickerResult? result = await picker.FilePicker.platform.pickFiles(
        type: picker.FileType.any, // Try FileType.any first
        allowMultiple: true,
        withData: false, // Only get file paths, not data for better performance
      );
      
      // If no result, try with specific file types
      if (result == null) {
        debugPrint('📁 First attempt failed, trying with custom types...');
        final picker.FilePickerResult? customResult = await picker.FilePicker.platform.pickFiles(
          type: picker.FileType.custom,
          allowedExtensions: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'],
          allowMultiple: true,
          withData: false,
        );
        
        if (customResult == null || customResult.files.isEmpty) return;
        
        // Process custom result
        await _processPickedFiles(customResult, doc);
        return;
      }

      if (result.files.isEmpty) return;
      
      // Process the result
      await _processPickedFiles(result, doc);

    } catch (e) {
      await _logger.e('Error picking files: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi chọn file: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Show upload error dialog with retry option
  void _showUploadErrorDialog(BuildContext context, FileUploadError state) {
    // Check if it's a server error (502, 503, 504)
    final isServerError = state.message.contains('502') || 
                         state.message.contains('503') || 
                         state.message.contains('504') ||
                         state.message.contains('Server error') ||
                         state.message.contains('External service error');
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Row(
            children: [
              Icon(
                isServerError ? TablerIcons.server_off : TablerIcons.alert_circle,
                color: isServerError ? AppColors.warning : AppColors.error,
                size: 24,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                isServerError ? 'Lỗi máy chủ' : 'Lỗi tải lên',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                isServerError 
                  ? 'Máy chủ đang gặp sự cố tạm thời. Vui lòng thử lại sau ít phút.'
                  : 'Không thể tải lên file. Vui lòng kiểm tra kết nối mạng và thử lại.',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
              if (isServerError) ...[
                SizedBox(height: AppDimensions.spacingM),
                Container(
                  padding: EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: AppColors.warning.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(
                      color: AppColors.warning.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        TablerIcons.info_circle,
                        color: AppColors.warning,
                        size: 16,
                      ),
                      SizedBox(width: AppDimensions.spacingS),
                      Expanded(
                        child: Text(
                          'Lỗi này thường xảy ra khi server quá tải. Thử lại sau 1-2 phút.',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppColors.warning,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Đóng',
                style: TextStyle(color: AppColors.textSecondary),
              ),
            ),
            if (isServerError)
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _retryUpload(state.filePath, state.documentType);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.kienlongOrange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('Thử lại'),
              ),
          ],
        );
      },
    );
  }

  /// Retry upload for failed files
  void _retryUpload(String filePath, String documentType) {
    debugPrint('🔄 Retrying upload for file: $filePath, type: $documentType');
    
    // Find the document by type
    final docToUpdate = _findDocumentByType(documentType);
    if (docToUpdate != null) {
      // Add to upload queue instead of immediate upload
      _addToUploadQueue(filePath, documentType, docToUpdate, 'retry');
      
      // Show retry message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  TablerIcons.refresh,
                  color: Colors.white,
                  size: 16,
                ),
                SizedBox(width: AppDimensions.spacingS),
                const Text('Đang thử lại tải lên...'),
              ],
            ),
            backgroundColor: AppColors.kienlongOrange,
          ),
        );
      }
    } else {
      debugPrint('❌ No document found for retry: $documentType');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không thể thử lại tải lên file'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  /// Add file to upload queue
  void _addToUploadQueue(String filePath, String documentType, DocumentModel doc, String source) {
    final uploadItem = {
      'filePath': filePath,
      'documentType': documentType,
      'documentId': doc.id,
      'mappingKey': _getDocumentMappingKey(doc),
      'productCode': doc.productCode ?? '',
      'fileName': filePath.split('/').last,
      'source': source,
    };
    
    _uploadQueue.add(uploadItem);
    debugPrint('📋 Added to upload queue: ${uploadItem['fileName']} (${_uploadQueue.length} items)');
    
    // Start processing queue if not already uploading
    if (!_isUploading) {
      _processUploadQueue();
    }
  }

  /// Process upload queue with delay between uploads
  Future<void> _processUploadQueue() async {
    if (_uploadQueue.isEmpty || _isUploading) return;
    
    _isUploading = true;
    debugPrint('🚀 Starting upload queue processing (${_uploadQueue.length} items)');
    
    while (_uploadQueue.isNotEmpty && mounted) {
      final uploadItem = _uploadQueue.removeAt(0);
      
      // Track file path to document ID mapping
      _filePathToDocumentId[uploadItem['filePath']] = uploadItem['documentId'];
      
      // Upload file using FileUploadBloc
      if (mounted) {
        debugPrint('📤 Processing upload: ${uploadItem['fileName']} (${uploadItem['source']})');
        context.read<FileUploadBloc>().add(
          UploadFileWithProgressEvent(
            uploadItem['filePath'],
            'documents/${DateTime.now().year}/${DateTime.now().month}',
            uploadItem['documentType'],
            metadata: {
              'source': uploadItem['source'],
              'documentId': uploadItem['documentId'],
              'mappingKey': uploadItem['mappingKey'] ?? '',
              'productCode': uploadItem['productCode'],
              'fileName': uploadItem['fileName'],
            },
          ),
        );
        
        // Wait for upload to complete (with timeout)
        await _waitForUploadCompletion(uploadItem['filePath']);
        
        // Add delay between uploads to prevent server overload
        if (_uploadQueue.isNotEmpty && mounted) {
          debugPrint('⏳ Waiting 2 seconds before next upload...');
          await Future.delayed(const Duration(seconds: 2));
        }
      }
    }
    
    _isUploading = false;
    debugPrint('✅ Upload queue processing completed');
  }

  /// Wait for upload completion with timeout
  Future<void> _waitForUploadCompletion(String filePath) async {
    const timeout = Duration(seconds: 30);
    final startTime = DateTime.now();
    
    while (mounted && DateTime.now().difference(startTime) < timeout) {
      // Check if file is still in progress
      final isStillUploading = _uploadProgress.values.any((progress) => progress < 1.0);
      if (!isStillUploading) {
        break;
      }
      
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  void _showUploadOptions([DocumentModel? doc]) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) {
        return Container(
          margin: EdgeInsets.all(AppDimensions.paddingM),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusL),
          ),
          child: Padding(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  doc != null ? 'Tải lên ${doc.name}' : 'Tải lên tài liệu',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: AppDimensions.spacingL),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.camera,
                    color: AppColors.kienlongOrange,
                  ),
                  title: const Text('Chụp ảnh'),
                  subtitle: const Text('Sử dụng camera để chụp tài liệu'),
                  onTap: () => _handleCameraCapture(doc),
                ),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.photo,
                    color: AppColors.kienlongSkyBlue,
                  ),
                  title: const Text('Chọn từ thư viện'),
                  subtitle: const Text('Chọn nhiều ảnh từ thư viện điện thoại'),
                  onTap: () => _handleGalleryPicker(doc),
                ),
                
                ListTile(
                  leading: Icon(
                    TablerIcons.file,
                    color: AppColors.info,
                  ),
                  title: const Text('Chọn file'),
                  subtitle: const Text('Chọn nhiều file PDF, DOC, ảnh từ thiết bị'),
                  onTap: () => _handleFilePicker(doc),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
} 