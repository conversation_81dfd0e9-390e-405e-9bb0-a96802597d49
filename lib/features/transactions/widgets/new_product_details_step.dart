import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../../auth/blocs/master_data_bloc.dart';
import '../../auth/services/auth_service.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/ward_model.dart';
import '../../../shared/models/collateral_category_model.dart';
import '../../../shared/constants/config_types.dart';
import '../../auth/services/qr_scanner_service.dart';
import '../../../shared/utils/index.dart';
import '../utils/product_form_manager.dart';
import '../utils/product_form_validator.dart';
import '../utils/typed_form_manager.dart';
import '../models/form_data/form_data.dart';
import '../../../shared/services/master_data_service.dart';

class NewProductDetailsStep extends StatefulWidget {
  final ProductModel? product;
  final CustomerModel? selectedCustomer;
  final Map<String, dynamic> details;
  final Function(Map<String, dynamic>) onDetailsChanged;
  final Function(bool)? onValidationChanged; // Deprecated - sẽ remove sau

  const NewProductDetailsStep({
    super.key,
    required this.product,
    this.selectedCustomer,
    required this.details,
    required this.onDetailsChanged,
    this.onValidationChanged,
  });

  @override
  State<NewProductDetailsStep> createState() => _NewProductDetailsStepState();
}

class _NewProductDetailsStepState extends State<NewProductDetailsStep> 
    with AutomaticKeepAliveClientMixin {
  final _formKey = GlobalKey<FormState>();
  
  // Sử dụng ProductFormManager thay vì _formData
  late ProductFormManager _formManager;
  late ProductFormValidator _validator;
  
  bool _isValidating = false;
  final ScrollController _scrollController = ScrollController();
  
  // Debounce timer for form updates
  Timer? _debounceTimer;
  
  // TextEditingControllers for form fields
  final Map<String, TextEditingController> _controllers = {};
  
  // Auth service for getting branch info
  final AuthService _authService = AuthService();

  @override
  void initState() {
    super.initState();
    _initializeTypedFormManager();
    _initializeFormManager();
    _initializeValidator();
    
    // Map customer data if available
    if (widget.selectedCustomer != null) {
      _mapCustomerToFormData();
    }
    
    // Defer state update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadMasterData();
        _updateDetails();
      }
    });
  }

  /// Initialize TypedFormManager
  void _initializeTypedFormManager() {
    _typedFormManager = TypedFormManager.fromMap(
      data: Map.from(widget.details),
      product: widget.product,
      selectedCustomer: widget.selectedCustomer,
    );
  }

  /// Initialize ProductFormManager
  void _initializeFormManager() {
    _formManager = ProductFormManager(
      initialData: Map.from(widget.details),
      product: widget.product,
      selectedCustomer: widget.selectedCustomer,
      masterDataService: MasterDataService(), // Truyền MasterDataService để lấy real data
    );
    
    // Initialize with defaults if empty
    if (_formManager.formData.isEmpty) {
      _formManager.initializeWithDefaults();
    }
    
    // Ensure loan_type has default value from config
    if (_formManager.getFieldValue<String>('loan_type') == null) {
      final defaultValue = _formManager.config?.getDefaultValue('loan_type');
      final defaultLoanType = defaultValue is String ? defaultValue : 'Có TSBĐ';
      _formManager.setFieldValue<String>('loan_type', defaultLoanType);
    }
    
    // Auto-fill branch code from AuthService
    _autofillBranchInfo();
  }

  /// Initialize ProductFormValidator - DEPRECATED: Chỉ dùng cho basic validation
  void _initializeValidator() {
    // Deprecated: ProductFormValidator giờ được sử dụng tại CreateTransactionScreen
    // Giữ lại để tương thích với validateForm() deprecated method
    _validator = ProductFormValidator(
      formData: _formManager.formData,
      product: widget.product,
      selectedCustomer: widget.selectedCustomer,
    );
  }

  /// Load master data for transaction forms
  void _loadMasterData() {
    debugPrint('=== START: Loading master data ===');
    
    // Load provinces for address dropdowns
    context.read<MasterDataBloc>().add(const LoadProvincesEvent());
    
    // Load transaction-related configs
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_TERM_DAYS));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_PURPOSE));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.INCOME_SOURCE));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.ASSET_CONDITION));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.REPAYMENT_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.DISBURSEMENT_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.HANDOVER_CONDITION));
    
    // Load personal info related configs
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.SEX));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.MARITAL_STATUS));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.ID_CARD_TYPE));
    
    // Load bank accounts for selected customer
    _loadBankAccountsForSelectedCustomer();
    
    // Load collateral categories for installment loan products (GOLD_LOAN, MANGO)
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      context.read<MasterDataBloc>().add(LoadCollateralCategoriesEvent());
    }
    
    debugPrint('=== END: Loading master data ===');
  }

  @override
  void didUpdateWidget(NewProductDetailsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Update form manager and validator when product or customer changes
    if (widget.product != oldWidget.product || widget.selectedCustomer != oldWidget.selectedCustomer) {
      final masterDataService = MasterDataService();
      _formManager = _formManager.withProduct(widget.product, masterDataService: masterDataService).withCustomer(widget.selectedCustomer, masterDataService: masterDataService);
      _validator = ProductFormValidator(
        formData: _formManager.formData,
        product: widget.product,
        selectedCustomer: widget.selectedCustomer,
      );
      
      // Map customer data if available and different from previous
      if (widget.selectedCustomer != null && widget.selectedCustomer != oldWidget.selectedCustomer) {
        _mapCustomerToFormData();
      }
      
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateDetails();
        }
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounceTimer?.cancel();
    // Dispose all text controllers
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    super.dispose();
  }

  /// Get or create TextEditingController for a field key
  TextEditingController _getController(String key) {
    if (!_controllers.containsKey(key)) {
      String initialValue = _formManager.getFieldValue<String>(key) ?? '';
      
      // For amount fields, remove leading zeros from display
      if (_isAmountField(key) && initialValue.isNotEmpty) {
        final cleanValue = initialValue.replaceAll(RegExp(r'[^\d]'), '');
        if (cleanValue.isNotEmpty) {
          try {
            initialValue = int.parse(cleanValue).toString();
          } catch (e) {
            initialValue = cleanValue;
          }
        }
      }
      
      _controllers[key] = TextEditingController(text: initialValue);
      
      // Add listener to sync with _formManager
      _controllers[key]!.addListener(() {
        final controllerText = _controllers[key]!.text;
        
        // For amount fields, process to remove leading zeros and update controller text
        if (_isAmountField(key)) {
          final cleanValue = controllerText.replaceAll(RegExp(r'[^\d]'), '');
          
          // Remove leading zeros safely
          String finalValue = '';
          if (cleanValue.isNotEmpty) {
            try {
              finalValue = int.parse(cleanValue).toString();
            } catch (e) {
              finalValue = cleanValue; // Fallback to original if parsing fails
            }
          }
          
          // Update controller text if it differs (this will remove leading zeros in display)
          if (controllerText != finalValue && finalValue.isNotEmpty) {
            _controllers[key]!.value = TextEditingValue(
              text: finalValue,
              selection: TextSelection.collapsed(
                offset: finalValue.length.clamp(0, finalValue.length),
              ),
            );
          }
          
          // Update form data
          if (_formManager.getFieldValue<String>(key) != finalValue) {
            _formManager.setFieldValue<String>(key, finalValue);
            _debouncedUpdateDetails();
          }
        } else {
          // For non-amount fields, sync normally
          if (_formManager.getFieldValue<String>(key) != controllerText) {
            _formManager.setFieldValue<String>(key, controllerText);
            _debouncedUpdateDetails();
          }
        }
      });
    }
    return _controllers[key]!;
  }
  
  /// Check if field is an amount field
  bool _isAmountField(String key) {
    return key == 'own_capital' || key == 'loan_amount' || key == 'collateral_value';
  }
  
  /// Check if income source is business-related using master data
  bool _isBusinessIncomeSource(String incomeSourceValue) {
    if (incomeSourceValue.isEmpty) return false;
    
    // Try to get the config from master data first
    final masterDataState = context.read<MasterDataBloc>().state;
    if (masterDataState is MasterDataLoaded) {
      final incomeSourceConfigs = masterDataState.configsByGroup['INCOME_SOURCE'] ?? [];
      final selectedConfig = incomeSourceConfigs.firstWhere(
        (config) => config.id == incomeSourceValue,
        orElse: () => ConfigModel(),
      );
      
      if (selectedConfig.id != null) {
        // Check if the selected config is business-related
        final code = selectedConfig.code?.toLowerCase() ?? '';
        final label = selectedConfig.label?.toLowerCase() ?? '';
        return code == 'business' || 
               label.contains('kinh doanh') ||
               code.contains('business');
      }
    }
    
    // Fallback check using known business ID and text patterns
    final lowerValue = incomeSourceValue.toLowerCase();
    return incomeSourceValue == 'Kinh doanh' || 
           lowerValue.contains('kinh doanh') || 
           lowerValue.contains('business') ||
           lowerValue.contains('kinhdoanh') ||
           // Known business income source ID from API response
           incomeSourceValue == '31a90b85-2954-47c7-889b-2a53f968b21e';
  }
  
  /// Update controller value when _formData changes externally (like QR parsing)
  void _updateController(String key, String value) {
    final controller = _getController(key);
    
    // For amount fields, remove leading zeros
    String processedValue = value;
    if (_isAmountField(key)) {
      final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanValue.isNotEmpty) {
        try {
          processedValue = int.parse(cleanValue).toString();
        } catch (e) {
          processedValue = cleanValue;
        }
      }
    }
    
    // Update controller if value is different
    if (controller.text != processedValue) {
      controller.value = TextEditingValue(
        text: processedValue,
        selection: TextSelection.collapsed(
          offset: processedValue.length.clamp(0, processedValue.length),
        ),
      );
    }
  }


  /// Map customer data to form
  void _mapCustomerToFormData() {
    if (widget.selectedCustomer == null) return;
    
    final customer = widget.selectedCustomer!;
    debugPrint('=== START: Mapping customer data to form ===');
    debugPrint('Customer: ${customer.fullName} (${customer.id})');
    
    // Map basic information
    if (customer.fullName.isNotEmpty) {
      _formManager.setFieldValue<String>('borrower_name', customer.fullName);
      debugPrint('✓ Mapped borrower_name: ${customer.fullName}');
    }
    
    if (customer.phoneNumber?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_phone', customer.phoneNumber!);
      debugPrint('✓ Mapped borrower_phone: ${customer.phoneNumber}');
    }
    
    // Map ID card information
    if (customer.idCardNumber?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_id_number', customer.idCardNumber!);
      debugPrint('✓ Mapped borrower_id_number: ${customer.idCardNumber}');
    }
    
    if (customer.idCardType?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_id_type', customer.idCardType!);
    }
    
    if (customer.idCardIssueDate != null) {
      _formManager.setFieldValue<String>('borrower_id_issue_date', _formatDateForForm(customer.idCardIssueDate!));
    }
    
    if (customer.idCardExpiryDate != null) {
      _formManager.setFieldValue<String>('borrower_id_expiry_date', _formatDateForForm(customer.idCardExpiryDate!));
    }
    
    if (customer.idCardIssuePlace?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_id_issue_place', customer.idCardIssuePlace!);
    }
    
    // Map personal information
    if (customer.birthDate != null) {
      _formManager.setFieldValue<String>('borrower_birth_date', _formatDateForForm(customer.birthDate!));
    }
    
    if (customer.gender?.code?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_gender', customer.gender!.code!);
    }
    
    // Map permanent address
    if (customer.permanentAddress?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_permanent_address', customer.permanentAddress!);
    }
    
    if (customer.province?.id.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_permanent_province', customer.province!.id);
    }
    
    if (customer.ward?.id?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_permanent_district', customer.ward!.id!);
    }
    
    // Map current address
    if (customer.currentAddress?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_current_address', customer.currentAddress!);
    } else if (customer.sameAddress && customer.permanentAddress?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('borrower_current_address', customer.permanentAddress!);
      _formManager.setFieldValue<bool>('borrower_current_same_permanent', true);
    }
    
    debugPrint('=== END: Mapping customer data to form ===');
  }

  /// Format DateTime to string for form input (DD/MM/YYYY)
  String _formatDateForForm(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  void _updateDetails() {
    // Auto-calculate dependent fields
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      _autoCalculateFields();
    }
    
    // Debug: Log form data before passing to parent
    debugPrint('📤 NewProductDetailsStep._updateDetails() - passing data to parent:');
    debugPrint('📤 Form data keys: ${_formManager.formData.keys.toList()}');
    
    // Check specific borrower fields
    final borrowerIdNumber = _formManager.formData['borrower_id_number'];
    debugPrint('📤 borrower_id_number: "$borrowerIdNumber" (${borrowerIdNumber.runtimeType})');
    
    final borrowerName = _formManager.formData['borrower_name'];
    debugPrint('📤 borrower_name: "$borrowerName" (${borrowerName.runtimeType})');
    
    // Check co_borrower_birth_date specifically
    final coBorrowerBirthDate = _formManager.formData['co_borrower_birth_date'];
    debugPrint('📤 co_borrower_birth_date: "$coBorrowerBirthDate" (${coBorrowerBirthDate.runtimeType})');
    
    // Pass form data to parent
    widget.onDetailsChanged(_formManager.formData);
  }

  /// Debounced update to prevent excessive calls
  void _debouncedUpdateDetails() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _updateDetails();
      }
    });
  }

  /// Auto-calculate dependent fields
  void _autoCalculateFields() {
    // Auto-fill borrower info to collateral owner
    final borrowerName = _formManager.getFieldValue<String>('borrower_name');
    if (borrowerName?.isNotEmpty == true) {
      _formManager.setFieldValue<String>('collateral_owner', borrowerName!);
    }
    
    final borrowerBirthDate = _formManager.getFieldValue<String>('borrower_birth_date');
    if (borrowerBirthDate?.isNotEmpty == true) {
      final year = borrowerBirthDate!.split('/').length == 3 ? borrowerBirthDate.split('/').last : '';
      _formManager.setFieldValue<String>('collateral_owner_birth_year', year);
    }
    
    // Auto-calculate total capital need
    _updateTotalCapitalNeed();
    
    // Auto-update collateral value text
    final collateralValue = _formManager.getFieldValue<String>('collateral_value');
    if (collateralValue?.isNotEmpty == true) {
      final value = collateralValue!.replaceAll(RegExp(r'[^\d]'), '');
      final textValue = _convertNumberToWords(value);
      _formManager.setFieldValue<String>('collateral_value_text', textValue);
      _updateController('collateral_value_text', textValue);
    }
    
    // Auto-update total collateral value
    _formManager.setFieldValue<String>('total_collateral_value', collateralValue ?? '0');
  }

  /// Get typed form data dựa vào product
  T getTypedFormData<T extends BaseFormData>() {
    return _typedFormManager.getTypedFormData<T>();
  }

  /// Example: Sử dụng typed form data
  void _exampleUsage() {
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      // Sử dụng CollateralLoanFormData
      final collateralForm = getTypedFormData<CollateralLoanFormData>();
      final borrowerName = collateralForm.borrowerName;
      final collateralValue = collateralForm.collateralValue;
      // ... sử dụng các field typed
    } else if (widget.product?.code == 'PERSONAL_LOAN') {
      // Sử dụng PersonalLoanFormData
      final personalForm = getTypedFormData<PersonalLoanFormData>();
      final borrowerName = personalForm.borrowerName;
      final loanAmount = personalForm.loanAmount;
      // ... sử dụng các field typed
    }
  }
  
  /// Auto-fill branch information from AuthService
  void _autofillBranchInfo() {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.branchCode?.isNotEmpty == true) {
        final branchCode = currentUser!.profile!.branchCode!;
        final branchName = currentUser.profile!.branchName ?? '';
        
        // Set branch code field
        final branchInfo = '$branchCode${branchName.isNotEmpty ? ' - $branchName' : ''}';
        _formManager.setFieldValue<String>('branch_code', branchInfo);
        _updateController('branch_code', branchInfo);
        
        debugPrint('Auto-filled branch info: $branchCode - $branchName');
      } else {
        debugPrint('No branch code found in user profile');
      }
    } catch (e) {
      debugPrint('Error auto-filling branch info: $e');
    }
  }

  /// Update total capital need calculation
  void _updateTotalCapitalNeed() {
    final ownCapital = int.tryParse(_formManager.getFieldValue<String>('own_capital')?.replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final loanAmount = int.tryParse(_formManager.getFieldValue<String>('loan_amount')?.replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final totalCapitalNeed = ownCapital + loanAmount;
    
    // Cap at 2 billion maximum
    final cappedTotal = totalCapitalNeed > 2000000000 ? 2000000000 : totalCapitalNeed;
    final totalCapitalNeedStr = cappedTotal.toString();
    
    // Update form manager
    _formManager.setFieldValue<String>('total_capital_need', totalCapitalNeedStr);
    
    // Update controller to display in TextField
    _updateController('total_capital_need', totalCapitalNeedStr);
  }

  /// Validate form using ProductFormValidator - DEPRECATED: Validation được thực hiện tại CreateTransactionScreen
  /// Method này được giữ lại để tương thích, nhưng sẽ được remove trong tương lai
  bool validateForm() {
    if (widget.product == null) return false;
    
    debugPrint('=== START: validateForm (DEPRECATED) ===');
    debugPrint('⚠️  Warning: validateForm() is deprecated. Validation is now handled by CreateTransactionScreen');
    
    // Basic form validation only
    final isValid = _formKey.currentState?.validate() ?? false;
    
    debugPrint('Basic form validation result: $isValid');
    debugPrint('=== END: validateForm (DEPRECATED) ===');
    return isValid;
  }

  /// Load bank accounts for selected customer
  void _loadBankAccountsForSelectedCustomer() {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.personIdCardNo != null && 
          currentUser!.profile!.personIdCardNo!.isNotEmpty) {
        final idCardNo = currentUser.profile!.personIdCardNo;
        debugPrint('Loading bank accounts for customer ID card: $idCardNo');
        context.read<MasterDataBloc>().add(LoadBankAccountsEvent(idCardNo!));
      } else {
        debugPrint('No ID card number found - skipping bank account loading');
      }
    } catch (e) {
      debugPrint('Error loading bank accounts: $e');
    }
  }

  /// Convert number to words
  String _convertNumberToWords(String number) {
    if (number.isEmpty || number == '0') {
      return 'Không đồng';
    }
    
    final amount = int.tryParse(number) ?? 0;
    if (amount == 0) {
      return 'Không đồng';
    }
    
    try {
      return NumberToWordsUtil.convertToWords(amount);
    } catch (e) {
      // Fallback conversion
      if (amount >= **********) {
        return '${(amount / **********).toStringAsFixed(1)} tỷ đồng';
      } else if (amount >= 1000000) {
        return '${(amount / 1000000).toStringAsFixed(1)} triệu đồng';
      } else if (amount >= 1000) {
        return '${(amount / 1000).toStringAsFixed(1)} nghìn đồng';
      } else {
        return '$amount đồng';
      }
    }
  }

  /// Format currency
  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    
    if (widget.product == null) {
      return const SizedBox();
    }

    return SingleChildScrollView(
      controller: _scrollController,
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              '📋 Thông tin ${widget.product?.displayName ?? 'sản phẩm'}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: widget.product?.displayColor ?? AppColors.kienlongOrange,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng điền đầy đủ thông tin để tạo giao dịch',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingXL),

            // Dynamic Form based on ProductFormConfig
            ..._buildDynamicForm(),
          ],
        ),
      ),
    );
  }

  /// Build dynamic form based on ProductFormConfig
  List<Widget> _buildDynamicForm() {
    final config = _formManager.config;
    if (config == null) {
      return [
        Text(
          'Không có cấu hình cho sản phẩm này',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: AppColors.error,
          ),
        ),
      ];
    }

    List<Widget> widgets = [];

    // Build form groups in order
    for (final groupName in config.fieldGroups.keys) {
      final groupFields = _formManager.getFieldsInGroup(groupName);
      if (groupFields.isEmpty) continue;

      // Add group as expandable card
      widgets.add(_buildGroupCard(groupName, groupFields));
      widgets.add(const SizedBox(height: AppDimensions.spacingL));
    }

    return widgets;
  }

  /// Build group card
  Widget _buildGroupCard(String groupName, List<ProductFormFieldDefinition> fields) {
    final expandedKey = '${groupName}_card_expanded';
    
    String title;
    IconData icon;
    Color color;
    
    switch (groupName) {
      case 'borrower_info':
        title = 'Thông tin người vay chính';
        icon = TablerIcons.user;
        color = AppColors.kienlongOrange;
        break;
      case 'co_borrower_info':
        title = 'Thông tin người đồng vay';
        icon = TablerIcons.users;
        color = AppColors.kienlongSkyBlue;
        break;
      case 'loan_proposal':
        title = 'Đề nghị và phương án vay vốn';
        icon = TablerIcons.cash;
        color = AppColors.success;
        break;
      case 'financial_info':
        title = 'Tình hình tài chính';
        icon = TablerIcons.chart_line;
        color = AppColors.info;
        break;
      case 'collateral_info':
        title = 'Tài sản bảo đảm';
        icon = TablerIcons.shield_check;
        color = AppColors.warning;
        break;
      default:
        title = groupName.replaceAll('_', ' ');
        icon = TablerIcons.file_text;
        color = AppColors.textPrimary;
    }

    // Special handling for co-borrower section
    if (groupName == 'co_borrower_info') {
      return Column(
        children: [
          _buildSwitchField(
            'Có người đồng vay',
            'has_co_borrower',
            'Bật nếu có người đồng vay trong giao dịch này',
          ),
          if (_formManager.getFieldValue<bool>('has_co_borrower') == true) ...[
            const SizedBox(height: AppDimensions.spacingL),
            _buildExpandableCard(
              title,
              icon,
              color,
              _buildGroupFieldsWithQrNfc(fields, groupName),
              expandedKey,
            ),
          ],
        ],
      );
    }

    // Special handling for collateral_info - split into two sections
    if (groupName == 'collateral_info') {
      // Only show if loan type has collateral
      if (_formManager.getFieldValue<String>('loan_type') == 'Có TSBĐ') {
        return Column(
          children: [
            // Basic collateral info
            _buildExpandableCard(
              'Tài sản bảo đảm',
              TablerIcons.shield_check,
              AppColors.warning,
              _buildBasicCollateralFields(fields),
              'collateral_basic_card_expanded',
            ),
            const SizedBox(height: AppDimensions.spacingL),
            // Detailed collateral info
            _buildExpandableCard(
              'Chi tiết tài sản bảo đảm',
              TablerIcons.file_description,
              AppColors.info,
              _buildDetailedCollateralFields(fields),
              'collateral_detailed_card_expanded',
            ),
          ],
        );
      } else {
        return const SizedBox.shrink();
      }
    }

    // Special handling for borrower_info and loan_proposal to add QR/NFC scanning
    if (groupName == 'borrower_info' || groupName == 'loan_proposal') {
      return _buildExpandableCard(
        title,
        icon,
        color,
        _buildGroupFieldsWithQrNfc(fields, groupName),
        expandedKey,
      );
    }

    return _buildExpandableCard(
      title,
      icon,
      color,
      _buildGroupFields(fields),
      expandedKey,
    );
  }

  /// Build fields for a group
  List<Widget> _buildGroupFields(List<ProductFormFieldDefinition> fields) {
    List<Widget> widgets = [];
    
    for (int i = 0; i < fields.length; i++) {
      final field = fields[i];
      
      // Check field visibility
      if (!_formManager.isFieldVisible(field.key)) {
        continue;
      }
      
      // Build field widget based on type
      widgets.add(_buildFieldWidget(field));
      
      // Add spacing between fields
      if (i < fields.length - 1) {
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
      }
    }
    
    return widgets;
  }

  /// Build fields for a group with QR/NFC scanning
  List<Widget> _buildGroupFieldsWithQrNfc(List<ProductFormFieldDefinition> fields, String groupName) {
    List<Widget> widgets = [];
    
    // Add QR/NFC scanning buttons at the top based on group
    if (groupName == 'borrower_info') {
      widgets.add(_buildQrNfcScanningButtons(
        'Quét thông tin từ CCCD người vay chính',
        _openQrScanner,
        () {
          // TODO: Implement NFC scanning
          debugPrint('NFC Scan tapped for borrower');
          _showQrScanMessage('Tính năng NFC đang được phát triển', isError: false);
        },
      ));
      widgets.add(const SizedBox(height: AppDimensions.spacingXL));
    } else if (groupName == 'co_borrower_info') {
      widgets.add(_buildQrNfcScanningButtons(
        'Quét thông tin từ CCCD người đồng vay',
        _openCoBorrowerQrScanner,
        () {
          // TODO: Implement NFC scanning for co-borrower
          debugPrint('Co-borrower NFC Scan tapped');
          _showQrScanMessage('Tính năng NFC đang được phát triển', isError: false);
        },
      ));
      widgets.add(const SizedBox(height: AppDimensions.spacingXL));
    } else if (groupName == 'collateral_info') {
      widgets.add(_buildQrNfcScanningButtons(
        'Quét thông tin từ giấy đăng ký xe',
        _openVehicleQrScanner,
        () {
          // TODO: Implement NFC scanning for vehicle
          debugPrint('Vehicle NFC Scan tapped');
          _showQrScanMessage('Tính năng NFC đang được phát triển', isError: false);
        },
      ));
      widgets.add(const SizedBox(height: AppDimensions.spacingXL));
    } else if (groupName == 'loan_proposal') {
      // Add loan type selection at the beginning of loan proposal
      widgets.add(_buildLoanTypeSelection());
      widgets.add(const SizedBox(height: AppDimensions.spacingXL));
    }
    
    // Add regular fields with special handling for address fields
    for (int i = 0; i < fields.length; i++) {
      final field = fields[i];
      
      // Check field visibility
      if (!_formManager.isFieldVisible(field.key)) {
        continue;
      }
      
      // Skip loan_type field as it's handled by _buildLoanTypeSelection()
      if (field.key == 'loan_type') {
        continue;
      }
      
      // Special handling for permanent address section in borrower_info
      if (groupName == 'borrower_info' && field.key == 'borrower_permanent_address') {
        widgets.add(_buildFieldWidget(field));
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
        
        // Add current address same as permanent checkbox
        widgets.add(_buildSwitchField(
          'Địa chỉ hiện tại trùng với địa chỉ thường trú',
          'borrower_current_same_permanent',
          'Đánh dấu nếu địa chỉ hiện tại giống thường trú',
        ));
        
        // Add current address fields if different from permanent
        if (_formManager.getFieldValue<bool>('borrower_current_same_permanent') != true) {
          widgets.add(const SizedBox(height: AppDimensions.spacingL));
          
          // Add current address fields dynamically
          final currentAddressFields = [
            'borrower_current_province',
            'borrower_current_district', 
            'borrower_current_address',
          ];
          
          for (final currentFieldKey in currentAddressFields) {
            final currentField = _formManager.getFieldDefinition(currentFieldKey);
            if (currentField != null) {
              widgets.add(_buildFieldWidget(currentField));
              widgets.add(const SizedBox(height: AppDimensions.spacingL));
            }
          }
        }
        continue;
      }
      
      // Special handling for permanent address section in co_borrower_info
      if (groupName == 'co_borrower_info' && field.key == 'co_borrower_permanent_address') {
        widgets.add(_buildFieldWidget(field));
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
        
        // Add current address same as permanent checkbox
        widgets.add(_buildSwitchField(
          'Địa chỉ hiện tại trùng với địa chỉ thường trú',
          'co_borrower_current_same_permanent',
          'Đánh dấu nếu địa chỉ hiện tại giống thường trú',
        ));
        
        // Add current address fields if different from permanent
        if (_formManager.getFieldValue<bool>('co_borrower_current_same_permanent') != true) {
          widgets.add(const SizedBox(height: AppDimensions.spacingL));
          
          // Add current address fields dynamically
          final currentAddressFields = [
            'co_borrower_current_province',
            'co_borrower_current_district', 
            'co_borrower_current_address',
          ];
          
          for (final currentFieldKey in currentAddressFields) {
            final currentField = _formManager.getFieldDefinition(currentFieldKey);
            if (currentField != null) {
              widgets.add(_buildFieldWidget(currentField));
              widgets.add(const SizedBox(height: AppDimensions.spacingL));
            }
          }
        }
        continue;
      }
      
      // Skip current address fields as they are handled above
      if (field.key.contains('current_') && 
          (field.key.contains('province') || field.key.contains('district') || field.key.contains('address')) &&
          !field.key.contains('same_permanent')) {
        continue;
      }
      
      // Build field widget based on type
      widgets.add(_buildFieldWidget(field));
      
      // Add spacing between fields
      if (i < fields.length - 1) {
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
      }
    }
    
    return widgets;
  }

  /// Build field widget based on field definition
  Widget _buildFieldWidget(ProductFormFieldDefinition field) {
    switch (field.type) {
      case FieldType.text:
        return _buildTextField(field);
      case FieldType.number:
        return _buildNumberField(field);
      case FieldType.amount:
        return _buildAmountField(field);
      case FieldType.date:
        return _buildDateField(field);
      case FieldType.phone:
        return _buildPhoneField(field);
      case FieldType.email:
        return _buildEmailField(field);
      case FieldType.textarea:
        return _buildTextAreaField(field);
      case FieldType.dropdown:
        return _buildDropdownField(field);
      case FieldType.masterDataDropdown:
        return _buildMasterDataDropdown(field);
      case FieldType.provinceDropdown:
        return _buildProvinceDropdown(field);
      case FieldType.wardDropdown:
        return _buildWardDropdown(field);
      case FieldType.collateralDropdown:
        return _buildCollateralDropdown(field);
      case FieldType.radio:
        return _buildRadioField(field);
      case FieldType.checkbox:
        return _buildCheckboxField(field);
      default:
        return _buildTextField(field);
    }
  }

  /// Build text field
  Widget _buildTextField(ProductFormFieldDefinition field) {
    // Check if this field should be read-only
    final isReadOnly = field.key == 'branch_code' || field.key == 'collateral_value_text';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          readOnly: isReadOnly,
          decoration: InputDecoration(
            hintText: isReadOnly ? 'Tự động điền' : (field.hint ?? 'Nhập ${field.label.toLowerCase()}'),
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            filled: isReadOnly,
            fillColor: isReadOnly ? AppColors.backgroundSecondary : null,
          ),
          validator: (value) => _validateField(field, value),
          onChanged: isReadOnly ? null : (value) {
            _formManager.setFieldValue<String>(field.key, value);
            _debouncedUpdateDetails();
          },
        ),
      ],
    );
  }

  /// Build number field
  Widget _buildNumberField(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            hintText: field.hint ?? 'Nhập ${field.label.toLowerCase()}',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: (value) => _validateField(field, value),
          onChanged: (value) {
            _formManager.setFieldValue<String>(field.key, value);
            _debouncedUpdateDetails();
          },
        ),
      ],
    );
  }

  /// Build amount field
  Widget _buildAmountField(ProductFormFieldDefinition field) {
    // Check if this field should be read-only
    final isReadOnly = field.key == 'total_capital_need';
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          keyboardType: TextInputType.number,
          readOnly: isReadOnly,
          inputFormatters: isReadOnly ? [] : [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
          ],
          decoration: InputDecoration(
            hintText: isReadOnly ? 'Tự động tính toán' : (field.hint ?? 'Nhập số tiền'),
            suffixText: 'VND',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            filled: isReadOnly,
            fillColor: isReadOnly ? AppColors.backgroundSecondary : null,
          ),
          validator: (value) => _validateField(field, value),
          onChanged: isReadOnly ? null : (value) {
            _formManager.setFieldValue<String>(field.key, value);
            
            // Trigger calculated fields update
            if (field.key == 'collateral_value') {
              _updateCollateralCalculatedFields(value);
            } else if (field.key == 'own_capital' || field.key == 'loan_amount') {
              _updateTotalCapitalNeed();
            }
            
            _debouncedUpdateDetails();
          },
        ),
        // Show formatted amount
        if (_shouldShowFormattedAmount(field.key))
          Padding(
            padding: EdgeInsets.only(top: AppDimensions.spacingXS),
            child: Text(
              '≈ ${_formatCurrency(_formManager.getFieldValue<String>(field.key) ?? '')} VND',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.kienlongOrange,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  /// Build date field
  Widget _buildDateField(ProductFormFieldDefinition field) {
    // Special debug for co_borrower_birth_date
    if (field.key == 'co_borrower_birth_date') {
      debugPrint('🔍 Building co_borrower_birth_date field:');
      debugPrint('  Initial value: ${_formManager.getFieldValue<String>(field.key)}');
      debugPrint('  Field visible: ${_formManager.isFieldVisible(field.key)}');
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        AppDateField(
          label: '',
          required: false, // Don't show required in AppDateField since we already show it in label
          initialValue: _formManager.getFieldValue<String>(field.key),
          shouldValidate: _isValidating,
          onChanged: (value) {
            if (field.key == 'co_borrower_birth_date') {
              debugPrint('🔍 co_borrower_birth_date onChanged: "$value"');
            }
            _formManager.setFieldValue<String>(field.key, value ?? '');
            _debouncedUpdateDetails();
          },
          validator: (value) => _validateField(field, value),
        ),
      ],
    );
  }

  /// Build phone field
  Widget _buildPhoneField(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          keyboardType: TextInputType.phone,
          inputFormatters: [
            FilteringTextInputFormatter.digitsOnly,
            LengthLimitingTextInputFormatter(10),
          ],
          decoration: InputDecoration(
            hintText: field.hint ?? '0xxxxxxxxx',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: (value) => _validateField(field, value),
          onChanged: (value) {
            _formManager.setFieldValue<String>(field.key, value);
            _debouncedUpdateDetails();
          },
        ),
      ],
    );
  }

  /// Build email field
  Widget _buildEmailField(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          keyboardType: TextInputType.emailAddress,
          decoration: InputDecoration(
            hintText: field.hint ?? '<EMAIL>',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: (value) => _validateField(field, value),
          onChanged: (value) {
            _formManager.setFieldValue<String>(field.key, value);
            _debouncedUpdateDetails();
          },
        ),
      ],
    );
  }

  /// Build textarea field
  Widget _buildTextAreaField(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        TextFormField(
          controller: _getController(field.key),
          maxLines: 3,
          decoration: InputDecoration(
            hintText: field.hint ?? 'Nhập ${field.label.toLowerCase()}',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          validator: (value) => _validateField(field, value),
          onChanged: (value) {
            _formManager.setFieldValue<String>(field.key, value);
            _debouncedUpdateDetails();
          },
        ),
      ],
    );
  }

  /// Build dropdown field
  Widget _buildDropdownField(ProductFormFieldDefinition field) {
    final optionsValue = field.metadata?['options'];
    final options = optionsValue is List ? optionsValue.map((e) => e.toString()).toList() : <String>[];
    
    // Get current value and check if it exists in items
    final currentValue = _formManager.getFieldValue<String>(field.key);
    final availableValues = options.toSet().toList();
    final validValue = availableValues.contains(currentValue) ? currentValue : null;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        DropdownButtonFormField<String>(
          value: validValue,
          decoration: InputDecoration(
            hintText: 'Chọn ${field.label.toLowerCase()}',
            border: InputBorder.none,
            contentPadding: EdgeInsets.all(AppDimensions.paddingM),
          ),
          items: options.map((option) {
            return DropdownMenuItem(
              value: option,
              child: Text(option),
            );
          }).toList(),
          onChanged: (value) {
            _formManager.setFieldValue<String>(field.key, value ?? '');
            _debouncedUpdateDetails();
          },
          validator: (value) => _validateField(field, value),
        ),
      ],
    );
  }

  /// Build master data dropdown
  Widget _buildMasterDataDropdown(ProductFormFieldDefinition field) {
    final configTypeValue = field.metadata?['configType'];
    final configType = configTypeValue is String ? configTypeValue : null;
    if (configType == null) {
      return Text('Error: No configType specified for ${field.key}');
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            List<ConfigModel> configs = [];
            
            if (state is MasterDataLoaded) {
              configs = state.configsByGroup[configType] ?? [];
            }
            
            // Get current value and check if it exists in items
            final currentValue = _formManager.getFieldValue<String>(field.key);
            final availableValues = configs.map((config) => config.id).toSet().toList();
            final validValue = availableValues.contains(currentValue) ? currentValue : null;
            
            return DropdownButtonFormField<String>(
              value: validValue,
              decoration: InputDecoration(
                hintText: 'Chọn ${field.label.toLowerCase()}',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
              ),
              items: configs.map((config) {
                return DropdownMenuItem(
                  value: config.id,
                  child: Text(config.label ?? config.code ?? ''),
                );
              }).toList(),
              onChanged: (value) {
                _formManager.setFieldValue<String>(field.key, value ?? '');
                
                // Special handling for income_source change - trigger rebuild to show/hide fields
                if (field.key == 'income_source') {
                  // Clear dependent fields when income source changes to non-business
                  if (!_isBusinessIncomeSource(value ?? '')) {
                    _formManager.setFieldValue<String>('daily_revenue', '');
                    _formManager.setFieldValue<String>('business_location_province', '');
                    _formManager.setFieldValue<String>('business_location_district', '');
                    _formManager.setFieldValue<String>('business_location_address', '');
                  }
                  
                  // Force rebuild to show/hide conditional fields
                  if (mounted) {
                    setState(() {});
                  }
                }
                
                _debouncedUpdateDetails();
              },
              validator: (value) => _validateField(field, value),
            );
          },
        ),
      ],
    );
  }

  /// Build province dropdown
  Widget _buildProvinceDropdown(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            List<ProvinceModel> provinces = [];
            
            if (state is MasterDataLoaded) {
              provinces = state.provinces;
            }
            
            // Get current value and check if it exists in items
            final currentValue = _formManager.getFieldValue<String>(field.key);
            final availableValues = provinces.map((province) => province.id).toSet().toList();
            final validValue = availableValues.contains(currentValue) ? currentValue : null;
            
            return DropdownButtonFormField<String>(
              value: validValue,
              decoration: InputDecoration(
                hintText: 'Chọn ${field.label.toLowerCase()}',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
              ),
              items: provinces.map((province) {
                return DropdownMenuItem(
                  value: province.id,
                  child: Text(province.name),
                );
              }).toList(),
              onChanged: (value) {
                debugPrint('Province changed: ${field.key} = $value');
                _formManager.setFieldValue<String>(field.key, value ?? '');
                
                // Clear dependent ward field
                final wardKey = field.key.replaceAll('province', 'district');
                debugPrint('Clearing ward field: $wardKey');
                _formManager.setFieldValue<String>(wardKey, '');
                
                // Load wards for selected province
                if (value != null && value.isNotEmpty) {
                  debugPrint('Loading wards for province: $value');
                  context.read<MasterDataBloc>().add(LoadWardsEvent(value));
                  
                  // Trigger rebuild to update ward dropdown
                  if (mounted) {
                    setState(() {});
                  }
                }
                
                _debouncedUpdateDetails();
              },
              validator: (value) => _validateField(field, value),
            );
          },
        ),
      ],
    );
  }

  /// Build ward dropdown
  Widget _buildWardDropdown(ProductFormFieldDefinition field) {
    final dependencies = field.dependencies ?? [];
    // Find the province key from dependencies (look for key containing 'province')
    final provinceKey = dependencies.firstWhere(
      (dep) => dep.contains('province'), 
      orElse: () => dependencies.isNotEmpty ? dependencies.first : ''
    );
    final provinceId = _formManager.getFieldValue<String>(provinceKey) ?? '';
    
    debugPrint('Ward dropdown for ${field.key}: provinceKey=$provinceKey, provinceId=$provinceId');
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          buildWhen: (previous, current) {
            // Rebuild when wards data changes
            return previous != current;
          },
          builder: (context, state) {
            List<WardModel> wards = [];
            
            if (state is MasterDataLoaded && provinceId.isNotEmpty) {
              wards = state.wardsByProvince[provinceId] ?? [];
              debugPrint('Loaded ${wards.length} wards for province $provinceId');
            } else {
              debugPrint('No wards loaded: state=${state.runtimeType}, provinceId=$provinceId');
            }
            
            // Get current value and check if it exists in items
            final currentValue = _formManager.getFieldValue<String>(field.key);
            final availableValues = wards.map((ward) => ward.id).toSet().toList();
            final validValue = availableValues.contains(currentValue) ? currentValue : null;
            
            return DropdownButtonFormField<String>(
              value: validValue,
              decoration: InputDecoration(
                hintText: provinceId.isEmpty ? 'Chọn tỉnh/thành phố trước' : 'Chọn ${field.label.toLowerCase()}',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
              ),
              items: wards.map((ward) {
                return DropdownMenuItem(
                  value: ward.id,
                  child: Text(ward.name ?? ''),
                );
              }).toList(),
              onChanged: provinceId.isEmpty ? null : (value) {
                _formManager.setFieldValue<String>(field.key, value ?? '');
                _debouncedUpdateDetails();
              },
              validator: (value) => _validateField(field, value),
            );
          },
        ),
      ],
    );
  }

  /// Build collateral dropdown
  Widget _buildCollateralDropdown(ProductFormFieldDefinition field) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        BlocBuilder<MasterDataBloc, MasterDataState>(
          builder: (context, state) {
            List<CollateralCategoryModel> categories = [];
            
            if (state is MasterDataLoaded) {
              categories = state.collateralCategories;
            }
            
            // Get current value and check if it exists in items
            final currentValue = _formManager.getFieldValue<String>(field.key);
            final availableValues = categories.map((category) => category.id).toSet().toList();
            final validValue = availableValues.contains(currentValue) ? currentValue : null;
            
            return DropdownButtonFormField<String>(
              value: validValue,
              decoration: InputDecoration(
                hintText: 'Chọn ${field.label.toLowerCase()}',
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
              ),
              items: categories.map((category) {
                return DropdownMenuItem(
                  value: category.id,
                  child: Text(category.name ?? ''),
                );
              }).toList(),
              onChanged: (value) {
                _formManager.setFieldValue<String>(field.key, value ?? '');
                _debouncedUpdateDetails();
              },
              validator: (value) => _validateField(field, value),
            );
          },
        ),
      ],
    );
  }

  /// Build radio field
  Widget _buildRadioField(ProductFormFieldDefinition field) {
    final optionsValue = field.metadata?['options'];
    final options = optionsValue is List ? optionsValue.map((e) => e.toString()).toList() : <String>[];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildFieldLabel(field),
        const SizedBox(height: AppDimensions.spacingXS),
        Wrap(
          spacing: AppDimensions.spacingS,
          children: options.map((option) {
            return SizedBox(
              width: (MediaQuery.of(context).size.width - AppDimensions.paddingM * 4) / options.length,
              child: RadioListTile<String>(
                title: Text(
                  option,
                  style: Theme.of(context).textTheme.bodySmall,
                  overflow: TextOverflow.ellipsis,
                ),
                value: option,
                groupValue: _formManager.getFieldValue<String>(field.key),
                onChanged: (value) {
                  _formManager.setFieldValue<String>(field.key, value ?? '');
                  _debouncedUpdateDetails();
                },
                contentPadding: EdgeInsets.zero,
                dense: true,
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Build checkbox field
  Widget _buildCheckboxField(ProductFormFieldDefinition field) {
    return _buildSwitchField(
      field.label,
      field.key,
      field.hint ?? 'Đánh dấu nếu áp dụng',
    );
  }

  /// Build switch field
  Widget _buildSwitchField(String label, String key, String description) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingXS),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _formManager.getFieldValue<bool>(key) ?? false,
            onChanged: (value) {
              _formManager.setFieldValue<bool>(key, value);
              _debouncedUpdateDetails();
            },
            activeColor: AppColors.kienlongOrange,
          ),
        ],
      ),
    );
  }

  /// Build field label
  Widget _buildFieldLabel(ProductFormFieldDefinition field) {
    return Row(
      children: [
        Text(
          field.label,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (field.isRequired) ...[
            const SizedBox(width: AppDimensions.spacingXS),
          Text(
            '*',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.error,
            ),
          ),
        ],
      ],
    );
  }

  /// Build expandable card
  Widget _buildExpandableCard(
    String title,
    IconData icon,
    Color color,
    List<Widget> children,
    String expandedKey,
  ) {
    final isExpanded = _formManager.getFieldValue<bool>(expandedKey) ?? true;
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              _formManager.setFieldValue<bool>(expandedKey, !isExpanded);
              _debouncedUpdateDetails();
            },
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(AppDimensions.radiusM),
              topRight: Radius.circular(AppDimensions.radiusM),
            ),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color, size: AppDimensions.iconM),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      TablerIcons.chevron_down,
                      color: color,
                      size: AppDimensions.iconS,
                    ),
                  ),
                ],
              ),
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: isExpanded ? null : 0,
            child: isExpanded
                ? Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: children,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }

  /// Validate field using ProductFormValidator
  String? _validateField(ProductFormFieldDefinition field, String? value) {
    if (!_isValidating) return null;
    
    final errors = _validator.validateField(field.key);
    return errors.isNotEmpty ? errors.first : null;
  }

  /// Check if formatted amount should be shown
  bool _shouldShowFormattedAmount(String key) {
    final value = _formManager.getFieldValue<String>(key);
    if (value == null || value.isEmpty) return false;
    
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return false;
    
    final amount = int.tryParse(cleanValue) ?? 0;
    return amount > 0;
  }

  /// Update collateral calculated fields
  void _updateCollateralCalculatedFields(String value) {
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    final textValue = _convertNumberToWords(cleanValue);
    
    _formManager.setFieldValue<String>('collateral_value_text', textValue);
    _formManager.setFieldValue<String>('total_collateral_value', cleanValue);
    
    // Update controllers to display in TextFields
    _updateController('collateral_value_text', textValue);
    _updateController('total_collateral_value', cleanValue);
  }

  // ===== QR/NFC SCANNING METHODS =====

  /// Mở QR scanner để quét mã QR từ CCCD cho người vay chính
  void _openQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleQrScanResult,
        onError: _handleQrScanError,
        instructionText: 'Hướng camera vào mã QR trên CCCD hoặc chọn ảnh từ thư viện',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Mở QR scanner để quét mã QR từ CCCD cho người đồng vay
  void _openCoBorrowerQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleCoBorrowerQrScanResult,
        onError: _handleQrScanError,
        instructionText: 'Hướng camera vào mã QR trên CCCD của người đồng vay',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Mở QR scanner để quét mã QR từ giấy đăng ký xe
  void _openVehicleQrScanner() {
    showDialog(
      context: context,
      builder: (context) => QrScannerWidget(
        onQrDetected: _handleVehicleQrScanResult,
        onError: _handleQrScanError,
        instructionText: 'Hướng camera vào mã QR trên giấy đăng ký xe',
        scanMode: QrScanMode.continuous,
      ),
    );
  }

  /// Xử lý kết quả scan QR cho người vay chính
  void _handleQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('QR scan completed with ${results.length} results');
      
      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }
      
      final qrResult = results.first;
      debugPrint('QR content: ${qrResult.value}');
      
      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      _showQrScanMessage('Đã quét thành công mã QR từ CCCD');
      
      // Parse QR data và mapping vào form
      await _parseQrAndMapToForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR', isError: true);
    }
  }

  /// Xử lý kết quả scan QR cho người đồng vay
  void _handleCoBorrowerQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Co-borrower QR scan completed with ${results.length} results');
      
      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }
      
      final qrResult = results.first;
      debugPrint('Co-borrower QR content: ${qrResult.value}');
      
      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      _showQrScanMessage('Đã quét thành công mã QR từ CCCD người đồng vay');
      
      // Parse QR data và mapping vào form
      await _parseQrAndMapToCoBorrowerForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling co-borrower QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR người đồng vay', isError: true);
    }
  }

  /// Xử lý kết quả scan QR cho giấy đăng ký xe
  void _handleVehicleQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Vehicle QR scan completed with ${results.length} results');
      
      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }
      
      final qrResult = results.first;
      debugPrint('Vehicle QR content: ${qrResult.value}');
      
      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }
      
      _showQrScanMessage('Đã quét thành công mã QR từ giấy đăng ký xe');
      
      // Parse QR data và mapping vào form
      await _parseQrAndMapToVehicleForm(qrResult.value);
    } catch (e) {
      debugPrint('Error handling vehicle QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR giấy đăng ký xe', isError: true);
    }
  }

  /// Xử lý lỗi scan QR
  void _handleQrScanError(String error) async {
    debugPrint('QR scan error: $error');
    _showQrScanMessage('Lỗi quét QR: $error', isError: true);
  }

  /// Hiển thị thông báo kết quả scan QR
  void _showQrScanMessage(String message, {bool isError = false}) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Parse QR data và mapping vào form fields cho người vay chính
  Future<void> _parseQrAndMapToForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToForm ===');
      debugPrint('QR Data: $qrData');
      
      // Parse CCCD QR data using util
      final qrFormData = QrUtil.parseCccdQrData(qrData);
      debugPrint('Parsed QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('QR data parsed successfully, mapping to form...');
        
        // Map QR data to form using utility
        final formData = _formManager.formData;
        QrUtil.mapCccdToBorrowerForm(qrFormData, formData);
        
        // Update form manager with new data
        _formManager.updateFromFormData(formData);
        
        // Update controllers with new values
        for (final key in qrFormData.keys) {
          if (_controllers.containsKey(key)) {
                _updateController(key, formData[key]?.toString() ?? '');
          }
        }
        
        debugPrint('QR data mapped successfully to borrower form');
      } else {
        debugPrint('QR data parsing failed, showing error dialog');
        
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToForm ===');
    } catch (e) {
      debugPrint('Error processing QR data: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Parse QR data và mapping vào form fields cho người đồng vay
  Future<void> _parseQrAndMapToCoBorrowerForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToCoBorrowerForm ===');
      debugPrint('Co-borrower QR Data: $qrData');
      
      // Parse CCCD QR data using util
      final qrFormData = QrUtil.parseCccdQrData(qrData);
      debugPrint('Parsed Co-borrower QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('Co-borrower QR data parsed successfully, mapping to form...');
        
        // Map QR data to form using utility
        final formData = _formManager.formData;
        QrUtil.mapCccdToCoBorrowerForm(qrFormData, formData);
        
        // Update form manager with new data
        _formManager.updateFromFormData(formData);
        
        // Update controllers with new values
        for (final entry in qrFormData.entries) {
          final coBorrowerKey = 'co_borrower_${entry.key.replaceAll('borrower_', '')}';
          if (_controllers.containsKey(coBorrowerKey)) {
            _updateController(coBorrowerKey, formData[coBorrowerKey]?.toString() ?? '');
          }
        }
        
        debugPrint('QR data mapped successfully to co-borrower form');
      } else {
        debugPrint('Co-borrower QR data parsing failed, showing error dialog');
        
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToCoBorrowerForm ===');
    } catch (e) {
      debugPrint('Error processing QR data for co-borrower: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Parse QR data và mapping vào form fields cho giấy đăng ký xe
  Future<void> _parseQrAndMapToVehicleForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToVehicleForm ===');
      debugPrint('Vehicle QR Data: $qrData');
      
      // Validate QR data first
      if (!VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData)) {
        debugPrint('Invalid vehicle QR data format');
        _showQrScanMessage('Mã QR không hợp lệ hoặc thiếu thông tin cần thiết', isError: true);
        return;
      }
      
      // Parse vehicle QR data using VehicleQRBasicUtil
      final qrFormData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
      debugPrint('Parsed Vehicle QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('Vehicle QR data parsed successfully, mapping to form...');
        
        // Map vehicle data to form
        final formData = _formManager.formData;
        
        // Map vehicle identification fields
        if (qrFormData['vehicle_plate_number']?.isNotEmpty == true) {
          formData['vehicle_plate_number'] = qrFormData['vehicle_plate_number'];
        }
        if (qrFormData['vehicle_frame_number']?.isNotEmpty == true) {
          formData['vehicle_frame_number'] = qrFormData['vehicle_frame_number'];
        }
        if (qrFormData['vehicle_engine_number']?.isNotEmpty == true) {
          formData['vehicle_engine_number'] = qrFormData['vehicle_engine_number'];
        }
        
        // Map vehicle details
        if (qrFormData['vehicle_name']?.isNotEmpty == true) {
          formData['vehicle_name'] = qrFormData['vehicle_name'];
        }
        if (qrFormData['vehicle_registration_number']?.isNotEmpty == true) {
          formData['vehicle_registration_number'] = qrFormData['vehicle_registration_number'];
        }
        if (qrFormData['vehicle_registration_place']?.isNotEmpty == true) {
          formData['vehicle_registration_place'] = qrFormData['vehicle_registration_place'];
        }
        if (qrFormData['vehicle_registration_date']?.isNotEmpty == true) {
          formData['vehicle_registration_date'] = qrFormData['vehicle_registration_date'];
        }
        if (qrFormData['collateral_owner']?.isNotEmpty == true) {
          formData['collateral_owner'] = qrFormData['collateral_owner'];
        }
        
        // Update form manager with new data
        _formManager.updateFromFormData(formData);
        
        // Update controllers with new values
        for (final key in qrFormData.keys) {
          if (_controllers.containsKey(key)) {
                _updateController(key, formData[key]?.toString() ?? '');
          }
        }
        
        debugPrint('Vehicle QR data mapped successfully to form');
        _showQrScanMessage('Đã quét và điền thông tin xe thành công');
        
      } else {
        debugPrint('Vehicle QR data parsing failed, showing error dialog');
        
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToVehicleForm ===');
    } catch (e) {
      debugPrint('Error processing vehicle QR data: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Hiển thị dialog lỗi parse QR
  void _showQrParseErrorDialog(String qrData) {
    if (!mounted) return;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_triangle,
              color: AppColors.warning,
            ),
            const SizedBox(width: AppDimensions.spacingS),
            const Text('Không thể đọc QR'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Không thể đọc thông tin từ mã QR này. Vui lòng thử lại hoặc nhập thông tin thủ công.',
            ),
            const SizedBox(height: AppDimensions.spacingL),
            ExpansionTile(
              title: const Text('Chi tiết dữ liệu QR'),
              children: [
                Text(
                  'Dữ liệu QR: ${qrData.length > 100 ? '${qrData.substring(0, 100)}...' : qrData}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontFamily: 'monospace',
                  ),
                ),
              ],
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  /// Build QR/NFC scanning buttons
  Widget _buildQrNfcScanningButtons(String title, VoidCallback onQrTap, VoidCallback onNfcTap) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.kienlongOrange,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Row(
          children: [
            Expanded(
              child: _buildScanButton(
                'Quét QR',
                TablerIcons.qrcode,
                AppColors.kienlongOrange,
                onQrTap,
              ),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildScanButton(
                'Quét NFC',
                TablerIcons.nfc,
                AppColors.kienlongSkyBlue,
                onNfcTap,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build individual scan button
  Widget _buildScanButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: AppDimensions.iconS),
            const SizedBox(width: AppDimensions.spacingS),
            Flexible(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build loan type selection (Có TSBĐ / Không TSBĐ)
  Widget _buildLoanTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Hình thức vay vốn',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.success,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingL),
        Row(
          children: [
            Expanded(
              child: _buildRadioTile('Có TSBĐ', 'loan_type', 'Có TSBĐ'),
            ),
            const SizedBox(width: AppDimensions.spacingM),
            Expanded(
              child: _buildRadioTile('Không TSBĐ', 'loan_type', 'Không TSBĐ'),
            ),
          ],
        ),
      ],
    );
  }

  /// Build radio tile for loan type selection
  Widget _buildRadioTile(String title, String groupKey, String value) {
    final isSelected = _formManager.getFieldValue<String>(groupKey) == value;
    
    return InkWell(
      onTap: () {
        _formManager.setFieldValue<String>(groupKey, value);
        _debouncedUpdateDetails();
      },
      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      child: Container(
        padding: EdgeInsets.all(AppDimensions.paddingM),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.success.withValues(alpha: 0.1)
              : Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          border: Border.all(
            color: isSelected 
                ? AppColors.success 
                : AppColors.borderLight,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? TablerIcons.circle_check_filled : TablerIcons.circle,
              color: isSelected ? AppColors.success : AppColors.textSecondary,
              size: AppDimensions.iconS,
            ),
            const SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isSelected ? AppColors.success : AppColors.textPrimary,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build basic collateral fields
  List<Widget> _buildBasicCollateralFields(List<ProductFormFieldDefinition> fields) {
    List<Widget> widgets = [];
    
    // Basic collateral fields (no QR scanner here, no total_collateral_value)
    final basicFields = [
      'collateral_type_id',
      'collateral_value',
      'collateral_value_text',
      'collateral_condition',
      'collateral_owner',
      'collateral_owner_birth_year',
    ];
    
    for (final fieldKey in basicFields) {
      final field = _formManager.getFieldDefinition(fieldKey);
      if (field != null && _formManager.isFieldVisible(field.key)) {
        widgets.add(_buildFieldWidget(field));
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
      }
    }
    
    return widgets;
  }

  /// Build detailed collateral fields
  List<Widget> _buildDetailedCollateralFields(List<ProductFormFieldDefinition> fields) {
    List<Widget> widgets = [];
    
    // Add QR scanning button at the top (only QR, no NFC)
    widgets.add(_buildVehicleQrScanningButton());
    widgets.add(const SizedBox(height: AppDimensions.spacingL));
    
    // Detailed collateral fields (vehicle specific)
    final detailedFields = [
      'vehicle_name',
      'vehicle_plate_number',
      'vehicle_frame_number',
      'vehicle_engine_number',
      'vehicle_registration_number',
      'vehicle_registration_place',
      'vehicle_registration_date',
      'vehicle_condition_at_handover',
    ];
    
    for (final fieldKey in detailedFields) {
      final field = _formManager.getFieldDefinition(fieldKey);
      if (field != null && _formManager.isFieldVisible(field.key)) {
        widgets.add(_buildFieldWidget(field));
        widgets.add(const SizedBox(height: AppDimensions.spacingL));
      }
    }
    
    // Add total collateral value at the end
    final totalCollateralField = _formManager.getFieldDefinition('total_collateral_value');
    if (totalCollateralField != null && _formManager.isFieldVisible(totalCollateralField.key)) {
      widgets.add(_buildFieldWidget(totalCollateralField));
      widgets.add(const SizedBox(height: AppDimensions.spacingL));
    }
    
    return widgets;
  }

  /// Build vehicle QR scanning button (only QR, no NFC)
  Widget _buildVehicleQrScanningButton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quét thông tin từ giấy đăng ký xe',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.kienlongOrange,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingS),
        SizedBox(
          width: double.infinity,
          child: _buildScanButton(
            'Quét QR',
            TablerIcons.qrcode,
            AppColors.kienlongOrange,
            _openVehicleQrScanner,
          ),
        ),
      ],
    );
  }
}
