import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../models/form_data/form_data.dart';
import '../blocs/transaction_form_bloc.dart';
import 'forms/installment_loan_form_widget.dart';

/// New Product Details Step V3 - Using Typed Form Data
/// Sử dụng typed form data và tích hợp với các form widget chuyên biệt
class NewProductDetailsStepV3 extends StatefulWidget {
  final ProductModel? product;
  final CustomerModel? selectedCustomer;
  // REMOVE: final Function(Map<String, dynamic>) onDetailsChanged;
  // REMOVE: final Function(bool)? onValidationChanged; // Deprecated - sẽ remove sau

  const NewProductDetailsStepV3({
    super.key,
    required this.product,
    this.selectedCustomer,
    // REMOVE: required this.onDetailsChanged,
    // REMOVE: this.onValidationChanged,
  });

  @override
  State<NewProductDetailsStepV3> createState() => _NewProductDetailsStepV3State();
}

class _NewProductDetailsStepV3State extends State<NewProductDetailsStepV3> {

  @override
  Widget build(BuildContext context) {
    if (widget.product == null) {
      return const SizedBox();
    }

    return _buildFormWidget();
  }

  /// Build appropriate form widget based on product type
  Widget _buildFormWidget() {
    final productCode = widget.product!.code;
    
    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        return _buildInstallmentLoanForm();
      case 'PERSONAL_LOAN':
        return _buildPersonalLoanForm();
      default:
        return _buildErrorWidget('Sản phẩm không được hỗ trợ: $productCode');
    }
  }

  /// Build installment loan form with BlocBuilder
  Widget _buildInstallmentLoanForm() {
    return BlocBuilder<TransactionFormBloc, TransactionFormState>(
      buildWhen: (previous, current) {
        // Only rebuild when product or customer changes (affects which form widget to show)
        // DO NOT rebuild when form data changes - let the form widget handle its own updates
        if (previous is TransactionFormLoaded && current is TransactionFormLoaded) {
          // Check if product changed (affects which form widget to render)
          if (previous.selectedProduct?.id != current.selectedProduct?.id) {
            debugPrint('🔄 NewProductDetailsStepV3 rebuilding: product changed');
            return true;
          }
          
          // Check if customer changed (affects initial form data)
          if (previous.selectedCustomer?.id != current.selectedCustomer?.id) {
            debugPrint('🔄 NewProductDetailsStepV3 rebuilding: customer changed');
            return true;
          }
          
          // DO NOT rebuild when formData changes - form widget handles its own updates
          debugPrint('⏭️ NewProductDetailsStepV3 skipping rebuild: formData changed but not rebuilding');
          return false;
        }
        
        // Always rebuild for state type changes
        return true;
      },
      builder: (context, state) {
        if (state is TransactionFormLoaded) {
          // Get typed form data from bloc (only when product/customer changes)
          InstallmentLoanFormData formData = InstallmentLoanFormData();
          
          if (state.formData is InstallmentLoanFormData) {
            formData = state.formData as InstallmentLoanFormData;
            debugPrint('✅ Using existing InstallmentLoanFormData from bloc state');
          } else if (state.formData != null) {
            // Should not happen, but handle gracefully
            debugPrint('⚠️ FormData exists but is not InstallmentLoanFormData: ${state.formData.runtimeType}');
            formData = InstallmentLoanFormData();
          } else {
            // No form data exists, create new
            debugPrint('📝 Creating new InstallmentLoanFormData');
            formData = InstallmentLoanFormData();
          }
          
          return InstallmentLoanFormWidget(
            formData: formData,
            selectedCustomer: widget.selectedCustomer,
            onChanged: (newFormData) {
              // Dispatch typed event to TransactionFormBloc
              context.read<TransactionFormBloc>().add(UpdateFormData(newFormData));
            },
          );
        }
        
        // Fallback for initial state
        return InstallmentLoanFormWidget(
          formData: InstallmentLoanFormData(),
          selectedCustomer: widget.selectedCustomer,
          onChanged: (newFormData) {
            context.read<TransactionFormBloc>().add(UpdateFormData(newFormData));
          },
        );
      },
    );
  }


  /// Build personal loan form (placeholder for now)
  Widget _buildPersonalLoanForm() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '📋 Thông tin Vay cá nhân',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Vui lòng điền đầy đủ thông tin để tạo giao dịch vay cá nhân',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingXL),
          
          // TODO: Implement PersonalLoanFormWidget
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingL),
            decoration: BoxDecoration(
              color: AppColors.warning.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              border: Border.all(color: AppColors.warning),
            ),
            child: Row(
              children: [
                Icon(
                  TablerIcons.info_circle,
                  color: AppColors.warning,
                  size: AppDimensions.iconM,
                ),
                SizedBox(width: AppDimensions.spacingM),
                Expanded(
                  child: Text(
                    'PersonalLoanFormWidget chưa được implement. Sẽ được phát triển trong tương lai.',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.warning,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_triangle,
              size: 64,
              color: AppColors.error,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Có lỗi xảy ra',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.spacingL),
            ElevatedButton.icon(
              onPressed: () {
                setState(() {
                  // Force rebuild
                });
              },
              icon: Icon(TablerIcons.refresh),
              label: Text('Thử lại'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.kienlongOrange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
