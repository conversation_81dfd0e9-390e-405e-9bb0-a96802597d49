import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import 'package:kiloba_biz/shared/models/bank_account_model.dart';
import '../../../core/theme/index.dart';
import '../../../shared/widgets/index.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/customers/models/customer_model.dart';
import '../../auth/blocs/master_data_bloc.dart';
import '../../auth/services/auth_service.dart';
import '../../../shared/models/config_model.dart';
import '../../../shared/models/province_model.dart';
import '../../../shared/models/ward_model.dart';
import '../../../shared/models/collateral_category_model.dart';
import '../../../shared/constants/config_types.dart';
import '../../auth/services/qr_scanner_service.dart';
import '../../../shared/utils/index.dart';

class ProductDetailsStep extends StatefulWidget {
  final ProductModel? product;
  final CustomerModel? selectedCustomer;
  final Map<String, dynamic> details;
  final Function(Map<String, dynamic>) onDetailsChanged;

  const ProductDetailsStep({
    super.key,
    required this.product,
    this.selectedCustomer,
    required this.details,
    required this.onDetailsChanged,
  });

  @override
  State<ProductDetailsStep> createState() => ProductDetailsStepState();
}

class ProductDetailsStepState extends State<ProductDetailsStep> 
    with AutomaticKeepAliveClientMixin {
  final _formKey = GlobalKey<FormState>();
  late Map<String, dynamic> _formData;
  bool _isValidating = false;
  final ScrollController _scrollController = ScrollController();
  
  // GlobalKeys for each TextField to enable scrolling to specific fields
  final Map<String, GlobalKey> _fieldKeys = {};
  
  // Debounce timer for form updates
  Timer? _debounceTimer;
  
  // TextEditingControllers for form fields - much more efficient than rebuild counter
  final Map<String, TextEditingController> _controllers = {};
  
  
  // Master data state variables
  List<ProvinceModel> _provinces = [];
  final List<ConfigModel> _loanTerms = [];
  final List<ConfigModel> _loanPurposes = [];
  final List<ConfigModel> _incomeSources = [];
  
  // Auth service for getting branch info
  final AuthService _authService = AuthService();
  
  // Note: Loading state is managed by BlocListener

  @override
  void initState() {
    super.initState();
    _formData = Map.from(widget.details);
    // Initialize defaults immediately for first render, then update if needed
    if (widget.product != null && _formData.isEmpty) {
      _setDefaults();
    }
    // Map customer data if available
    if (widget.selectedCustomer != null) {
      _mapCustomerToFormData();
    }
    // Defer state update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _loadMasterData();
        _updateDetails();
      }
    });
  }
  
  /// Load master data for transaction forms
  void _loadMasterData() {
    debugPrint('=== START: Loading master data ===');
    
    // Load provinces for address dropdowns
    context.read<MasterDataBloc>().add(const LoadProvincesEvent());
    
    // Load transaction-related configs
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_TERM_DAYS));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_PURPOSE));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.INCOME_SOURCE));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.ASSET_CONDITION));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.LOAN_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.REPAYMENT_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.DISBURSEMENT_METHOD));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.HANDOVER_CONDITION));
    
    // Load personal info related configs
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.SEX));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.MARITAL_STATUS));
    context.read<MasterDataBloc>().add(const LoadConfigEvent(ConfigTypes.ID_CARD_TYPE));
    
    // Load bank accounts for selected customer
    debugPrint('Calling _loadBankAccountsForSelectedCustomer()...');
    _loadBankAccountsForSelectedCustomer();
    
    // Load collateral categories for MANGO product
    debugPrint('Product code: ${widget.product?.code}');
    if (widget.product?.code == 'MANGO') {
      debugPrint('Loading collateral categories for MANGO product');
      context.read<MasterDataBloc>().add(LoadCollateralCategoriesEvent());
    } else {
      debugPrint('Not loading collateral categories for product: ${widget.product?.code}');
    }
    
    // Test: Always load collateral categories for debugging
    debugPrint('TEST: Loading collateral categories for all products');
    context.read<MasterDataBloc>().add(LoadCollateralCategoriesEvent());
    
    debugPrint('=== END: Loading master data ===');
  }

  @override
  void didUpdateWidget(ProductDetailsStep oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Với AutomaticKeepAliveClientMixin, chỉ cần sync data khi product hoặc customer thay đổi
    if (widget.product != oldWidget.product || widget.selectedCustomer != oldWidget.selectedCustomer) {
      _formData = Map.from(widget.details);
      if (_formData.isEmpty) {
        _setDefaults();
      }
      // Map customer data if available and different from previous
      if (widget.selectedCustomer != null && widget.selectedCustomer != oldWidget.selectedCustomer) {
        _mapCustomerToFormData();
      }
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _updateDetails();
        }
      });
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _debounceTimer?.cancel();
    // Dispose all text controllers
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
    // Clear caches to prevent memory leaks
    _cachedProvinces = null;
    _cachedRequiredFields = null;
    _formattedCurrencyCache.clear();
    _numberToWordsCache.clear();
    super.dispose();
  }

  /// Get or create TextEditingController for a field key
  TextEditingController _getController(String key) {
    if (!_controllers.containsKey(key)) {
      _controllers[key] = TextEditingController(text: _formData[key]?.toString() ?? '');
      
      // Add listener to sync with _formData
      _controllers[key]!.addListener(() {
        final controllerText = _controllers[key]!.text;
        final cleanValue = controllerText.replaceAll(RegExp(r'[^\d]'), '');
        
        // Remove leading zeros safely
        String finalValue = '';
        if (cleanValue.isNotEmpty) {
          try {
            finalValue = int.parse(cleanValue).toString();
          } catch (e) {
            finalValue = cleanValue; // Fallback to original if parsing fails
          }
        }
        
        if (_formData[key] != finalValue) {
          _formData[key] = finalValue;
          _debouncedUpdateDetails();
        }
      });
    }
    return _controllers[key]!;
  }
  
  /// Update controller value when _formData changes externally (like QR parsing)
  void _updateController(String key, String value) {
    final controller = _getController(key);
    
    // For amount fields, remove leading zeros
    String processedValue = value;
    if (key == 'collateral_value' || key == 'loan_amount' || key == 'own_capital') {
      final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
      if (cleanValue.isNotEmpty) {
        try {
          processedValue = int.parse(cleanValue).toString();
        } catch (e) {
          processedValue = cleanValue;
        }
      } else {
        processedValue = '';
      }
    }
    
    if (controller.text != processedValue) {
      controller.text = processedValue;
    }
  }
  
  /// Update all controllers when form data changes externally
  void _updateAllControllers() {
    for (final entry in _formData.entries) {
      if (entry.value is String && _controllers.containsKey(entry.key)) {
        _updateController(entry.key, entry.value.toString());
      }
    }
  }
  
  /// Get config type for dropdown key - for optimized BlocBuilder
  String? _getConfigTypeForKey(String key) {
    switch (key) {
      case 'loan_term':
        return ConfigTypes.LOAN_TERM_DAYS;
      case 'loan_purpose':
        return ConfigTypes.LOAN_PURPOSE;
      case 'income_source':
        return ConfigTypes.INCOME_SOURCE;
      case 'vehicle_condition_at_handover':
        return ConfigTypes.HANDOVER_CONDITION;
      case 'loan_method':
        return ConfigTypes.LOAN_METHOD;
      case 'repayment_method':
        return ConfigTypes.REPAYMENT_METHOD;
      case 'disbursement_method':
        return ConfigTypes.DISBURSEMENT_METHOD;
      case 'handover_condition':
        return ConfigTypes.HANDOVER_CONDITION;
      case 'borrower_gender':
      case 'co_borrower_gender':
        return ConfigTypes.SEX;
      case 'borrower_marital_status':
      case 'co_borrower_marital_status':
        return ConfigTypes.MARITAL_STATUS;
      case 'borrower_id_type':
      case 'co_borrower_id_type':
        return ConfigTypes.ID_CARD_TYPE;
      default:
        return null;
    }
  }
  
  /// Helper method to compare lists for BlocBuilder optimization
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    if (identical(a, b)) return true; // Same reference
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
  
  /// Memoized province list to avoid repeated processing
  List<ProvinceModel>? _cachedProvinces;
  List<ProvinceModel> _getCachedProvinces(List<ProvinceModel> provinces) {
    if (_cachedProvinces == null || !_listEquals(_cachedProvinces, provinces)) {
      _cachedProvinces = List.from(provinces);
    }
    return _cachedProvinces!;
  }
  
  /// Memoized required fields to avoid repeated computation
  String? _cachedProductCode;
  List<String>? _cachedRequiredFields;
  List<String> _getCachedRequiredFields() {
    final currentProductCode = widget.product?.code;
    if (_cachedProductCode != currentProductCode || _cachedRequiredFields == null) {
      _cachedProductCode = currentProductCode;
      _cachedRequiredFields = _getRequiredFields();
    }
    return _cachedRequiredFields!;
  }
  
  /// Memoized currency formatting to avoid repeated computation
  final Map<String, String> _formattedCurrencyCache = {};
  String _getCachedFormattedCurrency(String value) {
    if (_formattedCurrencyCache.containsKey(value)) {
      return _formattedCurrencyCache[value]!;
    }
    final formatted = _formatCurrency(value);
    _formattedCurrencyCache[value] = formatted;
    return formatted;
  }

  void _setDefaults() {
    if (widget.product != null) {
      switch (widget.product!.code) {
        case 'GOLD_LOAN':
        case 'MANGO':
          _formData = {
            // Borrower Information - ensure all fields are present with empty defaults
            // Let UI/UX validation handle required field validation
            'borrower_name': '',
            'borrower_id_number': '',
            'borrower_id_type': '',
            'borrower_id_issue_date': '',
            'borrower_id_expiry_date': '',
            'borrower_id_issue_place': '',
            'borrower_birth_date': '',
            'borrower_gender': '',
            'borrower_permanent_address': '',
            'borrower_permanent_province': '',
            'borrower_permanent_district': '',
            'borrower_current_address': '',
            'borrower_current_province': '',
            'borrower_current_district': '',
            'borrower_current_same_permanent': true,
            'borrower_marital_status': '',
            'borrower_phone': '',
            
            // Co-borrower Information
            'has_co_borrower': true,
            'co_borrower_name': '',
            'co_borrower_id_number': '',
            'co_borrower_id_type': '',
            'co_borrower_id_issue_date': '',
            'co_borrower_id_expiry_date': '',
            'co_borrower_id_issue_place': '',
            'co_borrower_birth_date': '',
            'co_borrower_gender': '',
            'co_borrower_permanent_address': '',
            'co_borrower_permanent_province': '',
            'co_borrower_permanent_district': '',
            'co_borrower_current_address': '',
            'co_borrower_current_province': '',
            'co_borrower_current_district': '',
            'co_borrower_current_same_permanent': true,
            'co_borrower_marital_status': '',
            'co_borrower_phone': '',
            
            // Loan Proposal - Default values according to SRS
            'loan_type': 'Có TSĐB',                    // Mặc định có TSBĐ (theo SRS)
            'own_capital': '0',                        // Số tiền mặc định 0 VND (theo SRS)
            'loan_amount': '',
            'loan_term': '60',                         // Mặc định 60 ngày (theo SRS)
            'total_capital_need': '',
            'branch_code': '',
            'loan_method': '',                         // Sẽ được set tự động từ master data
            'loan_purpose': '',
            'loan_purpose_other': '',
            'repayment_method': '',                       // Sẽ được set tự động từ master data
            'disbursement_method': '',                 // Sẽ được set tự động từ master data
            'disbursement_account': '',
            
            // Financial Information - Default values according to SRS
            'income_source': 'Kinh doanh',             // Mặc định: Kinh doanh (theo SRS)
            'daily_revenue': '0',                      // Số tiền mặc định 0 VND (theo SRS)
            'daily_income': '0',                       // Số tiền mặc định 0 VND (theo SRS)
            'business_location_province': '',
            'business_location_district': '',
            'business_location_address': '',
            
            // Collateral Information - Default values according to SRS
            'collateral_type': 'Mô tô/xe máy',          // Mặc định là Mô tô/xe máy (theo SRS)
            'collateral_value': '0',                   // Số tiền mặc định 0 VND (theo SRS)
            'collateral_value_text': '',
            'collateral_condition': '',
            'collateral_owner': '',
            'collateral_owner_birth_year': '',
            
            // Collateral Details - Default values according to SRS
            'vehicle_plate_number': '',
            'vehicle_name': '',
            'vehicle_frame_number': '',
            'vehicle_engine_number': '',
            'vehicle_registration_number': '',
            'vehicle_registration_place': '',
            'vehicle_registration_date': '',
            'vehicle_condition_at_handover': '',         // Sẽ được set tự động từ master data
            'total_collateral_value': '0',              // Số tiền mặc định 0 VND (theo SRS)
            
            // Card expanded states
            'borrower_card_expanded': true,
            'co_borrower_card_expanded': true,
            'loan_proposal_card_expanded': true,
            'financial_card_expanded': true,
            'collateral_card_expanded': true,
          };
          break;
        case 'PERSONAL_LOAN':
          _formData = {
            'amount': '',
            'term': '24',
            'purpose': 'Tiêu dùng cá nhân',
            'interestRate': '12',
          };
          break;
        case 'MORTGAGE_LOAN':
          _formData = {
            'amount': '',
            'term': '120',
            'purpose': 'Mua nhà',
            'interestRate': '10',
            'collateralType': 'Bất động sản',
          };
          break;
        case 'AUTO_LOAN':
          _formData = {
            'amount': '',
            'term': '60',
            'purpose': 'Mua xe',
            'interestRate': '8',
            'collateralType': 'Xe ô tô',
          };
          break;
        case 'BUSINESS_LOAN':
          _formData = {
            'amount': '',
            'term': '36',
            'purpose': 'Kinh doanh',
            'interestRate': '9',
            'collateralType': 'Tài sản kinh doanh',
          };
          break;
        default:
          _formData = {};
      }
    }
  }

  /// Map thông tin từ CustomerModel vào form data cho phần thông tin người vay chính
  void _mapCustomerToFormData() {
    if (widget.selectedCustomer == null) return;
    
    final customer = widget.selectedCustomer!;
    debugPrint('=== START: Mapping customer data to form ===');
    debugPrint('Customer: ${customer.fullName} (${customer.id})');
    
    // Map thông tin cơ bản
    if (customer.fullName.isNotEmpty) {
      _formData['borrower_name'] = customer.fullName;
    }
    
    if (customer.phoneNumber?.isNotEmpty == true) {
      _formData['borrower_phone'] = customer.phoneNumber;
    }
    
    // Map thông tin giấy tờ tùy thân
    if (customer.idCardNumber?.isNotEmpty == true) {
      _formData['borrower_id_number'] = customer.idCardNumber;
    }
    
    if (customer.idCardType?.isNotEmpty == true) {
      _formData['borrower_id_type'] = customer.idCardType;
    }
    
    if (customer.idCardIssueDate != null) {
      _formData['borrower_id_issue_date'] = _formatDateForForm(customer.idCardIssueDate!);
    }
    
    if (customer.idCardExpiryDate != null) {
      _formData['borrower_id_expiry_date'] = _formatDateForForm(customer.idCardExpiryDate!);
    }
    
    if (customer.idCardIssuePlace?.isNotEmpty == true) {
      _formData['borrower_id_issue_place'] = customer.idCardIssuePlace;
    }
    
    // Map thông tin cá nhân
    if (customer.birthDate != null) {
      _formData['borrower_birth_date'] = _formatDateForForm(customer.birthDate!);
    }
    
    if (customer.gender?.code?.isNotEmpty == true) {
      _formData['borrower_gender'] = customer.gender!.code!;
    }
    
    // Map thông tin địa chỉ thường trú
    if (customer.permanentAddress?.isNotEmpty == true) {
      _formData['borrower_permanent_address'] = customer.permanentAddress;
    }
    
    if (customer.province?.id.isNotEmpty == true) {
      _formData['borrower_permanent_province'] = customer.province!.id;
    }
    
    if (customer.ward?.id?.isNotEmpty == true) {
      _formData['borrower_permanent_district'] = customer.ward!.id;
    }
    
    // Map thông tin địa chỉ hiện tại
    if (customer.currentAddress?.isNotEmpty == true) {
      _formData['borrower_current_address'] = customer.currentAddress;
    } else if (customer.sameAddress && customer.permanentAddress?.isNotEmpty == true) {
      // Nếu địa chỉ hiện tại giống thường trú
      _formData['borrower_current_address'] = customer.permanentAddress;
      _formData['borrower_current_same_permanent'] = true;
    }
    
    // Map thông tin nghề nghiệp (nếu có)
    if (customer.occupation?.isNotEmpty == true) {
      _formData['borrower_occupation'] = customer.occupation;
    }
    
    if (customer.workplace?.isNotEmpty == true) {
      _formData['borrower_workplace'] = customer.workplace;
    }
    
    if (customer.monthlyIncome?.isNotEmpty == true) {
      _formData['borrower_monthly_income'] = customer.monthlyIncome;
    }
    
    debugPrint('=== END: Mapping customer data to form ===');
    debugPrint('Mapped fields: ${_formData.keys.where((key) => key.startsWith('borrower_')).toList()}');
  }

  /// Format DateTime to string for form input (DD/MM/YYYY)
  String _formatDateForForm(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Lấy tên chi nhánh/phòng giao dịch từ AuthService
  String _getBranchNameFromAuth() {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.branchName?.isNotEmpty == true) {
        return currentUser!.profile!.branchName!;
      }
      return 'Chưa xác định chi nhánh';
    } catch (e) {
      debugPrint('Error getting branch name from auth: $e');
      return 'Lỗi lấy thông tin chi nhánh';
    }
  }

  /// Kiểm tra có nên hiển thị số tiền bên dưới field không
  bool _shouldShowAmountDisplay(String key) {
    final value = _formData[key]?.toString();
    
    // Không hiển thị nếu giá trị rỗng hoặc null
    if (value == null || value.isEmpty) return false;
    
    // Chuyển đổi thành số và kiểm tra
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return false;
    
    final amount = int.tryParse(cleanValue) ?? 0;
    
    // Chỉ hiển thị khi số tiền > 0
    return amount > 0;
  }

  void _updateDetails() {
    // Auto-calculate dependent fields for installment loans
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      _autoCalculateInstallmentLoanFields();
    }
    
    // Add common fields that are always needed
    _addCommonFields(_formData);
    
    // Pass form data directly - no need for mapping since _formData is already in correct format
    widget.onDetailsChanged(_formData);
  }

  /// Debounced update to prevent excessive setState calls
  void _debouncedUpdateDetails() {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _updateDetails();
      }
    });
  }


  /// Add common fields that are always needed
  void _addCommonFields(Map<String, dynamic> data) {
    // Add product information
    data['product_code'] = widget.product?.code ?? '';
    data['product_name'] = widget.product?.displayName ?? '';
    
    // Add timestamps
    data['created_at'] = DateTime.now().toIso8601String();
    data['updated_at'] = DateTime.now().toIso8601String();
    
    // Add validation flags
    data['is_validated'] = false;
    data['validation_errors'] = <String>[];
  }

  bool validateForm() {
    if (widget.product == null) return false;
    
    debugPrint('=== START: validateForm ===');
    
    // Trigger validation UI update
    setState(() {
      _isValidating = true;
    });
    
    final isValid = _formKey.currentState?.validate() ?? false;
    debugPrint('Form validation result: $isValid');
    
    debugPrint('=== END: validateForm ===');
    return isValid;
  }


  /// Mở QR scanner để quét mã QR từ CCCD cho người vay chính
  void _openQrScanner() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QrScannerWidget(
          onQrDetected: _handleQrScanResult,
          onError: _handleQrScanError,
          instructionText: 'Hướng camera vào mã QR trên CCCD hoặc chọn ảnh từ thư viện',
          scanMode: QrScanMode.continuous,
        ),
      ),
    );
  }

  /// Mở QR scanner để quét mã QR từ CCCD cho người đồng vay
  void _openCoBorrowerQrScanner() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QrScannerWidget(
          onQrDetected: _handleCoBorrowerQrScanResult,
          onError: _handleQrScanError,
          instructionText: 'Hướng camera vào mã QR trên CCCD của người đồng vay',
          scanMode: QrScanMode.continuous,
        ),
      ),
    );
  }

  /// Mở QR scanner để quét mã QR từ giấy đăng ký xe
  void _openVehicleQrScanner() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => QrScannerWidget(
          onQrDetected: _handleVehicleQrScanResult,
          onError: _handleQrScanError,
          instructionText: 'Hướng camera vào mã QR trên giấy đăng ký xe',
          scanMode: QrScanMode.continuous,
        ),
      ),
    );
  }


  /// Xử lý kết quả scan QR cho người vay chính
  void _handleQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      // Lấy kết quả đầu tiên
      final qrResult = results.first;
      debugPrint('QR content: ${qrResult.value}');

      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo thành công
      _showQrScanMessage('Đã quét thành công mã QR từ CCCD');

      // Parse QR data và mapping vào form
      await _parseQrAndMapToForm(qrResult.value);

    } catch (e) {
      debugPrint('Error handling QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR', isError: true);
    }
  }

  /// Xử lý kết quả scan QR cho người đồng vay
  void _handleCoBorrowerQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Co-borrower QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      // Lấy kết quả đầu tiên
      final qrResult = results.first;
      debugPrint('Co-borrower QR content: ${qrResult.value}');

      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo thành công
      _showQrScanMessage('Đã quét thành công mã QR từ CCCD người đồng vay');

      // Parse QR data và mapping vào form
      await _parseQrAndMapToCoBorrowerForm(qrResult.value);

    } catch (e) {
      debugPrint('Error handling co-borrower QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR người đồng vay', isError: true);
    }
  }

  /// Xử lý kết quả scan QR cho giấy đăng ký xe
  void _handleVehicleQrScanResult(List<QrScanResult> results) async {
    try {
      debugPrint('Vehicle QR scan completed with ${results.length} results');

      if (results.isEmpty) {
        _showQrScanMessage('Không tìm thấy mã QR', isError: true);
        return;
      }

      // Lấy kết quả đầu tiên
      final qrResult = results.first;
      debugPrint('Vehicle QR content: ${qrResult.value}');

      // Đóng QR scanner
      if (mounted) {
        Navigator.of(context).pop();
      }

      // Hiển thị thông báo thành công
      _showQrScanMessage('Đã quét thành công mã QR từ giấy đăng ký xe');

      // Parse QR data và mapping vào form
      await _parseQrAndMapToVehicleForm(qrResult.value);

    } catch (e) {
      debugPrint('Error handling vehicle QR scan result: $e');
      _showQrScanMessage('Có lỗi xảy ra khi xử lý mã QR giấy đăng ký xe', isError: true);
    }
  }

  /// Xử lý lỗi scan QR
  void _handleQrScanError(String error) async {
    debugPrint('QR scan error: $error');
    _showQrScanMessage('Lỗi quét QR: $error', isError: true);
  }

  /// Hiển thị thông báo kết quả scan QR
  void _showQrScanMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Parse QR data và mapping vào form fields cho người vay chính
  Future<void> _parseQrAndMapToForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToForm ===');
      debugPrint('QR Data: $qrData');
      
      // Parse CCCD QR data using util
      final qrFormData = QrUtil.parseCccdQrData(qrData);
      debugPrint('Parsed QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('QR data parsed successfully, mapping to form...');
        debugPrint('Form data before mapping: $_formData');
        
        // Mapping dữ liệu vào form using util
        setState(() {
          QrUtil.mapCccdToBorrowerForm(qrFormData, _formData);
          debugPrint('Form data after mapping: $_formData');
        });
        
        // Update all text controllers to reflect new data
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _updateAllControllers();
            _updateDetails();
          }
        });
        
        debugPrint('QR data mapped successfully to borrower form');
      } else {
        debugPrint('QR data parsing failed, showing error dialog');
        // Show error dialog
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToForm ===');
    } catch (e) {
      debugPrint('Error processing QR data: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Parse QR data và mapping vào form fields cho người đồng vay
  Future<void> _parseQrAndMapToCoBorrowerForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToCoBorrowerForm ===');
      debugPrint('Co-borrower QR Data: $qrData');
      
      // Parse CCCD QR data using util
      final qrFormData = QrUtil.parseCccdQrData(qrData);
      debugPrint('Parsed Co-borrower QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('Co-borrower QR data parsed successfully, mapping to form...');
        debugPrint('Form data before mapping: $_formData');
        
        // Mapping dữ liệu vào form using util
        setState(() {
          QrUtil.mapCccdToCoBorrowerForm(qrFormData, _formData);
          debugPrint('Form data after mapping: $_formData');
        });
        
        // Update all text controllers to reflect new data
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _updateAllControllers();
            _updateDetails();
          }
        });
        
        debugPrint('QR data mapped successfully to co-borrower form');
      } else {
        debugPrint('Co-borrower QR data parsing failed, showing error dialog');
        // Show error dialog
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToCoBorrowerForm ===');
    } catch (e) {
      debugPrint('Error processing QR data for co-borrower: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Parse QR data và mapping vào form fields cho giấy đăng ký xe
  Future<void> _parseQrAndMapToVehicleForm(String qrData) async {
    try {
      debugPrint('=== START: _parseQrAndMapToVehicleForm ===');
      debugPrint('Vehicle QR Data: $qrData');

      // Validate QR data first
      if (!VehicleQRBasicUtil.isValidBasicVehicleQRData(qrData)) {
        debugPrint('Invalid vehicle QR data format');
        _showQrScanMessage('Mã QR không hợp lệ hoặc thiếu thông tin cần thiết', isError: true);
        return;
      }
      
      // Parse vehicle QR data using VehicleQRBasicUtil
      final qrFormData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
      debugPrint('Parsed Vehicle QR Form Data: $qrFormData');
      
      if (qrFormData.isNotEmpty) {
        debugPrint('Vehicle QR data parsed successfully, mapping to form...');
        debugPrint('Form data before mapping: $_formData');
        
        // Mapping dữ liệu vào form
        setState(() {
          // Map 3 essential fields: biển kiểm soát, số khung, số máy
          if (qrFormData['vehicle_plate_number']?.isNotEmpty == true) {
            _formData['vehicle_plate_number'] = qrFormData['vehicle_plate_number'];
          }
          if (qrFormData['vehicle_frame_number']?.isNotEmpty == true) {
            _formData['vehicle_frame_number'] = qrFormData['vehicle_frame_number'];
          }
          if (qrFormData['vehicle_engine_number']?.isNotEmpty == true) {
            _formData['vehicle_engine_number'] = qrFormData['vehicle_engine_number'];
          }
          
          // Map additional fields if available
          if (qrFormData['vehicle_name']?.isNotEmpty == true) {
            _formData['vehicle_name'] = qrFormData['vehicle_name'];
          }
          if (qrFormData['vehicle_registration_number']?.isNotEmpty == true) {
            _formData['vehicle_registration_number'] = qrFormData['vehicle_registration_number'];
          }
          if (qrFormData['vehicle_registration_place']?.isNotEmpty == true) {
            _formData['vehicle_registration_place'] = qrFormData['vehicle_registration_place'];
          }
          if (qrFormData['vehicle_registration_date']?.isNotEmpty == true) {
            _formData['vehicle_registration_date'] = qrFormData['vehicle_registration_date'];
          }
          if (qrFormData['collateral_owner']?.isNotEmpty == true) {
            _formData['collateral_owner'] = qrFormData['collateral_owner'];
          }
          
          debugPrint('Form data after mapping: $_formData');
        });
        
        // Update all text controllers to reflect new data
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _updateAllControllers();
            _updateDetails();
          }
        });
        
        debugPrint('Vehicle QR data mapped successfully to form');
        _showQrScanMessage('Đã quét và điền thông tin xe thành công');

      } else {
        debugPrint('Vehicle QR data parsing failed, showing error dialog');
        // Show error dialog
        _showQrParseErrorDialog(qrData);
      }
      
      debugPrint('=== END: _parseQrAndMapToVehicleForm ===');
    } catch (e) {
      debugPrint('Error processing vehicle QR data: $e');
      _showQrParseErrorDialog(qrData);
    }
  }

  /// Hiển thị dialog lỗi parse QR
  void _showQrParseErrorDialog(String qrData) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              TablerIcons.alert_triangle,
              color: AppColors.warning,
              size: AppDimensions.iconM,
            ),
            SizedBox(width: AppDimensions.spacingS),
            const Text('Không thể đọc QR'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Không thể đọc thông tin từ mã QR này. Vui lòng thử lại hoặc nhập thông tin thủ công.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: AppDimensions.spacingM),
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(AppDimensions.paddingS),
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: Text(
                'Dữ liệu QR: ${qrData.length > 100 ? '${qrData.substring(0, 100)}...' : qrData}',
                style: const TextStyle(
                  fontFamily: 'monospace',
                  fontSize: 10,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Đóng'),
          ),
        ],
      ),
    );
  }

  List<String> _getRequiredFields() {
    if (widget.product == null) return [];
    
    switch (widget.product!.code) {
      case 'GOLD_LOAN':
      case 'MANGO':
        return _getInstallmentLoanRequiredFields();
      case 'PERSONAL_LOAN':
        return ['amount', 'term', 'purpose'];
      case 'MORTGAGE_LOAN':
        return ['amount', 'term', 'purpose', 'collateralType'];
      case 'AUTO_LOAN':
        return ['amount', 'term', 'purpose', 'collateralType'];
      case 'BUSINESS_LOAN':
        return ['amount', 'term', 'purpose', 'collateralType'];
      case 'EDUCATION_LOAN':
        return ['amount', 'term', 'purpose', 'schoolName'];
      case 'AGRICULTURE_LOAN':
        return ['amount', 'term', 'purpose', 'cropType'];
      case 'SME_LOAN':
        return ['amount', 'term', 'purpose', 'companyName'];
      case 'REVOLVING_CREDIT':
        return ['amount', 'term', 'purpose', 'companyName'];
      default:
        return [];
    }
  }

  /// Get required fields for installment loan products (GOLD_LOAN, MANGO)
  /// Based on SRS document section 2.1 - Step 3/5 Product Details
  List<String> _getInstallmentLoanRequiredFields() {
    List<String> requiredFields = [
      // Card 1: Người vay chính - Always required (theo SRS)
      // Thông tin nhận dạng giấy tờ
      'borrower_name',                    // Họ và tên ✔
      'borrower_id_number',              // Số GTTT ✔
      'borrower_id_issue_date',          // Ngày cấp ✔
      'borrower_id_expiry_date',         // Ngày hết hạn ✔
      'borrower_id_issue_place',         // Nơi cấp ✔
      'borrower_birth_date',             // Ngày sinh ✔
      'borrower_gender',                 // Giới tính ✔
      
      // Hộ khẩu thường trú
      'borrower_permanent_province',     // Tỉnh/thành phố thường trú ✔
      'borrower_permanent_district',     // Phường/Xã thường trú ✔
      'borrower_permanent_address',      // Địa chỉ cụ thể thường trú ✔
      
      // Thông tin cá nhân
      'borrower_marital_status',         // Tình trạng hôn nhân ✔
      'borrower_phone',                  // Số điện thoại ✔
      
      // Card 3: Đề nghị và phương án vay vốn - Only fields that exist in UI
      'loan_amount',                     // Số tiền đề nghị vay ✔
      'loan_term',                       // Thời hạn vay ✔
      'loan_purpose',                    // Mục đích sử dụng vốn ✔
      
      // Card 4: Tình hình tài chính - Only fields that exist in UI
      'income_source',                   // Nguồn thu ✔
      'daily_income',                    // Thu nhập bình quân/ngày ✔
      
      // Card 5: Tài sản bảo đảm - Only fields that exist in UI
      'collateral_value',                // Giá trị tài sản ✔
      'collateral_condition',            // Hiện trạng tài sản ✔
      
      // Card 6: Chi tiết tài sản bảo đảm - Only fields that exist in UI
      'vehicle_plate_number',            // Biển kiểm soát ✔
      'vehicle_name',                    // Tên tài sản ✔
      'vehicle_frame_number',            // Số khung ✔
      'vehicle_engine_number',           // Số máy ✔
      'vehicle_registration_number',     // Số giấy chứng nhận đăng ký xe ✔
      'vehicle_registration_place',      // Nơi cấp ✔
      'vehicle_registration_date',       // Ngày cấp ✔
      'vehicle_condition_at_handover',   // Tình trạng tài sản khi giao ✔
    ];
    
    // Conditional required fields for co-borrower
    final hasCoBorrower = _formData['has_co_borrower'] == true;
    if (hasCoBorrower) {
      requiredFields.addAll([
        // Card 2: Người đồng vay - Required when has_co_borrower = true
        // Thông tin nhận dạng giấy tờ
        'co_borrower_name',              // Họ và tên ✔
        'co_borrower_id_number',         // Số GTTT ✔
        'co_borrower_id_issue_date',     // Ngày cấp ✔
        'co_borrower_id_expiry_date',    // Ngày hết hạn ✔
        'co_borrower_id_issue_place',    // Nơi cấp ✔
        'co_borrower_birth_date',        // Ngày sinh ✔
        'co_borrower_gender',            // Giới tính ✔
        'co_borrower_permanent_address', // Hộ khẩu thường trú ✔
        
        // Thông tin cá nhân
        'co_borrower_marital_status',    // Tình trạng hôn nhân ✔
        'co_borrower_phone',             // Số điện thoại ✔
      ]);
      
      // Add co-borrower current address fields if different from permanent
      final coBorrowerSameAddress = _formData['co_borrower_current_same_permanent'] == true;
      if (!coBorrowerSameAddress) {
        requiredFields.addAll([
          'co_borrower_current_province',  // Tỉnh/thành phố hiện tại ✔
          'co_borrower_current_district',  // Phường/Xã hiện tại ✔
          'co_borrower_current_address',   // Địa chỉ hiện tại ✔
        ]);
      }
    }
    
    // Conditional required fields for business income source
    final incomeSource = _formData['income_source']?.toString() ?? '';
    if (incomeSource == 'Kinh doanh') {
      requiredFields.addAll([
        'daily_revenue',                 // Doanh số bình quân/ngày ✔
        'business_location_province',    // Tỉnh/thành phố SXKD ✔
        'business_location_district',    // Phường/Xã SXKD ✔
        'business_location_address',     // Địa chỉ SXKD ✔
      ]);
    }
    
    // Conditional required fields for current address if different from permanent
    final borrowerSameAddress = _formData['borrower_current_same_permanent'] == true;
    if (!borrowerSameAddress) {
      requiredFields.addAll([
        'borrower_current_province',     // Tỉnh/thành phố hiện tại ✔
        'borrower_current_district',     // Phường/Xã hiện tại ✔
        'borrower_current_address',      // Địa chỉ hiện tại ✔
      ]);
    }
    
    // Conditional required field for other loan purpose
    final loanPurpose = _formData['loan_purpose']?.toString() ?? '';
    if (loanPurpose == 'Khác') {
      requiredFields.add('loan_purpose_other'); // Tên mục đích ✔
    }
    
    // Conditional required field for bank account if disbursement method is transfer
    // Note: Only add if the field exists in the UI
    final disbursementMethod = _formData['disbursement_method']?.toString() ?? '';
    if (disbursementMethod == 'Chuyển khoản') {
      // requiredFields.add('disbursement_account'); // Commented out - field not in current UI
    }
    
    return requiredFields;
  }

  /// Get appropriate keyboard type based on field key
  TextInputType? _getKeyboardTypeForField(String key) {
    if (key.contains('phone')) {
      return TextInputType.phone;
    }
    if (key.contains('id_number') || key.contains('id_card')) {
      return TextInputType.number;
    }
    return null;
  }

  /// Get appropriate input formatters based on field key
  List<TextInputFormatter> _getInputFormattersForField(String key) {
    if (key.contains('phone')) {
      return [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(10),
        PhoneInputFormatter(),
      ];
    }
    
    if (key.contains('id_number') || key.contains('id_card')) {
      return [
        FilteringTextInputFormatter.digitsOnly,
        LengthLimitingTextInputFormatter(12),
      ];
    }
    
    // Name fields
    if (key.contains('name') || key == 'borrower_name' || key == 'co_borrower_name') {
      return [LengthLimitingTextInputFormatter(100)];
    }
    
    // Issue place fields
    if (key.contains('issue_place')) {
      return [LengthLimitingTextInputFormatter(50)];
    }
    
    // Address fields
    if (key.contains('address')) {
      return [LengthLimitingTextInputFormatter(250)];
    }
    
    // Loan purpose other field
    if (key == 'loan_purpose_other') {
      return [LengthLimitingTextInputFormatter(100)];
    }
    
    return [];
  }
  
  void _autoCalculateInstallmentLoanFields() {
    // Auto-fill borrower info to collateral owner if they match
    if (_formData['borrower_name']?.toString().isNotEmpty == true) {
      _formData['collateral_owner'] = _formData['borrower_name'];
    }
    
    if (_formData['borrower_birth_date']?.toString().isNotEmpty == true) {
      final birthDate = _formData['borrower_birth_date'].toString();
      final year = birthDate.split('/').length == 3 ? birthDate.split('/').last : '';
      _formData['collateral_owner_birth_year'] = year;
    }
    
    // Auto-calculate total capital need using new function
    _updateTotalCapitalNeed();
    
    // Auto-update collateral value text
    if (_formData['collateral_value']?.toString().isNotEmpty == true) {
      final value = _formData['collateral_value']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0';
      _formData['collateral_value_text'] = _convertNumberToWords(value);
    }
    
    // Auto-update total collateral value
    _formData['total_collateral_value'] = _formData['collateral_value']?.toString() ?? '0';
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    if (widget.product == null) {
      return SizedBox();
    }

    return SingleChildScrollView(
      controller: _scrollController,
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              '📋 Thông tin ${widget.product?.displayName ?? 'sản phẩm'}',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: widget.product?.displayColor ?? AppColors.kienlongOrange,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Text(
              'Điền thông tin chi tiết cho sản phẩm đã chọn',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: AppDimensions.spacingL),

            // Dynamic Form based on product type
            ..._buildProductForm(),

            // Calculation Preview
            if (_shouldShowCalculation()) ...[
              const SizedBox(height: AppDimensions.spacingL),
              _buildCalculationPreview(),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildProductForm() {
    switch (widget.product?.code) {
      case 'GOLD_LOAN':
      case 'MANGO':
        return _buildDailyInstallmentLoanForm();
      case 'PERSONAL_LOAN':
        return _buildLoanForm();
      case 'MORTGAGE_LOAN':
        return _buildMortgageForm();
      case 'AUTO_LOAN':
        return _buildAutoLoanForm();
      case 'BUSINESS_LOAN':
        return _buildBusinessLoanForm();
      case 'EDUCATION_LOAN':
        return _buildEducationLoanForm();
      case 'AGRICULTURE_LOAN':
        return _buildAgricultureLoanForm();
      case 'SME_LOAN':
        return _buildSmeLoanForm();
      case 'REVOLVING_CREDIT':
        return _buildRevolvingCreditForm();
      default:
        return _buildGenericForm();
    }
  }

  List<Widget> _buildDailyInstallmentLoanForm() {
    return [
      // Main Borrower Information Card
      _buildExpandableCard(
        'Thông tin người vay chính',
        TablerIcons.user,
        AppColors.kienlongOrange,
        _buildMainBorrowerForm(),
        'borrower_card_expanded',
      ),
      
      const SizedBox(height: AppDimensions.spacingM),
      
      // Co-borrower Toggle and Information
      _buildCoBorrowerSection(),
      
      const SizedBox(height: AppDimensions.spacingM),
      
      // Loan Proposal Card
      _buildExpandableCard(
        'Đề nghị và phương án vay vốn',
        TablerIcons.cash,
        AppColors.success,
        _buildLoanProposalForm(),
        'loan_proposal_card_expanded',
      ),
      
      const SizedBox(height: AppDimensions.spacingM),
      
      // Financial Information Card
      _buildExpandableCard(
        'Tình hình tài chính',
        TablerIcons.chart_line,
        AppColors.info,
        _buildFinancialInfoForm(),
        'financial_card_expanded',
      ),
      
      const SizedBox(height: AppDimensions.spacingM),
      
      // Collateral Information Card (only if loan type has collateral)
      if (_formData['loan_type'] == 'Có TSĐB') ...[
        _buildExpandableCard(
          'Tài sản bảo đảm',
          TablerIcons.shield_check,
          AppColors.warning,
          _buildCollateralForm(),
          'collateral_card_expanded',
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildExpandableCard(
          'Chi tiết tài sản bảo đảm',
          TablerIcons.file_description,
          AppColors.info,
          _buildDetailedCollateralForm(),
          'detailed_collateral_card_expanded',
        ),
        const SizedBox(height: AppDimensions.spacingM),
      ],
    ];
  }

  List<Widget> _buildLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['12', '24', '36', '48', '60'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Tiêu dùng cá nhân', 'Kinh doanh', 'Mua xe', 'Sửa chữa nhà', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất tham khảo',
        '${_formData['interestRate']}%/năm',
        TablerIcons.percentage,
        AppColors.info,
      ),
    ];
  }



  List<Widget> _buildMortgageForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['60', '120', '180', '240'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Mua nhà', 'Mua đất', 'Xây nhà', 'Sửa chữa nhà'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Loại tài sản thế chấp',
        'collateralType',
        ['Bất động sản', 'Nhà ở', 'Đất nền', 'Căn hộ chung cư'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất ưu đãi',
        '${_formData['interestRate']}%/năm',
        TablerIcons.home,
        AppColors.success,
      ),
    ];
  }

  List<Widget> _buildAutoLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['12', '24', '36', '48', '60', '72'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Mua xe mới', 'Mua xe cũ', 'Sửa chữa xe', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Loại xe',
        'collateralType',
        ['Xe ô tô', 'Xe máy', 'Xe tải', 'Xe khách'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất ưu đãi',
        '${_formData['interestRate']}%/năm',
        TablerIcons.car,
        AppColors.warning,
      ),
    ];
  }

  List<Widget> _buildBusinessLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['12', '24', '36', '48', '60'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Mở rộng kinh doanh', 'Bổ sung vốn lưu động', 'Mua thiết bị', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Loại tài sản thế chấp',
        'collateralType',
        ['Bất động sản', 'Thiết bị', 'Hàng hóa', 'Không thế chấp'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất kinh doanh',
        '${_formData['interestRate']}%/năm',
        TablerIcons.building,
        AppColors.success,
      ),
    ];
  }

  List<Widget> _buildEducationLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['24', '36', '48', '60', '72'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Học phí', 'Chi phí sinh hoạt', 'Mua sách vở', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Tên trường học',
        'schoolName',
        'Nhập tên trường học',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất giáo dục',
        '${_formData['interestRate']}%/năm',
        TablerIcons.school,
        AppColors.info,
      ),
    ];
  }

  List<Widget> _buildAgricultureLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['6', '12', '18', '24', '36'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Mua giống', 'Mua phân bón', 'Thuê máy móc', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Loại cây trồng',
        'cropType',
        ['Lúa', 'Cà phê', 'Tiêu', 'Cao su', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất nông nghiệp',
        '${_formData['interestRate']}%/năm',
        TablerIcons.plant,
        AppColors.success,
      ),
    ];
  }

  List<Widget> _buildSmeLoanForm() {
    return [
      _buildAmountField('Số tiền vay', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn vay',
        'term',
        ['12', '24', '36', '48', '60'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích vay',
        'purpose',
        ['Vốn lưu động', 'Mua thiết bị', 'Mở rộng sản xuất', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Tên doanh nghiệp',
        'companyName',
        'Nhập tên doanh nghiệp',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất SME',
        '${_formData['interestRate']}%/năm',
        TablerIcons.building_community,
        AppColors.warning,
      ),
    ];
  }

  List<Widget> _buildRevolvingCreditForm() {
    return [
      _buildAmountField('Hạn mức tín dụng', 'amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Thời hạn',
        'term',
        ['12', '24', '36', '48', '60'],
        (value) => '$value tháng',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildDropdownField(
        'Mục đích sử dụng',
        'purpose',
        ['Bổ sung vốn lưu động', 'Thanh toán nhà cung cấp', 'Khác'],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Tên doanh nghiệp',
        'companyName',
        'Nhập tên doanh nghiệp',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildInfoCard(
        'Lãi suất tín dụng',
        '${_formData['interestRate']}%/năm',
        TablerIcons.credit_card,
        AppColors.info,
      ),
    ];
  }

  List<Widget> _buildGenericForm() {
    return [
      _buildTextField(
        'Ghi chú',
        'notes',
        'Thêm thông tin chi tiết...',
        maxLines: 3,
      ),
    ];
  }

  // Enhanced amount field with proper validation
  Widget _buildAmountField(String label, String key, String suffix) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator and error color
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: hasFieldError ? Theme.of(context).colorScheme.error : null,
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
        TextFormField(
            key: _fieldKeys[key],
            controller: _getController(key),
            keyboardType: TextInputType.number,
            inputFormatters: [
              FilteringTextInputFormatter.digitsOnly,
              LeadingZeroRemoverFormatter(maxValue: 1000000000), // Remove leading zeros and cap at 1 billion
              LengthLimitingTextInputFormatter(10), // Max 1 billion (10 digits)
            ],
            onChanged: (value) {
              // InputFormatter already handles leading zeros removal and 1 billion cap
              if (_formData[key] != value) {
                setState(() {
                  _formData[key] = value;
                  
                  // Trigger update for calculated fields that depend on this value
                  if (key == 'collateral_value') {
                    _updateCollateralCalculatedFields();
                  }
                  
                  // Auto-calculate total capital need for loan fields
                  if (key == 'own_capital' || key == 'loan_amount') {
                    _updateTotalCapitalNeed();
                  }
                });
                _debouncedUpdateDetails();
              }
            },
            decoration: InputDecoration(
              hintText: 'Nhập số tiền',
              suffixText: suffix,
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            validator: (value) {
              if (!_isValidating) return null;
              
              final requiredFields = _getCachedRequiredFields();
              final isRequired = requiredFields.contains(key);
              
              if (isRequired && (value == null || value.isEmpty)) {
                return 'Vui lòng nhập $label';
              }
              
              if (value != null && value.isNotEmpty) {
                // Validate based on field type
                if (key == 'own_capital') {
                  return _validateOwnCapital(value);
                } else if (key == 'loan_amount') {
                  return _validateLoanAmount(value);
                } else {
                  // General validation for other amount fields
                  final amount = int.tryParse(value.replaceAll(RegExp(r'[^\d]'), '')) ?? 0;
                  if (amount > 1000000000) {
                    return 'Số tiền vượt quá giới hạn 1 tỷ VND';
                  }
                }
              }
              return null;
            },
          ),
  
        if (_shouldShowAmountDisplay(key))
          Padding(
            padding: EdgeInsets.only(top: AppDimensions.spacingS),
            child: Text(
              '≈ ${_getCachedFormattedCurrency(_formData[key].toString())} $suffix',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.kienlongOrange,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }

  // Enhanced text field with validation support
  Widget _buildTextField(
    String label,
    String key,
    String hint, {
    int maxLines = 1,
    bool readOnly = false,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    void Function(String?)? onChanged,
  }) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator and error color
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: hasFieldError ? Theme.of(context).colorScheme.error : null,
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
       TextFormField(
            key: _fieldKeys[key],
            controller: _getController(key),
            maxLines: maxLines,
            readOnly: readOnly,
            keyboardType: keyboardType ?? _getKeyboardTypeForField(key),
            inputFormatters: inputFormatters ?? _getInputFormattersForField(key),
            onChanged: readOnly ? null : (value) {
              // No need for setState here - controller listener handles _formData sync
              // Call additional onChanged callback if provided
              onChanged?.call(value);
            },
            decoration: InputDecoration(
              hintText: hint,
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            validator: (value) {
              if (readOnly) return null;
              if (!_isValidating) return null;
              
              final requiredFields = _getCachedRequiredFields();
              final isRequired = requiredFields.contains(key);
              
              if (isRequired && (value == null || value.isEmpty)) {
                return 'Vui lòng nhập $label';
              }
              
              // Validate field-specific rules according to SRS
              if (value != null && value.isNotEmpty) {
                // Phone number validation - must start with 0 and be 10 digits (theo SRS)
                if (key.contains('phone')) {
                  // Remove all non-digits for validation
                  final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
                  
                  if (cleanValue.length != 10) {
                    return 'Số điện thoại phải có đúng 10 chữ số';
                  }
                  
                  if (!cleanValue.startsWith('0')) {
                    return 'Số điện thoại phải bắt đầu bằng số 0';
                  }
                  
                  // Check for valid Vietnamese mobile number patterns
                  final validPrefixes = [
                    '032', '033', '034', '035', '036', '037', '038', '039', '056', '058', // Viettel
                    '070', '076', '077', '078', '079', '081', '082', '083', '084', '085', // Mobifone
                    '088', '091', '094', '096', '097', '098', // Vinaphone & Viettel
                  ];
                  
                  final prefix = cleanValue.substring(0, 3);
                  if (!validPrefixes.contains(prefix)) {
                    return 'Số điện thoại không hợp lệ. Vui lòng nhập số điện thoại Việt Nam';
                  }
                }
                
                // ID number fields - max 12 digits, only numbers (theo SRS)
                if (key.contains('id_number') || key.contains('id_card')) {
                  final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
                  if (cleanValue.length > 12) {
                    return 'Số giấy tờ tối đa 12 ký tự số';
                  }
                  if (cleanValue.isNotEmpty && !RegExp(r'^\d+$').hasMatch(cleanValue)) {
                    return 'Số giấy tờ chỉ được chứa số';
                  }
                }
                
                // Name fields - max 100 characters (theo SRS)
                if ((key.contains('name') || key == 'borrower_name' || key == 'co_borrower_name') && 
                    value.length > 100) {
                  return 'Tối đa 100 ký tự tính cả khoảng trắng';
                }
                
                // Issue place fields - max 50 characters (theo SRS)
                if (key.contains('issue_place') && value.length > 50) {
                  return 'Tối đa 50 ký tự';
                }
                
                // Address fields - max 250 characters (theo SRS)
                if (key.contains('address') && value.length > 250) {
                  return 'Tối đa 250 ký tự';
                }
                
                // Loan purpose other field - max 100 characters (theo SRS)
                if (key == 'loan_purpose_other' && value.length > 100) {
                  return 'Tối đa 100 ký tự';
                }
              }
              return null;
            },
          ),
     
      ],
    );
  }

  Widget _buildDropdownField(String label, String key, List<String> options, [String Function(String)? formatter]) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator and error color
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: hasFieldError ? Theme.of(context).colorScheme.error : null,
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
        DropdownButtonFormField<String>(
            key: _fieldKeys[key],
            value: options.contains(_formData[key]) ? _formData[key] : null,
            decoration: InputDecoration(
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            items: options.map((option) {
              return DropdownMenuItem(
                value: option,
                child: Text(formatter?.call(option) ?? option),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _formData[key] = value;
              });
              _debouncedUpdateDetails();
            },
            validator: (value) {
              if (!_isValidating) return null;
              
              final requiredFields = _getCachedRequiredFields();
              final isRequired = requiredFields.contains(key);
              
              if (isRequired && value == null) {
                return 'Vui lòng chọn $label';
              }
              return null;
            },
          ),
     
      ],
    );
  }

  /// Build dropdown field using master data configs - Updated to use id instead of code
  Widget _buildMasterDataDropdown(
    String label,
    String key,
    List<ConfigModel> configs, [
    String Function(ConfigModel)? formatter,
  ]) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        // Only rebuild when specific config data changes
        if (previous is MasterDataLoaded && current is MasterDataLoaded) {
          // Get config type for this dropdown
          String? configType = _getConfigTypeForKey(key);
          if (configType != null) {
            final prevConfigs = previous.configsByGroup[configType] ?? [];
            final currConfigs = current.configsByGroup[configType] ?? [];
            return prevConfigs != currConfigs;
          }
        }
        return current is MasterDataLoaded ||
               (current is MasterDataLoading && current.type == 'config') ||
               (current is MasterDataError && current.type == 'config');
      },
      builder: (context, state) {
        List<ConfigModel> availableConfigs = [];
        bool isLoading = false;

        // Get configs from state
        if (state is MasterDataLoaded) {
          // Find the config type based on the key using helper method
          String? configType = _getConfigTypeForKey(key);
          
          if (configType != null) {
            availableConfigs = state.configsByGroup[configType] ?? [];
          }
        } else if (state is MasterDataLoading && state.type == 'config') {
          isLoading = true;
        } else if (state is MasterDataError && state.type == 'config') {
        }

        // Use passed configs if state doesn't have them
        if (availableConfigs.isEmpty && configs.isNotEmpty) {
          availableConfigs = configs;
        }

        // Show error state only when there's an actual API error, not validation error
        if (state is MasterDataError && state.type == 'config') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              DropdownButtonFormField<String>(
                value: null,
                decoration: InputDecoration(
                  hintText: 'Lỗi tải dữ liệu',
                  prefixIcon: Icon(
                    TablerIcons.list,
                    color: AppColors.textSecondary,
                  ),
                  contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                ),
                isExpanded: true, // Fix overflow issue
                items: [],
                onChanged: null,
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Text(
                'Không thể tải danh sách $label',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color  
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              key: _fieldKeys[key],
              value: _getValidDropdownValue(key, availableConfigs),
              decoration: InputDecoration(
                hintText: isLoading ? 'Đang tải...' : 'Chọn',
                prefixIcon: Icon(
                  TablerIcons.list,
                  color: AppColors.textSecondary,
                ),
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                // Apply error styling when validation fails
                errorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                ) : null,
                focusedErrorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error, width: 2),
                ) : null,
              ),
              // Sửa overflow ở icon arrow
              isExpanded: true,
              icon: Icon(
                TablerIcons.chevron_down,
                color: AppColors.textSecondary,
                size: 16,
              ),
              items: availableConfigs.map((config) {
                return DropdownMenuItem<String>(
                  value: config.id, // ✅ SỬ DỤNG ID THAY VÌ CODE
                  child: Text(
                    formatter?.call(config) ?? config.label ?? config.code ?? '',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                );
              }).toList(),
              onChanged: availableConfigs.isEmpty || isLoading ? null : (value) {
                setState(() {
                  _formData[key] = value;
                });
                _debouncedUpdateDetails();
              },
              validator: (value) {
                if (!_isValidating) return null;
                
                final requiredFields = _getCachedRequiredFields();
                final isRequired = requiredFields.contains(key);
                
                if (isRequired && (value == null || value.isEmpty)) {
                  return 'Vui lòng chọn $label';
                }
                return null;
              },
            ),
          ],
        );
      },
    );
  }

  /// Build master data read-only field - maintains readOnly UI but uses master data for value
  Widget _buildMasterDataReadOnlyField(String label, String key) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        // Only rebuild when specific config data changes
        if (previous is MasterDataLoaded && current is MasterDataLoaded) {
          // Get config type for this field
          String? configType = _getConfigTypeForKey(key);
          if (configType != null) {
            final prevConfigs = previous.configsByGroup[configType] ?? [];
            final currConfigs = current.configsByGroup[configType] ?? [];
            return prevConfigs != currConfigs;
          }
        }
        return current is MasterDataLoaded ||
               (current is MasterDataLoading && current.type == 'config') ||
               (current is MasterDataError && current.type == 'config');
      },
      builder: (context, state) {
        List<ConfigModel> availableConfigs = [];
        bool isLoading = false;
        String displayValue = 'Đang tải...';

        // Get configs from state
        if (state is MasterDataLoaded) {
          String? configType = _getConfigTypeForKey(key);
          if (configType != null) {
            availableConfigs = state.configsByGroup[configType] ?? [];
          }
        } else if (state is MasterDataLoading && state.type == 'config') {
          isLoading = true;
        } else if (state is MasterDataError && state.type == 'config') {
          displayValue = 'Lỗi tải dữ liệu';
        }

        // Auto-select first item if no selection and data is available
        if (availableConfigs.isNotEmpty && 
            (_formData[key] == null || _formData[key].toString().isEmpty)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _formData[key] = availableConfigs.first.id;
              });
              _debouncedUpdateDetails();
            }
          });
        }

        // Get display value from selected config
        if (availableConfigs.isNotEmpty && _formData[key]?.toString().isNotEmpty == true) {
          final selectedConfig = availableConfigs.firstWhere(
            (config) => config.id == _formData[key].toString(),
            orElse: () => availableConfigs.first,
          );
          displayValue = selectedConfig.label ?? selectedConfig.code ?? 'Không xác định';
        } else if (!isLoading && state is! MasterDataError) {
          displayValue = 'Chưa có dữ liệu';
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            Container(
              decoration: BoxDecoration(
                color: AppColors.neutral100,
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                border: Border.all(color: AppColors.borderLight),
              ),
              child: TextFormField(
                key: _fieldKeys[key],
                controller: TextEditingController(text: displayValue),
                readOnly: true,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                  prefixIcon: isLoading 
                      ? Padding(
                          padding: EdgeInsets.all(AppDimensions.paddingM),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
                            ),
                          ),
                        )
                      : Icon(
                          TablerIcons.lock,
                          color: AppColors.textSecondary,
                          size: AppDimensions.iconS,
                        ),
                ),
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: isLoading || state is MasterDataError 
                      ? AppColors.textSecondary 
                      : AppColors.textPrimary,
                ),
                validator: (value) {
                  if (!_isValidating) return null;
                  
                  final requiredFields = _getCachedRequiredFields();
                  final isRequired = requiredFields.contains(key);
                  
                  if (isRequired && (_formData[key] == null || _formData[key].toString().isEmpty)) {
                    return 'Vui lòng chọn $label';
                  }
                  return null;
                },
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build master data radio group - maintains radio button UI but uses master data
  Widget _buildMasterDataRadioGroup(String label, String key) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        // Only rebuild when specific config data changes
        if (previous is MasterDataLoaded && current is MasterDataLoaded) {
          // Get config type for this radio group
          String? configType = _getConfigTypeForKey(key);
          if (configType != null) {
            final prevConfigs = previous.configsByGroup[configType] ?? [];
            final currConfigs = current.configsByGroup[configType] ?? [];
            return prevConfigs != currConfigs;
          }
        }
        return current is MasterDataLoaded ||
               (current is MasterDataLoading && current.type == 'config') ||
               (current is MasterDataError && current.type == 'config');
      },
      builder: (context, state) {
        List<ConfigModel> availableConfigs = [];
        bool isLoading = false;

        // Get configs from state
        if (state is MasterDataLoaded) {
          String? configType = _getConfigTypeForKey(key);
          if (configType != null) {
            availableConfigs = state.configsByGroup[configType] ?? [];
          }
        } else if (state is MasterDataLoading && state.type == 'config') {
          isLoading = true;
        } else if (state is MasterDataError && state.type == 'config') {
        }

        // Auto-select first item if no selection and data is available
        if (availableConfigs.isNotEmpty && 
            (_formData[key] == null || _formData[key].toString().isEmpty)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _formData[key] = availableConfigs.first.id;
              });
              _debouncedUpdateDetails();
            }
          });
        }

        // Show error state only when there's an actual API error
        if (state is MasterDataError && state.type == 'config') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.neutral100,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: AppColors.borderLight),
                ),
                child: Text(
                  'Không thể tải dữ liệu $label',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.error,
                  ),
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color  
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            
            if (isLoading)
              Container(
                padding: EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.neutral100,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  border: Border.all(color: AppColors.borderLight),
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.kienlongOrange),
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Text(
                      'Đang tải...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              )
            else if (availableConfigs.length <= 2)
              // Use row layout for 2 or fewer items
              Row(
                children: availableConfigs.map((config) {
                  final isSelected = _formData[key]?.toString() == config.id;
                  return Expanded(
                    child: Container(
                      margin: EdgeInsets.only(
                        right: config != availableConfigs.last ? AppDimensions.spacingM : 0,
                      ),
                      decoration: BoxDecoration(
                        color: Theme.of(context).cardColor,
                        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                        border: Border.all(
                          color: isSelected ? AppColors.kienlongOrange : AppColors.borderLight,
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: RadioListTile<String>(
                        key: config == availableConfigs.first ? _fieldKeys[key] : null,
                        title: Text(
                          config.label ?? config.code ?? '',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                        value: config.id ?? '',
                        groupValue: _formData[key]?.toString(),
                        onChanged: (selectedValue) {
                          setState(() {
                            _formData[key] = selectedValue ?? '';
                          });
                          _debouncedUpdateDetails();
                        },
                        activeColor: AppColors.kienlongOrange,
                        contentPadding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingS),
                      ),
                    ),
                  );
                }).toList(),
              )
            else
              // Use column layout for more than 2 items
              Column(
                children: availableConfigs.map((config) {
                  final isSelected = _formData[key]?.toString() == config.id;
                  return Container(
                    margin: EdgeInsets.only(
                      bottom: config != availableConfigs.last ? AppDimensions.spacingS : 0,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                      border: Border.all(
                        color: isSelected ? AppColors.kienlongOrange : AppColors.borderLight,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: RadioListTile<String>(
                      key: config == availableConfigs.first ? _fieldKeys[key] : null,
                      title: Text(
                        config.label ?? config.code ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                      value: config.id ?? '',
                      groupValue: _formData[key]?.toString(),
                      onChanged: (selectedValue) {
                        setState(() {
                          _formData[key] = selectedValue ?? '';
                        });
                        _debouncedUpdateDetails();
                      },
                      activeColor: AppColors.kienlongOrange,
                      contentPadding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingS),
                    ),
                  );
                }).toList(),
              ),
          ],
        );
      },
    );
  }

  /// Build bank account dropdown using master data
  Widget _buildBankAccountDropdown(String label, String key) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        return current is MasterDataLoaded ||
               (current is MasterDataLoading && current.type == 'bank_accounts') ||
               (current is MasterDataError && current.type == 'bank_accounts');
      },
      builder: (context, state) {
        List<BankAccountModel> bankAccounts = [];
        bool isLoading = false;

        // Get bank accounts from state
        if (state is MasterDataLoaded) {
          // Lấy idCardNo từ customer data đã chọn ở bước trước
          final idCardNo = widget.details['customer_id_card_no']?.toString() ?? 
                           widget.details['id_card_no']?.toString() ??
                           _formData['borrower_id_number']?.toString();
          if (idCardNo != null && idCardNo.isNotEmpty) {
            bankAccounts = state.bankAccounts;
          }
        } else if (state is MasterDataLoading && state.type == 'bank_accounts') {
          isLoading = true;
        } else if (state is MasterDataError && state.type == 'bank_accounts') {
        }

        // Show error state only when there's an actual API error, not validation error
        if (state is MasterDataError && state.type == 'bank_accounts') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              DropdownButtonFormField<String>(
                value: null,
                decoration: InputDecoration(
                  hintText: 'Lỗi tải dữ liệu',
                  prefixIcon: Icon(
                    TablerIcons.credit_card,
                    color: AppColors.textSecondary,
                  ),
                  contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                ),
                isExpanded: true, // Fix overflow issue
                items: [],
                onChanged: null,
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Text(
                'Không thể tải danh sách tài khoản',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              key: _fieldKeys[key],
              value: _formData[key]?.toString().isNotEmpty == true
                  ? _formData[key]
                  : null,
              decoration: InputDecoration(
                hintText: isLoading ? 'Đang tải...' : 'Chọn tài khoản',
                prefixIcon: Icon(
                  TablerIcons.credit_card,
                  color: AppColors.textSecondary,
                ),
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                // Apply error styling when validation fails
                errorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                ) : null,
                focusedErrorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error, width: 2),
                ) : null,
              ),
              isExpanded: true, // Fix overflow issue
              items: bankAccounts.map((account) {
                return DropdownMenuItem<String>(
                  value: account.accountNo,
                  child: Text(
                    account.displayName,
                    style: Theme.of(context).textTheme.bodyMedium,
                    overflow: TextOverflow.ellipsis,
                  ),
                );
              }).toList(),
              onChanged: bankAccounts.isEmpty || isLoading ? null : (value) {
                setState(() {
                  _formData[key] = value;
                });
                _debouncedUpdateDetails();
              },
              validator: (value) {
                if (!_isValidating) return null;
                
                final requiredFields = _getCachedRequiredFields();
                final isRequired = requiredFields.contains(key);
                
                if (isRequired && (value == null || value.isEmpty)) {
                  return 'Vui lòng chọn tài khoản';
                }
                return null;
              },
            ),
           
          ],
        );
      },
    );
  }

  // Note: Loading state is managed by BlocBuilder

  /// Get valid dropdown value to avoid duplicate value errors - Updated to use id
  String? _getValidDropdownValue(String key, List<ConfigModel> configs) {
    final currentValue = _formData[key];
    if (currentValue == null || currentValue.toString().isEmpty) {
      return null;
    }

    // Check if current value exists in configs by id (primary identifier)
    final matchingConfigs = configs.where((config) => 
      config.id == currentValue || 
      config.code == currentValue || // fallback for backward compatibility
      config.label == currentValue
    ).toList();

    // If exactly one match, return the id
    if (matchingConfigs.length == 1) {
      return matchingConfigs.first.id;
    }

    // If multiple matches or no matches, return null to avoid duplicate error
    debugPrint('Warning: Multiple or no configs found for key: $key, value: $currentValue');
    return null;
  }

  // Note: Loading state is managed by BlocBuilder

  /// Handle province change - Updated to match address_info_step.dart pattern
  void Function(String?)? _onProvinceChanged(String key) {
    return (String? provinceId) {
      debugPrint('_onProvinceChanged called with: $provinceId for key: $key');

      setState(() {
        _formData[key] = provinceId ?? '';
        
        // Clear ward selection when province changes
        final wardKey = key.replaceAll('province', 'district');
        _formData[wardKey] = null;
      });

      debugPrint(
        'After setState - province: ${_formData[key]}, ward: ${_formData[key.replaceAll('province', 'district')]}',
      );

      // Load wards for selected province
      if (provinceId != null && provinceId.isNotEmpty) {
        debugPrint('Loading wards for province: $provinceId');
        context.read<MasterDataBloc>().add(LoadWardsEvent(provinceId));
      }
      
      _updateDetails();
    };
  }

  /// Handle ward change - Updated to match address_info_step.dart pattern
  void Function(String?)? _onWardChanged(String key) {
    return (String? wardId) {
      setState(() {
        _formData[key] = wardId;
      });
      _updateDetails();
    };
  }

  /// Build province dropdown using master data - Updated to match address_info_step.dart pattern
  Widget _buildProvinceDropdown(String label, String key) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return RepaintBoundary(
      child: BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        // Chỉ rebuild khi provinces thực sự thay đổi
        if (previous is MasterDataLoaded && current is MasterDataLoaded) {
          final prevProvinces = previous.provinces;
          final currProvinces = current.provinces;
          return prevProvinces.length != currProvinces.length || 
                 !_listEquals(prevProvinces, currProvinces);
        }
        return current is MasterDataLoaded ||
            current is ProvincesLoaded ||
            (current is MasterDataError && current.type == 'provinces') ||
            (current is MasterDataLoading && current.type == 'provinces');
      },
      builder: (context, state) {
        // Cập nhật danh sách provinces khi state thay đổi
        if (state is MasterDataLoaded) {
          _provinces = _getCachedProvinces(state.provinces);
        } else if (state is ProvincesLoaded) {
          _provinces = _getCachedProvinces(state.provinces);
        }

        // Debug prints removed for performance

        if (state is MasterDataError && state.type == 'provinces') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              DropdownButtonFormField<String>(
                value: null,
                decoration: InputDecoration(
                  hintText: 'Lỗi tải dữ liệu',
                  prefixIcon: Icon(
                    TablerIcons.building_skyscraper,
                    color: AppColors.textSecondary,
                  ),
                  contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                ),
                isExpanded: true, // Fix overflow issue
                items: [],
                onChanged: null,
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Text(
                'Không thể tải danh sách tỉnh/thành',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              key: _fieldKeys[key],
              value: _formData[key]?.toString().isNotEmpty == true
                  ? _formData[key]
                  : null,
              decoration: InputDecoration(
                hintText: (state is MasterDataLoading && state.type == 'provinces')
                    ? 'Đang tải...'
                    : 'Chọn $label',
                prefixIcon: Icon(
                  TablerIcons.building_skyscraper,
                  color: AppColors.textSecondary,
                ),
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                // Apply error styling when validation fails
                errorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                ) : null,
                focusedErrorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error, width: 2),
                ) : null,
              ),
              isExpanded: true, // Fix overflow issue
              items: _provinces.map((province) {
                return DropdownMenuItem<String>(
                  value: province.id,
                  child: Text(province.name),
                );
              }).toList(),
              onChanged: _onProvinceChanged(key),
              validator: (value) {
                if (!_isValidating) return null;
                
                final requiredFields = _getCachedRequiredFields();
                final isRequired = requiredFields.contains(key);
                
                if (isRequired && (value == null || value.isEmpty)) {
                  return 'Vui lòng chọn ${label.toLowerCase()}';
                }
                return null;
              },
            ),
          ],
        );
      },
    ),
    );
  }

  /// Build ward dropdown using master data - Updated to match address_info_step.dart pattern
  Widget _buildWardDropdown(String label, String key, String provinceKey) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return RepaintBoundary(
      child: BlocBuilder<MasterDataBloc, MasterDataState>(
        buildWhen: (previous, current) {
          // Chỉ rebuild khi wards thực sự thay đổi cho province hiện tại
          final currentProvinceId = _formData[provinceKey];
          if (currentProvinceId == null || currentProvinceId.isEmpty) return false;
          
          if (previous is MasterDataLoaded && current is MasterDataLoaded) {
            final prevWards = previous.wardsByProvince[currentProvinceId] ?? [];
            final currWards = current.wardsByProvince[currentProvinceId] ?? [];
            return prevWards.length != currWards.length || 
                   !_listEquals(prevWards, currWards);
          }
          
          return current is MasterDataLoaded ||
              current is WardsLoaded ||
              (current is MasterDataError && current.type == 'wards') ||
              (current is MasterDataLoading && current.type == 'wards');
        },
      builder: (context, state) {
        List<WardModel> wards = [];
        bool isLoadingWards = false;

        // Debug prints removed for performance

        // Check new composite state first
        if (state is MasterDataLoaded) {
          final wardsForProvince = state.wardsByProvince[_formData[provinceKey]];
          if (wardsForProvince != null) {
            wards = wardsForProvince;
          }
        }
        // Fallback to legacy state for backward compatibility
        else if (state is WardsLoaded &&
            state.provinceId == _formData[provinceKey]) {
          wards = state.wards;
        } else if (state is MasterDataLoading && state.type == 'wards') {
          isLoadingWards = true;
        } else if (state is MasterDataError && state.type == 'wards') {
          // Error state handled below
        }

        // Show error state only when there's an actual API error, not validation error
        if (state is MasterDataError && state.type == 'wards') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    SizedBox(width: AppDimensions.spacingXS),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              DropdownButtonFormField<String>(
                value: null,
                decoration: InputDecoration(
                  hintText: 'Lỗi tải dữ liệu',
                  prefixIcon: Icon(
                    TablerIcons.building,
                    color: AppColors.textSecondary,
                  ),
                  contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                ),
                isExpanded: true, // Fix overflow issue
                items: [],
                onChanged: null,
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Text(
                'Không thể tải danh sách phường/xã',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppColors.error,
                ),
              ),
            ],
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator and error color
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(width: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            DropdownButtonFormField<String>(
              key: _fieldKeys[key],
              value: _formData[key]?.toString().isNotEmpty == true
                  ? _formData[key]
                  : null,
              decoration: InputDecoration(
                hintText: isLoadingWards ? 'Đang tải...' : 'Chọn $label',
                prefixIcon: Icon(
                  TablerIcons.building,
                  color: AppColors.textSecondary,
                ),
                contentPadding: EdgeInsets.all(AppDimensions.paddingM),
                // Apply error styling when validation fails
                errorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
                ) : null,
                focusedErrorBorder: hasFieldError ? OutlineInputBorder(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                  borderSide: BorderSide(color: Theme.of(context).colorScheme.error, width: 2),
                ) : null,
              ),
              isExpanded: true, // Fix overflow issue
              items: wards.map((ward) {
                return DropdownMenuItem<String>(
                  value: ward.id,
                  child: Text(ward.name ?? ''),
                );
              }).toList(),
              onChanged: _formData[provinceKey]?.toString().isNotEmpty == true
                  ? _onWardChanged(key)
                  : null,
              validator: (value) {
                if (!_isValidating) return null;
                
                final requiredFields = _getCachedRequiredFields();
                final isRequired = requiredFields.contains(key);
                
                if (isRequired && (value == null || value.isEmpty)) {
                  return 'Vui lòng chọn ${label.toLowerCase()}';
                }
                return null;
              },
            ),
          ],
        );
      },
    ),
    );
  }

  // Note: Loading state is managed by BlocBuilder

  Widget _buildSwitchField(String label, String key, String description) {
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  SizedBox(height: AppDimensions.spacingXS),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
                SizedBox(height: AppDimensions.spacingXS),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _formData[key] ?? false,
            onChanged: (value) {
              setState(() {
                _formData[key] = value;
              });
              _debouncedUpdateDetails();
            },
            activeColor: AppColors.kienlongOrange,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: AppDimensions.iconM),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  value,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool _shouldShowCalculation() {
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      return _formData['loan_amount']?.toString().isNotEmpty == true &&
             _formData['loan_term']?.toString().isNotEmpty == true;
    }
    
    return (widget.product?.code == 'PERSONAL_LOAN' || 
            widget.product?.code == 'MORTGAGE_LOAN' ||
            widget.product?.code == 'AUTO_LOAN' ||
            widget.product?.code == 'BUSINESS_LOAN') &&
           _formData['amount']?.toString().isNotEmpty == true &&
           _formData['term']?.toString().isNotEmpty == true;
  }

  Widget _buildCalculationPreview() {
    return RepaintBoundary(
      child: _buildCalculationContent(),
    );
  }
  
  Widget _buildCalculationContent() {
    // Handle installment loan calculation
    if (widget.product?.code == 'GOLD_LOAN' || widget.product?.code == 'MANGO') {
      return _buildInstallmentLoanCalculation();
    }
    
    // Original calculation for other loan types
    final amount = double.tryParse(_formData['amount']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final term = int.tryParse(_formData['term']?.toString() ?? '0') ?? 0;
    final rate = double.tryParse(_formData['interestRate']?.toString() ?? '12') ?? 12;
    
    if (amount <= 0 || term <= 0) return const SizedBox.shrink();
    
    final monthlyRate = rate / 100 / 12;
    final monthlyPayment = amount * monthlyRate * Math.pow(1 + monthlyRate, term) / 
                          (Math.pow(1 + monthlyRate, term) - 1);
    final totalInterest = (monthlyPayment * term) - amount;
    
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingL),
      decoration: BoxDecoration(
        color: AppColors.kienlongOrange.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.kienlongOrange.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                TablerIcons.calculator,
                color: AppColors.kienlongOrange,
                size: AppDimensions.iconM,
              ),
              SizedBox(width: AppDimensions.spacingS),
              Text(
                'Dự kiến',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppDimensions.spacingM),
          _buildCalculationRow('Số tiền', '${_formatCurrency(amount.round().toString())} VND'),
          _buildCalculationRow('Thời hạn', '$term tháng'),
          _buildCalculationRow('Góp hàng tháng', '~${_formatCurrency(monthlyPayment.round().toString())} VND'),
          _buildCalculationRow('Tổng lãi', '~${_formatCurrency(totalInterest.round().toString())} VND'),
        ],
      ),
    );
  }
  
  Widget _buildInstallmentLoanCalculation() {
    // Card đã được xóa theo yêu cầu
    return const SizedBox.shrink();
  }

  Widget _buildCalculationRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXS),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  // Expandable Card Widget
  Widget _buildExpandableCard(
    String title,
    IconData icon,
    Color color,
    List<Widget> children,
    String expandedKey,
  ) {
    final isExpanded = _formData[expandedKey] ?? true;
    
    return RepaintBoundary(
      child: Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _formData[expandedKey] = !isExpanded;
              });
              _debouncedUpdateDetails();
            },
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(AppDimensions.radiusM),
              topRight: Radius.circular(AppDimensions.radiusM),
            ),
            child: Container(
              padding: EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Row(
                children: [
                  Icon(icon, color: color, size: AppDimensions.iconM),
                  const SizedBox(width: AppDimensions.spacingM),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                  ),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: Icon(
                      TablerIcons.chevron_down,
                      color: color,
                      size: AppDimensions.iconS,
                    ),
                  ),
                ],
              ),
            ),
          ),
          AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: isExpanded ? null : 0,
            child: isExpanded
                ? Container(
                    padding: EdgeInsets.all(AppDimensions.paddingM),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: children,
                    ),
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    ),
    );
  }

  // Main Borrower Form
  List<Widget> _buildMainBorrowerForm() {
    return [
      // Document Information Section
      Text(
        'Thông tin nhận dạng giấy tờ',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.kienlongOrange,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      // QR/NFC Scanning Section - Moved up for better visibility
      Row(
        children: [
          Expanded(
            child: _buildActionButton(
              'Quét QR',
              TablerIcons.qrcode,
              AppColors.info,
              _openQrScanner,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildActionButton(
              'Quét NFC',
              TablerIcons.nfc,
              AppColors.warning,
              () {
                // TODO: Implement NFC scanning
                debugPrint('NFC Scan tapped');
              },
            ),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      _buildTextField('Họ và tên', 'borrower_name', 'Nhập họ và tên đầy đủ'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildMasterDataDropdown(
              'Loại giấy tờ',
              'borrower_id_type',
              [],
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildTextField(
              keyboardType: TextInputType.number,
              'Số GTTT', 
              'borrower_id_number', 
              'Nhập số giấy tờ',
              onChanged: (value) {
                // Auto-load bank accounts when ID card number is entered
                if (value != null && value.length >= 9) {
                  _loadBankAccountsForBorrower();
                }
              },
            ),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildDateField('Ngày cấp', 'borrower_id_issue_date'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildDateField('Ngày hết hạn', 'borrower_id_expiry_date'),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildTextField('Nơi cấp', 'borrower_id_issue_place', 'Nhập nơi cấp'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildDateField('Ngày sinh', 'borrower_birth_date'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildMasterDataDropdown(
              'Giới tính',
              'borrower_gender',
              [],
            ),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      // Address Information
      Text(
        'Hộ khẩu thường trú',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.kienlongOrange,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildProvinceDropdown(
              'Tỉnh/thành phố',
              'borrower_permanent_province',
            ),
            const SizedBox(height: AppDimensions.spacingM),
     _buildWardDropdown(
              'Phường/Xã',
              'borrower_permanent_district',
              'borrower_permanent_province',
            ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Địa chỉ cụ thể',
        'borrower_permanent_address',
        'Nhập địa chỉ thường trú',
        maxLines: 2,
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      // Personal Information
      Text(
        'Thông tin cá nhân',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.kienlongOrange,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildMasterDataDropdown(
              'Tình trạng hôn nhân',
              'borrower_marital_status',
              [],
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildTextField('Số điện thoại', 'borrower_phone', '0xxxxxxxxx', keyboardType: TextInputType.number,),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildSwitchField(
        'Địa chỉ hiện tại trùng với địa chỉ thường trú',
        'borrower_current_same_permanent',
        'Đánh dấu nếu địa chỉ hiện tại giống thường trú',
      ),
      
      if (_formData['borrower_current_same_permanent'] != true) ...[
        const SizedBox(height: AppDimensions.spacingM),
        _buildProvinceDropdown(
          'Tỉnh/thành phố',
          'borrower_current_province',
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildWardDropdown(
          'Phường/Xã',
          'borrower_current_district',
          'borrower_current_province',
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildTextField(
          'Địa chỉ hiện tại',
          'borrower_current_address',
          'Nhập địa chỉ hiện tại',
          maxLines: 2,
        ),
      ],
      
      const SizedBox(height: AppDimensions.spacingM),
    ];
  }
  
  // Co-borrower Section
  Widget _buildCoBorrowerSection() {
    return Column(
      children: [
        _buildSwitchField(
          'Có người đồng vay',
          'has_co_borrower',
          'Bật nếu có người đồng vay trong giao dịch này',
        ),
        
        if (_formData['has_co_borrower'] == true) ...[
          const SizedBox(height: AppDimensions.spacingM),
          _buildExpandableCard(
            'Thông tin người đồng vay',
            TablerIcons.users,
            AppColors.kienlongSkyBlue,
            _buildCoBorrowerForm(),
            'co_borrower_card_expanded',
          ),
        ],
      ],
    );
  }
  
  // Co-borrower Form
  List<Widget> _buildCoBorrowerForm() {
    return [
      // Document Information Section
      Text(
        'Thông tin nhận dạng giấy tờ',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.kienlongSkyBlue,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      // QR/NFC Scanning Section for Co-borrower
      Row(
        children: [
          Expanded(
            child: _buildActionButton(
              'Quét QR',
              TablerIcons.qrcode,
              AppColors.info,
              _openCoBorrowerQrScanner,
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildActionButton(
              'Quét NFC',
              TablerIcons.nfc,
              AppColors.warning,
              () {
                // TODO: Implement NFC scanning for co-borrower
                debugPrint('Co-borrower NFC Scan tapped');
              },
            ),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      _buildTextField('Họ và tên', 'co_borrower_name', 'Nhập họ và tên đầy đủ'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        children: [
          Expanded(
            child: _buildMasterDataDropdown(
              'Loại giấy tờ',
              'co_borrower_id_type',
              [],
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildTextField('Số GTTT', 'co_borrower_id_number', 'Nhập số giấy tờ', keyboardType: TextInputType.number,),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildDateField('Ngày cấp', 'co_borrower_id_issue_date'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildDateField('Ngày hết hạn', 'co_borrower_id_expiry_date'),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildTextField('Nơi cấp', 'co_borrower_id_issue_place', 'Nhập nơi cấp'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildDateField('Ngày sinh', 'co_borrower_birth_date'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildMasterDataDropdown(
              'Giới tính',
              'co_borrower_gender',
              [],
            ),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      // Address and Personal Information
      Text(
        'Hộ khẩu thường trú',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.kienlongSkyBlue,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildProvinceDropdown(
        'Tỉnh/thành phố',
        'co_borrower_permanent_province',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildWardDropdown(
        'Phường/Xã',
        'co_borrower_permanent_district',
        'co_borrower_permanent_province',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField(
        'Địa chỉ cụ thể',
        'co_borrower_permanent_address',
        'Nhập địa chỉ thường trú',
        maxLines: 2,
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        children: [
          Expanded(
            child: _buildMasterDataDropdown(
              'Tình trạng hôn nhân',
              'co_borrower_marital_status',
              [],
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildTextField('Số điện thoại', 'co_borrower_phone', '0xxxxxxxxx',keyboardType: TextInputType.number,),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildSwitchField(
        'Địa chỉ hiện tại trùng với địa chỉ thường trú',
        'co_borrower_current_same_permanent',
        'Đánh dấu nếu địa chỉ hiện tại giống thường trú',
      ),
      
      if (_formData['co_borrower_current_same_permanent'] != true) ...[
        const SizedBox(height: AppDimensions.spacingM),
        _buildProvinceDropdown(
          'Tỉnh/thành phố',
          'co_borrower_current_province',
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildWardDropdown(
          'Phường/Xã',
          'co_borrower_current_district',
          'co_borrower_current_province',
        ),
        const SizedBox(height: AppDimensions.spacingM),
        _buildTextField(
          'Địa chỉ hiện tại',
          'co_borrower_current_address',
          'Nhập địa chỉ hiện tại',
          maxLines: 2,
        ),
      ],
      
      const SizedBox(height: AppDimensions.spacingM),
    ];
  }
  
  // Loan Proposal Form
  List<Widget> _buildLoanProposalForm() {
    return [
      // Loan Type
      Text(
        'Hình thức vay vốn',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.success,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        children: [
          Expanded(
            child: _buildRadioTile('Có TSĐB', 'loan_type', 'Có TSĐB'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildRadioTile('Không TSĐB', 'loan_type', 'Không TSĐB'),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      // Capital Information
      Text(
        'Phương án vay vốn',
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.bold,
          color: AppColors.success,
        ),
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildAmountField('Vốn tự có', 'own_capital', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildAmountField('Số tiền đề nghị vay', 'loan_amount', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        children: [
          Expanded(
            child: _buildMasterDataDropdown(
              'Thời hạn vay',
              'loan_term',
              _loanTerms,
              (config) => '${config.label}',
            ),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildCalculatedField(
              'Tổng nhu cầu',
              'total_capital_need',
              () {
                final ownCapital = int.tryParse(_formData['own_capital']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
                final loanAmount = int.tryParse(_formData['loan_amount']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
                final total = ownCapital + loanAmount;
                // Cap at 2 billion maximum
                final cappedTotal = total > 2000000000 ? 2000000000 : total;
                return cappedTotal.toString();
              },
              readOnly: true,
            ),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      // Branch and Method Information
      _buildTextField(
        'CN/PGD', 
        'branch_code', 
        _getBranchNameFromAuth(), 
        readOnly: true,
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildMasterDataDropdown(
        'Phương thức vay',
        'loan_method',
        [],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildMasterDataDropdown(
        'Mục đích sử dụng vốn',
        'loan_purpose',
        _loanPurposes,
      ),
      
      if (_formData['loan_purpose'] == 'Khác') ...[
        const SizedBox(height: AppDimensions.spacingM),
        _buildTextField('Tên mục đích', 'loan_purpose_other', 'Nhập mục đích khác'),
      ],
      
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildMasterDataReadOnlyField('Hình thức trả nợ', 'repayment_method'),
      const SizedBox(height: AppDimensions.spacingM),
      
      // Disbursement Method - Using master data with radio buttons
      _buildMasterDataRadioGroup(
        'Phương thức giải ngân',
        'disbursement_method',
      ),
      
      if (_shouldShowBankAccountField()) ...[
        const SizedBox(height: AppDimensions.spacingM),
        _buildBankAccountDropdown(
          'Số tài khoản nhận tiền',
          'disbursement_account',
        ),
      ],
    ];
  }
  
  // Financial Information Form
    /// Helper method để kiểm tra có nên hiển thị trường tài khoản ngân hàng
  bool _shouldShowBankAccountField() {
    final disbursementMethodId = _formData['disbursement_method']?.toString();
    if (disbursementMethodId == null || disbursementMethodId.isEmpty) return false;
    
    // Get the disbursement method config to check its label/code
    final masterDataState = context.read<MasterDataBloc>().state;
    if (masterDataState is MasterDataLoaded) {
      final disbursementConfigs = masterDataState.configsByGroup[ConfigTypes.DISBURSEMENT_METHOD] ?? [];
      final selectedConfig = disbursementConfigs.firstWhere(
        (config) => config.id == disbursementMethodId,
        orElse: () => ConfigModel(id: '', code: '', label: ''),
      );
      
      // Check if the selected disbursement method corresponds to bank transfer
      final label = selectedConfig.label?.toLowerCase() ?? '';
      final code = selectedConfig.code?.toLowerCase() ?? '';
      
      return label.contains('chuyển') || label.contains('khoản') ||
             code.contains('transfer') || code.contains('bank') ||
             label.contains('transfer') || label.contains('bank');
    }
    
    // Fallback check on ID itself
    return disbursementMethodId.toLowerCase().contains('chuyen') ||
           disbursementMethodId.toLowerCase().contains('transfer') ||
           disbursementMethodId.toLowerCase().contains('bank');
  }

  /// Helper method để kiểm tra loại nguồn thu
  bool _isBusinessIncomeSource(String? incomeSource) {
    if (incomeSource == null || incomeSource.isEmpty) return false;
    
    final lowerSource = incomeSource.toLowerCase();
    return lowerSource.contains('kinh doanh') || 
           lowerSource.contains('business') ||
           lowerSource.contains('thương mại') ||
           lowerSource.contains('buôn bán');
  }

  /// Load bank accounts cho borrower dựa trên ID card number
  void _loadBankAccountsForBorrower() {
    final idCardNo = _formData['borrower_id_number']?.toString();
    if (idCardNo != null && idCardNo.isNotEmpty) {
      context.read<MasterDataBloc>().add(LoadBankAccountsEvent(idCardNo));
    }
  }

  /// Load bank accounts cho khách hàng đã chọn từ bước trước
  void _loadBankAccountsForSelectedCustomer() {
    String? idCardNo;

    // Thứ tự ưu tiên: 1) Profile từ _authService, 2) selectedCustomer, 3) Không call API
    try {
      // 1. Lấy từ profile của user hiện tại từ _authService
      final currentUser = _authService.currentUser;
      if (currentUser?.profile?.personIdCardNo != null && 
          currentUser!.profile!.personIdCardNo!.isNotEmpty) {
        idCardNo = currentUser.profile!.personIdCardNo;
        debugPrint('Using ID card from current user profile: $idCardNo');
      }
      // 3. Nếu không có từ cả hai nguồn trên, không call API
      else {
        debugPrint('No ID card number found from profile or selected customer - skipping bank account loading');
        return;
      }
    } catch (e) {
      debugPrint('Error getting ID card number: $e');
      return;
    }
    
    // Call API với idCardNo đã lấy được
    if (idCardNo != null && idCardNo.isNotEmpty) {
      debugPrint('Loading bank accounts for customer ID card: $idCardNo');
      context.read<MasterDataBloc>().add(LoadBankAccountsEvent(idCardNo));
    }
  }


  List<Widget> _buildFinancialInfoForm() {
    return [
      _buildMasterDataDropdown(
        'Nguồn thu',
        'income_source',
        _incomeSources,
      ),
      
      if (_isBusinessIncomeSource(_formData['income_source'])) ...[
        const SizedBox(height: AppDimensions.spacingM),
        _buildAmountField('Doanh số bình quân/ngày', 'daily_revenue', 'VND'),
      ],
      
      const SizedBox(height: AppDimensions.spacingM),
      _buildAmountField('Thu nhập bình quân/ngày', 'daily_income', 'VND'),
      
      if (_isBusinessIncomeSource(_formData['income_source'])) ...[
        const SizedBox(height: AppDimensions.spacingL),
        Text(
          'Địa điểm sản xuất - kinh doanh',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.info,
          ),
        ),
        const SizedBox(height: AppDimensions.spacingM),
        
        Column(
          children: [
            _buildProvinceDropdown(
              'Tỉnh/thành phố',
              'business_location_province',
            ),
            const SizedBox(height: AppDimensions.spacingM),
            _buildWardDropdown(
              'Phường/Xã',
              'business_location_district',
              'business_location_province',
            ),
          ],
        ),
        const SizedBox(height: AppDimensions.spacingM),
        
        _buildTextField(
          'Địa chỉ',
          'business_location_address',
          'Nhập địa chỉ cụ thể',
          maxLines: 2,
        ),
      ],
    ];
  }


  List<Widget> _buildCollateralForm() {
    return [
      // Basic Collateral Information
      _buildCollateralCategoryDropdown(
        'Loại tài sản',
        'collateral_type',
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildAmountField('Giá trị tài sản', 'collateral_value', 'VND'),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildCalculatedField(
        'Giá trị tài sản (bằng chữ)',
        'collateral_value_text',
        () => _formData['collateral_value_text']?.toString() ?? 'Không đồng',
        readOnly: true,
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildTextField('Hiện trạng tài sản', 'collateral_condition', 'Mô tả tình trạng tài sản'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        children: [
          Expanded(
            child: _buildTextField('Chủ sở hữu tài sản', 'collateral_owner', '', readOnly: true),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildTextField('Năm sinh', 'collateral_owner_birth_year', '', readOnly: true),
          ),
        ],
      ),
      SizedBox(height: AppDimensions.spacingL),
      
      const SizedBox(height: AppDimensions.spacingM),
    ];
  }

  // Detailed Collateral Form
  List<Widget> _buildDetailedCollateralForm() {
    return [
      // QR Scanning for Vehicle Registration - Moved up before plate number
      _buildActionButton(
        'Quét QR đăng ký xe',
        TablerIcons.qrcode,
        AppColors.warning,
        _openVehicleQrScanner,
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildTextField('Biển kiểm soát', 'vehicle_plate_number', 'Nhập biển số xe'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField('Tên tài sản', 'vehicle_name', 'Nhập tên loại xe'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField('Số khung', 'vehicle_frame_number', 'Nhập số khung'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField('Số máy', 'vehicle_engine_number', 'Nhập số máy'),
      const SizedBox(height: AppDimensions.spacingM),
      _buildTextField('Số giấy chứng nhận đăng ký xe', 'vehicle_registration_number', 'Nhập số đăng ký'),
      const SizedBox(height: AppDimensions.spacingM),
      
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: _buildTextField('Nơi cấp', 'vehicle_registration_place', 'Nhập nơi cấp'),
          ),
          const SizedBox(width: AppDimensions.spacingM),
          Expanded(
            child: _buildDateField('Ngày cấp', 'vehicle_registration_date'),
          ),
        ],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildMasterDataDropdown(
        'Tình trạng tài sản khi giao',
        'vehicle_condition_at_handover',
        [],
      ),
      const SizedBox(height: AppDimensions.spacingM),
      
      _buildCalculatedField(
        'Tổng trị giá tài sản bảo đảm',
        'total_collateral_value',
        () => _formData['collateral_value']?.toString() ?? '0',
        readOnly: true,
      ),
      const SizedBox(height: AppDimensions.spacingM),
    ];
  }

  // Helper Widgets - Optimized Date Field using AppDateField
  Widget _buildDateField(String label, String key) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    
    // Sử dụng default date range của AppDateField - đơn giản và hợp lý
    return AppDateField(
              key: _fieldKeys[key],
      label: label,
      required: isRequired,
      initialValue: _formData[key]?.toString(),
      shouldValidate: _isValidating, // Chỉ validate khi _isValidating = true
      onChanged: (value) {
        setState(() {
          _formData[key] = value;
        });
        _debouncedUpdateDetails();
      },
      validator: (value) {
        if (!_isValidating) return null;
        
        final requiredFields = _getCachedRequiredFields();
        final isRequired = requiredFields.contains(key);
        
        if (isRequired && (value == null || value.isEmpty)) {
          return 'Vui lòng chọn ${label.toLowerCase()}';
        }
        return null;
      },
    );
  }
  
  Widget _buildRadioTile(String title, String key, String value) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(
          color: _formData[key] == value ? AppColors.kienlongOrange : AppColors.borderLight,
          width: _formData[key] == value ? 2 : 1,
        ),
      ),
      child: RadioListTile<String>(
        title: Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: _formData[key] == value ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        value: value,
        groupValue: _formData[key],
        onChanged: (selectedValue) {
          setState(() {
            _formData[key] = selectedValue;
          });
          _debouncedUpdateDetails();
        },
        activeColor: AppColors.kienlongOrange,
        contentPadding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingS),
      ),
    );
  }
  
  Widget _buildCalculatedField(
    String label,
    String key,
    String Function() calculator, {
    bool readOnly = false,
  }) {
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    // For calculated fields, ensure controller text is always up to date
    if (readOnly) {
      final currentValue = _formData[key]?.toString() ?? '';
      final controller = _getController(key);
      if (controller.text != currentValue) {
        // Use _updateController to avoid triggering unnecessary listeners
        _updateController(key, currentValue);
      }
    }
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator and error color
        Row(
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: hasFieldError ? Theme.of(context).colorScheme.error : null,
              ),
            ),
            if (isRequired) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.spacingS),
        Container(
          decoration: BoxDecoration(
            color: readOnly ? AppColors.neutral100 : Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
            border: Border.all(color: AppColors.borderLight),
          ),
          child: TextFormField(
            controller: _getController(key),
            readOnly: readOnly,
            decoration: InputDecoration(
              hintText: readOnly ? 'Tự động tính' : 'Nhập giá trị',
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
            ),
            onChanged: readOnly ? null : (value) {
              setState(() {
                _formData[key] = value;
              });
            },
          ),
        ),
        // Show formatted currency for total capital need
        if (key == 'total_capital_need' && _shouldShowAmountDisplay(key))
          Padding(
            padding: EdgeInsets.only(top: AppDimensions.spacingS),
            child: Text(
              '≈ ${_getCachedFormattedCurrency(_formData[key].toString())} VND',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppColors.kienlongOrange,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildActionButton(
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      child: TextButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, color: color, size: AppDimensions.iconS),
        label: Text(
          label,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: TextButton.styleFrom(
          padding: EdgeInsets.all(AppDimensions.paddingM),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppDimensions.radiusM),
          ),
        ),
      ),
    );
  }
  
  // Number to words converter (simplified version)
  /// Update calculated fields related to collateral value
  void _updateCollateralCalculatedFields() {
    final value = _formData['collateral_value']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0';
    final textValue = _convertNumberToWords(value);
    
    // Only update if values have actually changed
    if (_formData['collateral_value_text'] != textValue) {
      _formData['collateral_value_text'] = textValue;
      _updateController('collateral_value_text', textValue);
    }
    
    if (_formData['total_collateral_value'] != value) {
      _formData['total_collateral_value'] = value;
    }
  }

  /// Update total capital need calculation
  void _updateTotalCapitalNeed() {
    final ownCapital = int.tryParse(_formData['own_capital']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final loanAmount = int.tryParse(_formData['loan_amount']?.toString().replaceAll(RegExp(r'[^\d]'), '') ?? '0') ?? 0;
    final totalCapitalNeed = ownCapital + loanAmount;
    
    // Validate total doesn't exceed 2 billion
    final validatedTotal = totalCapitalNeed > 2000000000 ? 2000000000 : totalCapitalNeed;
    
    if (_formData['total_capital_need'] != validatedTotal.toString()) {
      _formData['total_capital_need'] = validatedTotal.toString();
      _updateController('total_capital_need', validatedTotal.toString());
    }
  }

  /// Validate own capital amount
  String? _validateOwnCapital(String value) {
    if (value.isEmpty) return null;
    
    // Check for negative sign
    if (value.contains('-')) return 'Vốn tự có không được âm';
    
    // Check if input contains only digits
    if (!RegExp(r'^\d+$').hasMatch(value)) return 'Vốn tự có chỉ được chứa số';
    
    final amount = int.tryParse(value) ?? 0;
    
    // Check range: 0 to 1 billion
    if (amount > 1000000000) {
      return 'Vốn tự có tối đa 1 tỷ VND';
    }
    
    return null;
  }

  /// Validate loan amount
  String? _validateLoanAmount(String value) {
    if (value.isEmpty) return null;
    
    // Check for negative sign
    if (value.contains('-')) return 'Số tiền vay không được âm';
    
    // Check if input contains only digits
    if (!RegExp(r'^\d+$').hasMatch(value)) return 'Số tiền vay chỉ được chứa số';
    
    final amount = int.tryParse(value) ?? 0;
    
    // Check range: 1 million to 1 billion
    if (amount < 1000000) {
      return 'Số tiền vay tối thiểu 1.000.000 VND';
    }
    
    if (amount > 1000000000) {
      return 'Số tiền vay tối đa 1 tỷ VND';
    }
    
    return null;
  }

  // Cache for number to words conversion to avoid repeated calculations
  final Map<String, String> _numberToWordsCache = {};
  
  String _convertNumberToWords(String number) {
    // Return cached result if available
    if (_numberToWordsCache.containsKey(number)) {
      return _numberToWordsCache[number]!;
    }
    
    String result;
    if (number.isEmpty || number == '0') {
      result = 'Không đồng';
    } else {
      final amount = int.tryParse(number) ?? 0;
      if (amount == 0) {
        result = 'Không đồng';
      } else if (amount < 0) {
        // Handle negative numbers
        result = 'Không đồng';
      } else {
        // Use NumberToWordsUtil for proper conversion
        try {
          result = NumberToWordsUtil.convertToWords(amount);
        } catch (e) {
          // Fallback to simplified conversion if NumberToWordsUtil fails
          if (amount >= 1000000000) {
            result = '${(amount / 1000000000).toStringAsFixed(1)} tỷ đồng';
          } else if (amount >= 1000000) {
            result = '${(amount / 1000000).toStringAsFixed(1)} triệu đồng';
          } else if (amount >= 1000) {
            result = '${(amount / 1000).toStringAsFixed(1)} nghìn đồng';
          } else {
            result = '$amount đồng';
          }
        }
      }
    }
    
    // Cache the result
    _numberToWordsCache[number] = result;
    return result;
  }

  /// Build dropdown for collateral categories
  Widget _buildCollateralCategoryDropdown(
    String label,
    String key,
  ) {
    // Ensure GlobalKey exists for this field
    if (!_fieldKeys.containsKey(key)) {
      _fieldKeys[key] = GlobalKey();
    }
    
    // Check if field is required and has validation error
    final requiredFields = _getCachedRequiredFields();
    final isRequired = requiredFields.contains(key);
    final hasFieldError = _isValidating && isRequired && (_formData[key]?.toString().isEmpty ?? true);
    
    return BlocBuilder<MasterDataBloc, MasterDataState>(
      buildWhen: (previous, current) {
        // Rebuild when state changes to loading, loaded, or error
        return current is MasterDataLoading || 
               current is MasterDataLoaded || 
               current is MasterDataError;
      },
      builder: (context, state) {
        List<CollateralCategoryModel> availableCategories = [];
        bool isLoading = false;

        // Get collateral categories from state
        if (state is MasterDataLoaded) {
          availableCategories = state.collateralCategories;
        } else if (state is MasterDataLoading && state.type == 'collateral_categories') {
          isLoading = true;
        }

        // Show error state only when there's an actual API error, not validation error
        if (state is MasterDataError && state.type == 'collateral_categories') {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Label with required indicator and error color
              Row(
                children: [
                  Text(
                    label,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                    ),
                  ),
                  if (isRequired) ...[
                    const SizedBox(width: 4),
                    Text(
                      '*',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(height: AppDimensions.spacingS),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingM),
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.error,
                    width: 1.5,
                  ),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusM),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: null,
                    hint: Text(
                      'Lỗi tải dữ liệu',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.error,
                      ),
                    ),
                    items: const [],
                    onChanged: null,
                  ),
                ),
              ),
            ],
          );
        }

        // Remove duplicates based on ID and filter out items with null IDs
        final uniqueCategories = <String, CollateralCategoryModel>{};
        for (final category in availableCategories) {
          if (category.id != null && category.id!.isNotEmpty) {
            uniqueCategories[category.id!] = category;
          }
        }
        final filteredCategories = uniqueCategories.values.toList();

        // Handle migration from name-based to ID-based values
        String? currentValue = _formData[key]?.toString();
        if (currentValue != null && currentValue.isNotEmpty && filteredCategories.isNotEmpty) {
          // Check if current value is an ID (exists in filtered categories)
          final hasValidId = filteredCategories.any((cat) => cat.id == currentValue);
          
          if (!hasValidId) {
            // Current value might be a name, try to find corresponding ID
            CollateralCategoryModel? categoryByName;
            try {
              categoryByName = filteredCategories.firstWhere(
                (cat) => cat.name == currentValue,
              );
            } catch (e) {
              categoryByName = null;
            }
            if (categoryByName != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                if (mounted) {
                  setState(() {
                    _formData[key] = categoryByName!.id ?? '';
                  });
                  _debouncedUpdateDetails();
                }
              });
              currentValue = categoryByName.id;
            } else {
              // Invalid value, reset to empty
              currentValue = null;
            }
          }
        }

        // Auto-select first item if no valid selection and data is available
        if (filteredCategories.isNotEmpty && 
            (currentValue == null || currentValue.isEmpty)) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _formData[key] = filteredCategories.first.id ?? '';
              });
              _debouncedUpdateDetails();
            }
          });
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label with required indicator
            Row(
              children: [
                Text(
                  label,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: hasFieldError ? Theme.of(context).colorScheme.error : null,
                  ),
                ),
                if (isRequired) ...[
                  const SizedBox(width: 4),
                  Text(
                    '*',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
            const SizedBox(height: AppDimensions.spacingS),
            
            // Dropdown
            Container(
              padding: const EdgeInsets.symmetric(horizontal: AppDimensions.spacingM),
              decoration: BoxDecoration(
                border: Border.all(
                  color: hasFieldError 
                      ? Theme.of(context).colorScheme.error
                      : Theme.of(context).colorScheme.outline,
                  width: hasFieldError ? 1.5 : 1,
                ),
                borderRadius: BorderRadius.circular(AppDimensions.radiusM),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<String>(
                  value: currentValue,
                  hint: Text(
                    isLoading ? 'Đang tải...' : 
                    filteredCategories.isEmpty ? 'Không có dữ liệu' : 'Chọn loại tài sản',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                  isExpanded: true,
                  items: filteredCategories
                      .map((category) {
                        final categoryName = category.name ?? 'Không tên';
                        final categoryId = category.id ?? '';
                        return DropdownMenuItem<String>(
                          value: categoryId,
                          child: Text(
                            categoryName,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        );
                      })
                      .toList(),
                  onChanged: isLoading ? null : (String? value) {
                    if (value != null) {
                      setState(() {
                        _formData[key] = value;
                      });
                      _debouncedUpdateDetails();
                    }
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}


// Math pow function replacement
class Math {
  static double pow(double base, num exponent) {
    if (exponent == 0) return 1;
    if (exponent == 1) return base;
    
    double result = 1;
    for (int i = 0; i < exponent; i++) {
      result *= base;
    }
    return result;
  }
}
