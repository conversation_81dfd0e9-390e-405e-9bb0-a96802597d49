import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../../core/theme/index.dart';
import '../models/index.dart';
import '../../products/models/product_model.dart';
import '../../customers/models/customer_model.dart';
import '../../../shared/utils/product_icon_mapper.dart';

class ReviewConfirmStep extends StatefulWidget {
  final ProductModel? selectedProduct;
  final CustomerModel? selectedCustomer;
  final Map<String, dynamic> transactionDetails;
  final List<DocumentModel> documents;
  final VoidCallback? onConfirm;
  final VoidCallback? onEditCustomer;
  final VoidCallback? onEditProduct;
  final VoidCallback? onEditDocuments;

  const ReviewConfirmStep({
    super.key,
    this.selectedProduct,
    this.selectedCustomer,
    required this.transactionDetails,
    required this.documents,
    this.onConfirm,
    this.onEditCustomer,
    this.onEditProduct,
    this.onEditDocuments,
  });

  @override
  State<ReviewConfirmStep> createState() => _ReviewConfirmStepState();
}

class _ReviewConfirmStepState extends State<ReviewConfirmStep>
    with AutomaticKeepAliveClientMixin {
  
  @override
  bool get wantKeepAlive => true;

  String _getTransactionId() {
    final productCode = widget.selectedProduct?.code;
    if (productCode == null) return DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    
    final code = productCode.length >= 2 ? productCode.substring(0, 2).toUpperCase() : productCode.toUpperCase();
    return '$code${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
  }

  Color _getProductColor() {
    if (widget.selectedProduct == null) return AppColors.kienlongOrange;
    
    return widget.selectedProduct!.hasCustomColor
        ? widget.selectedProduct!.displayColor
        : AppColors.kienlongOrange;
  }

  IconData _getProductIcon() {
    if (widget.selectedProduct == null) return TablerIcons.file_text;
    
    return ProductIconMapper.getIcon(
      widget.selectedProduct!.displayConfig?.icon ?? widget.selectedProduct!.group,
    );
  }

  String _formatCurrency(String value) {
    if (value.isEmpty) return '';
    final cleanValue = value.replaceAll(RegExp(r'[^\d]'), '');
    if (cleanValue.isEmpty) return '';
    
    final number = int.tryParse(cleanValue) ?? 0;
    return number.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (match) => '${match[1]}.',
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Bắt buộc phải gọi!
    
    if (widget.selectedProduct == null || widget.selectedCustomer == null) {
      return _buildIncompleteData(context);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            '✅ Xác nhận tạo giao dịch',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.kienlongOrange,
            ),
          ),
          SizedBox(height: AppDimensions.spacingS),
          Text(
            'Xem lại thông tin trước khi tạo giao dịch',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          SizedBox(height: AppDimensions.spacingL),

          // Transaction Summary Card
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingS),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  _getProductColor(),
                  _getProductColor().withValues(alpha: 0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(AppDimensions.radiusL),
              boxShadow: [
                BoxShadow(
                  color: _getProductColor().withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      width: 56,
                      height: 56,
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _getProductIcon(),
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    SizedBox(width: AppDimensions.spacingM),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _getTransactionId(),
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            widget.selectedProduct?.displayName ?? '',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                if (_getMainAmountFromDetails(widget.transactionDetails) != null) ...[
                  SizedBox(height: AppDimensions.spacingL),
                  Text(
                    '${_formatCurrency(_getMainAmountFromDetails(widget.transactionDetails)!)} VND',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),

          SizedBox(height: AppDimensions.spacingL),

          // Customer Information
          _buildInfoSection(
            context,
            'Thông tin khách hàng',
            TablerIcons.user,
            AppColors.kienlongSkyBlue,
            [
              _InfoItem('Họ tên', widget.selectedCustomer?.fullName ?? ''),
              _InfoItem('Số điện thoại', widget.selectedCustomer?.phoneNumber ?? ''),
              _InfoItem('Email', widget.selectedCustomer?.email ?? ''),
              _InfoItem('Loại khách hàng', 'Cá nhân'), // Default for now
              // Note: tag field doesn't exist in CustomerModel
            ],
            onEdit: widget.onEditCustomer,
          ),

          SizedBox(height: AppDimensions.spacingM),

          // Product Details
          if (widget.transactionDetails.isNotEmpty)
            _buildInfoSection(
              context,
              'Chi tiết sản phẩm',
              _getProductIcon(),
              _getProductColor(),
              _buildProductDetails(widget.transactionDetails),
              onEdit: widget.onEditProduct,
            ),

          SizedBox(height: AppDimensions.spacingM),

          // Documents Status
          _buildDocumentsSection(context, widget.documents, onEdit: widget.onEditDocuments),

          SizedBox(height: AppDimensions.spacingM),

          SizedBox(height: AppDimensions.spacingL),
  ],
            ),
          );
  }

  Widget _buildIncompleteData(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.paddingXL),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              TablerIcons.alert_circle,
              size: 64,
              color: AppColors.warning,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Thông tin chưa đầy đủ',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppColors.warning,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: AppDimensions.spacingS),
            Text(
              'Vui lòng hoàn thành các bước trước để xem thông tin tổng hợp',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
    List<_InfoItem> items, {
    VoidCallback? onEdit,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (onEdit != null)
                  GestureDetector(
                    onTap: onEdit,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: color.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            TablerIcons.edit,
                            color: color,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            'Chỉnh sửa',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: color,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

                     // Items
           for (final item in items)
             _buildInfoRow(context, item.label, item.value),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    // Check if this is a section header
    if (label.startsWith('━━━') && label.endsWith('━━━')) {
      return Container(
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingM,
          vertical: AppDimensions.paddingM,
        ),
        margin: EdgeInsets.only(top: AppDimensions.spacingS),
        decoration: BoxDecoration(
          color: AppColors.kienlongOrange.withValues(alpha: 0.05),
          border: Border(
            left: BorderSide(
              color: AppColors.kienlongOrange,
              width: 4,
            ),
          ),
        ),
        child: Row(
          children: [
            Icon(
              TablerIcons.info_circle,
              color: AppColors.kienlongOrange,
              size: AppDimensions.iconS,
            ),
            SizedBox(width: AppDimensions.spacingS),
            Expanded(
              child: Text(
                label.replaceAll('━', '').trim(),
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: AppColors.kienlongOrange,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      );
    }
    
    // Regular info row
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingS,
      ),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppColors.borderLight,
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentsSection(BuildContext buildContext, List<DocumentModel> documents, {VoidCallback? onEdit}) {
    
    // Get required and optional documents based on product type
    final requiredDocs = _getRequiredDocumentsForProduct();
    final optionalDocs = _getOptionalDocumentsForProduct();
    
    final totalRequiredCount = requiredDocs.length;
    final totalOptionalCount = optionalDocs.length;
    
    // Count uploaded documents with improved matching
    final uploadedRequiredCount = requiredDocs.where((docName) {
      return documents.any((doc) => 
          doc.hasFiles && (
            doc.name == docName || 
            doc.name.contains(docName) || 
            docName.contains(doc.name)
          )
      );
    }).length;
    
    final uploadedOptionalCount = optionalDocs.where((docName) {
      return documents.any((doc) => 
          doc.hasFiles && (
            doc.name == docName || 
            doc.name.contains(docName) || 
            docName.contains(doc.name)
          )
      );
    }).length;
    
    final totalUploadedDocuments = documents.where((doc) => doc.hasFiles).length;
    debugPrint('Document counts - Required: $uploadedRequiredCount/$totalRequiredCount, Optional: $uploadedOptionalCount/$totalOptionalCount, Total uploaded: $totalUploadedDocuments');
    
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(buildContext).cardColor,
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        border: Border.all(color: AppColors.borderLight),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowLight,
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Section Header
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            decoration: BoxDecoration(
              color: AppColors.info.withValues(alpha: 0.05),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppDimensions.radiusM),
                topRight: Radius.circular(AppDimensions.radiusM),
              ),
            ),
            child: Row(
              children: [
                Icon(TablerIcons.file_text, color: AppColors.info, size: AppDimensions.iconM),
                SizedBox(width: AppDimensions.spacingS),
                Expanded(
                  child: Text(
                    'Tài liệu',
                    style: Theme.of(buildContext).textTheme.titleMedium?.copyWith(
                      color: AppColors.info,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                if (onEdit != null) ...[
                  SizedBox(width: AppDimensions.spacingM),
                  GestureDetector(
                    onTap: onEdit,
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: AppDimensions.paddingS,
                        vertical: AppDimensions.paddingXS,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.info.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                        border: Border.all(
                          color: AppColors.info.withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            TablerIcons.edit,
                            color: AppColors.info,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingXS),
                          Text(
                            'Chỉnh sửa',
                            style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                              color: AppColors.info,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Document Status
          Container(
            padding: EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
              
                // All Uploaded Documents Section (for debugging and completeness)
                if (documents.where((doc) => doc.hasFiles).isNotEmpty) ...[
                 
                  Text(
                    'Tất cả tài liệu đã tải lên',
                    style: Theme.of(buildContext).textTheme.titleSmall?.copyWith(
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: AppDimensions.spacingS),
                  ...documents.where((doc) => doc.hasFiles).map((doc) {
                    return Padding(
                      padding: EdgeInsets.symmetric(vertical: AppDimensions.spacingXS),
                      child: Row(
                        children: [
                          Icon(
                            TablerIcons.file_check,
                            color: AppColors.success,
                            size: 16,
                          ),
                          SizedBox(width: AppDimensions.spacingS),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  doc.name,
                                  style: Theme.of(buildContext).textTheme.bodyMedium?.copyWith(
                                    color: AppColors.success,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                if (doc.filesCount > 0) ...[
                                  SizedBox(height: 2),
                                  Text(
                                    '${doc.filesCount} file${doc.filesCount > 1 ? 's' : ''} • ${doc.totalSizeFormatted}',
                                    style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                                if (doc.description?.isNotEmpty == true) ...[
                                  SizedBox(height: 2),
                                  Text(
                                    doc.description!,
                                    style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                      color: AppColors.textSecondary,
                                      fontStyle: FontStyle.italic,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: AppDimensions.paddingS,
                              vertical: AppDimensions.paddingXS,
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.success.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                            ),
                            child: Text(
                              doc.status.displayName,
                              style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                                color: AppColors.success,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ],
                
                // TODO: Sumary số tài liệu
                // Summary
                // if (totalDocumentsCount > 0) ...[
                //   SizedBox(height: AppDimensions.spacingM),
                //   Container(
                //     padding: EdgeInsets.all(AppDimensions.paddingM),
                //     decoration: BoxDecoration(
                //       color: uploadedRequiredCount == totalRequiredCount 
                //           ? AppColors.success.withValues(alpha: 0.1)
                //           : AppColors.warning.withValues(alpha: 0.1),
                //       borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                //       border: Border.all(
                //         color: uploadedRequiredCount == totalRequiredCount 
                //             ? AppColors.success.withValues(alpha: 0.3)
                //             : AppColors.warning.withValues(alpha: 0.3),
                //       ),
                //     ),
                //     child: Row(
                //       children: [
                //         Icon(
                //           uploadedRequiredCount == totalRequiredCount 
                //               ? TablerIcons.circle_check
                //               : TablerIcons.alert_circle,
                //           color: uploadedRequiredCount == totalRequiredCount 
                //               ? AppColors.success
                //               : AppColors.warning,
                //           size: AppDimensions.iconS,
                //         ),
                //         SizedBox(width: AppDimensions.spacingS),
                //         Expanded(
                //           child: Text(
                //             uploadedRequiredCount == totalRequiredCount
                //                 ? 'Tài liệu bắt buộc đã đầy đủ. Có thể tạo giao dịch.'
                //                 : 'Còn thiếu ${totalRequiredCount - uploadedRequiredCount} tài liệu bắt buộc.',
                //             style: Theme.of(buildContext).textTheme.bodySmall?.copyWith(
                //               color: uploadedRequiredCount == totalRequiredCount 
                //                   ? AppColors.success
                //                   : AppColors.warning,
                //               fontWeight: FontWeight.w500,
                //             ),
                //           ),
                //         ),
                //       ],
                //     ),
                //   ),
                // ],
              ],
            ),
          ),
        ],
      ),
    );
  }


  List<_InfoItem> _buildProductDetails(Map<String, dynamic> details) {
    final productCode = widget.selectedProduct?.code;
    final productName = widget.selectedProduct?.name;
    
    // Handle daily installment loan details (GOLD_LOAN, MANGO)
    if (productCode == 'GOLD_LOAN' || productCode == 'MANGO' || productName?.contains('Vay trả góp ngày') == true) {
      return _buildDailyInstallmentLoanDetails(details);
    }
    
    // Handle other product types
    final items = <_InfoItem>[];
    
    if (details['amount']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Số tiền', '${_formatCurrency(details['amount'].toString())} VND'));
    }
    
    if (details['term']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Thời hạn', '${details['term']} tháng'));
    }
    
    if (details['purpose']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Mục đích', details['purpose'].toString()));
    }
    
    if (details['interestRate']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Lãi suất', '${details['interestRate']}%/năm'));
    }
    
    if (details['cardType']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Loại thẻ', details['cardType'].toString()));
    }
    
    if (details['limit']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Hạn mức', '${_formatCurrency(details['limit'].toString())} VND'));
    }
    
    if (details['deliveryAddress']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Địa chỉ giao thẻ', details['deliveryAddress'].toString()));
    }
    
    if (details['autoRenew'] == true) {
      items.add(_InfoItem('Tái tục', 'Tự động'));
    }
    
    return items;
  }

  List<_InfoItem> _buildDailyInstallmentLoanDetails(Map<String, dynamic> details) {
    final items = <_InfoItem>[];
    
    // Debug: Log all keys for debugging
    debugPrint('Daily installment loan details keys: ${details.keys.toList()}');
    
    // ====== 1. THÔNG TIN ĐỀ NGHỊ VAY VỐN ======
    items.add(_InfoItem('━━━ ĐỀ NGHỊ VAY VỐN ━━━', ''));
    
    // Loan type information
    if (details['loan_type']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Hình thức vay', details['loan_type'].toString()));
    }
    
    // Basic loan details
    if (details['loan_amount']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Số tiền vay', '${_formatCurrency(details['loan_amount'].toString())} VND'));
    }
    
    if (details['own_capital']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Vốn tự có', '${_formatCurrency(details['own_capital'].toString())} VND'));
    }
    
    if (details['total_capital_need']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Tổng nhu cầu vốn', '${_formatCurrency(details['total_capital_need'].toString())} VND'));
    }
    
    if (details['loan_term']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Thời hạn vay', '${details['loan_term']} ngày'));
    }
    
    if (details['branch_code']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('CN/PGD', details['branch_code'].toString()));
    }
    
    if (details['loan_method']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Phương thức vay', details['loan_method'].toString()));
    }
    
    if (details['loan_purpose']?.toString().isNotEmpty == true) {
      final purpose = details['loan_purpose'].toString();
      if (purpose == 'Khác' && details['loan_purpose_other']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Mục đích vay', '$purpose: ${details['loan_purpose_other']}'));
      } else {
        items.add(_InfoItem('Mục đích vay', purpose));
      }
    }
    
    if (details['repayment_method']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Phương thức trả nợ', details['repayment_method'].toString()));
    }
    
    if (details['disbursement_method']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Phương thức giải ngân', details['disbursement_method'].toString()));
    }
    
    if (details['disbursement_account']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Tài khoản nhận tiền', details['disbursement_account'].toString()));
    }
    
    // ====== 2. THÔNG TIN NGƯỜI VAY CHÍNH ======
    items.add(_InfoItem('━━━ NGƯỜI VAY CHÍNH ━━━', ''));
    
    // Document information
    if (details['borrower_name']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Họ và tên', details['borrower_name'].toString()));
    }
    
    if (details['borrower_id_type']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Loại giấy tờ', details['borrower_id_type'].toString()));
    }
    
    if (details['borrower_id_number']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Số GTTT', details['borrower_id_number'].toString()));
    }
    
    if (details['borrower_id_issue_date']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Ngày cấp', details['borrower_id_issue_date'].toString()));
    }
    
    if (details['borrower_id_expiry_date']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Ngày hết hạn', details['borrower_id_expiry_date'].toString()));
    }
    
    if (details['borrower_id_issue_place']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Nơi cấp', details['borrower_id_issue_place'].toString()));
    }
    
    if (details['borrower_birth_date']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Ngày sinh', details['borrower_birth_date'].toString()));
    }
    
    if (details['borrower_gender']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Giới tính', details['borrower_gender'].toString()));
    }
    
    if (details['borrower_marital_status']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Tình trạng hôn nhân', details['borrower_marital_status'].toString()));
    }
    
    if (details['borrower_phone']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Số điện thoại', details['borrower_phone'].toString()));
    }
    
    // Address information - Permanent address
    if (details['borrower_permanent_address']?.toString().isNotEmpty == true) {
      final province = details['borrower_permanent_province']?.toString() ?? '';
      final district = details['borrower_permanent_district']?.toString() ?? '';
      final address = details['borrower_permanent_address'].toString();
      
      String fullAddress = address;
      if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
      if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
      
      items.add(_InfoItem('Hộ khẩu thường trú', fullAddress));
    }
    
    // Current address if different from permanent
    if (details['borrower_current_same_permanent'] != true) {
      if (details['borrower_current_address']?.toString().isNotEmpty == true) {
        final province = details['borrower_current_province']?.toString() ?? '';
        final district = details['borrower_current_district']?.toString() ?? '';
        final address = details['borrower_current_address'].toString();
        
        String fullAddress = address;
        if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
        if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
        
        items.add(_InfoItem('Địa chỉ hiện tại', fullAddress));
      }
    }
    
    // ====== 3. THÔNG TIN NGƯỜI ĐỒNG VAY ======
    if (details['has_co_borrower'] == true) {
      items.add(_InfoItem('━━━ NGƯỜI ĐỒNG VAY ━━━', ''));
      
      if (details['co_borrower_name']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Họ và tên', details['co_borrower_name'].toString()));
      }
      
      if (details['co_borrower_id_type']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Loại giấy tờ', details['co_borrower_id_type'].toString()));
      }
      
      if (details['co_borrower_id_number']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Số GTTT', details['co_borrower_id_number'].toString()));
      }
      
      if (details['co_borrower_id_issue_date']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Ngày cấp', details['co_borrower_id_issue_date'].toString()));
      }
      
      if (details['co_borrower_id_expiry_date']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Ngày hết hạn', details['co_borrower_id_expiry_date'].toString()));
      }
      
      if (details['co_borrower_id_issue_place']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Nơi cấp', details['co_borrower_id_issue_place'].toString()));
      }
      
      if (details['co_borrower_birth_date']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Ngày sinh', details['co_borrower_birth_date'].toString()));
      }
      
      if (details['co_borrower_gender']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Giới tính', details['co_borrower_gender'].toString()));
      }
      
      if (details['co_borrower_marital_status']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Tình trạng hôn nhân', details['co_borrower_marital_status'].toString()));
      }
      
      if (details['co_borrower_phone']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Số điện thoại', details['co_borrower_phone'].toString()));
      }
      
      // Co-borrower permanent address
      if (details['co_borrower_permanent_address']?.toString().isNotEmpty == true) {
        final province = details['co_borrower_permanent_province']?.toString() ?? '';
        final district = details['co_borrower_permanent_district']?.toString() ?? '';
        final address = details['co_borrower_permanent_address'].toString();
        
        String fullAddress = address;
        if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
        if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
        
        items.add(_InfoItem('Hộ khẩu thường trú', fullAddress));
      }
      
      // Co-borrower current address if different
      if (details['co_borrower_current_same_permanent'] != true) {
        if (details['co_borrower_current_address']?.toString().isNotEmpty == true) {
          final province = details['co_borrower_current_province']?.toString() ?? '';
          final district = details['co_borrower_current_district']?.toString() ?? '';
          final address = details['co_borrower_current_address'].toString();
          
          String fullAddress = address;
          if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
          if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
          
          items.add(_InfoItem('Địa chỉ hiện tại', fullAddress));
        }
      }
    }
    
    // ====== 4. THÔNG TIN TÀI CHÍNH ======
    items.add(_InfoItem('━━━ TÌNH HÌNH TÀI CHÍNH ━━━', ''));
    
    if (details['income_source']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Nguồn thu', details['income_source'].toString()));
    }
    
    if (details['daily_income']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Thu nhập bình quân/ngày', '${_formatCurrency(details['daily_income'].toString())} VND'));
    }
    
    // Business information (if income source is business)
    if (details['daily_revenue']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Doanh số bình quân/ngày', '${_formatCurrency(details['daily_revenue'].toString())} VND'));
    }
    
    // Business location if applicable
    if (details['business_location_address']?.toString().isNotEmpty == true) {
      final province = details['business_location_province']?.toString() ?? '';
      final district = details['business_location_district']?.toString() ?? '';
      final address = details['business_location_address'].toString();
      
      String fullAddress = address;
      if (district.isNotEmpty) fullAddress = '$fullAddress, $district';
      if (province.isNotEmpty) fullAddress = '$fullAddress, $province';
      
      items.add(_InfoItem('Địa điểm SXKD', fullAddress));
    }
    
    // Employment information (if income source is salary)
    if (details['employer_name']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Tên công ty', details['employer_name'].toString()));
    }
    
    if (details['position']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Chức vụ', details['position'].toString()));
    }
    
    if (details['work_experience']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Kinh nghiệm làm việc', '${details['work_experience']} năm'));
    }
    
    if (details['monthly_salary']?.toString().isNotEmpty == true) {
      items.add(_InfoItem('Lương hàng tháng', '${_formatCurrency(details['monthly_salary'].toString())} VND'));
    }
    
    // ====== 5. THÔNG TIN TÀI SẢN BẢO ĐẢM ======
    if (details['loan_type'] == 'Có TSĐB') {
      items.add(_InfoItem('━━━ TÀI SẢN BẢO ĐẢM ━━━', ''));
      
      if (details['collateral_type']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Loại tài sản', details['collateral_type'].toString()));
      }
      
      if (details['collateral_value']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Giá trị tài sản', '${_formatCurrency(details['collateral_value'].toString())} VND'));
      }
      
      if (details['collateral_value_text']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Giá trị (bằng chữ)', details['collateral_value_text'].toString()));
      }
      
      if (details['collateral_condition']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Hiện trạng tài sản', details['collateral_condition'].toString()));
      }
      
      if (details['collateral_owner']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Chủ sở hữu', details['collateral_owner'].toString()));
      }
      
      if (details['collateral_owner_birth_year']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Năm sinh chủ sở hữu', details['collateral_owner_birth_year'].toString()));
      }
      
      // ====== 6. CHI TIẾT TÀI SẢN (XE CỘ) ======
      bool hasVehicleDetails = details['vehicle_plate_number']?.toString().isNotEmpty == true ||
                              details['vehicle_name']?.toString().isNotEmpty == true ||
                              details['vehicle_frame_number']?.toString().isNotEmpty == true;
      
      if (hasVehicleDetails) {
        items.add(_InfoItem('━━━ CHI TIẾT TÀI SẢN ━━━', ''));
        
        if (details['vehicle_plate_number']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Biển kiểm soát', details['vehicle_plate_number'].toString()));
        }
        
        if (details['vehicle_name']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Tên tài sản', details['vehicle_name'].toString()));
        }
        
        if (details['vehicle_frame_number']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Số khung', details['vehicle_frame_number'].toString()));
        }
        
        if (details['vehicle_engine_number']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Số máy', details['vehicle_engine_number'].toString()));
        }
        
        if (details['vehicle_registration_number']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Số đăng ký xe', details['vehicle_registration_number'].toString()));
        }
        
        if (details['vehicle_registration_place']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Nơi cấp đăng ký', details['vehicle_registration_place'].toString()));
        }
        
        if (details['vehicle_registration_date']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Ngày cấp đăng ký', details['vehicle_registration_date'].toString()));
        }
        
        if (details['vehicle_condition_at_handover']?.toString().isNotEmpty == true) {
          items.add(_InfoItem('Tình trạng khi giao', details['vehicle_condition_at_handover'].toString()));
        }
      }
      
      if (details['total_collateral_value']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Tổng trị giá TSĐB', '${_formatCurrency(details['total_collateral_value'].toString())} VND'));
      }
    }
    
    // ====== 7. DỰ KIẾN TÍNH TOÁN ======
    bool hasCalculation = details['daily_payment']?.toString().isNotEmpty == true ||
                         details['total_interest']?.toString().isNotEmpty == true ||
                         details['total_payment']?.toString().isNotEmpty == true ||
                         details['debt_to_income_ratio']?.toString().isNotEmpty == true;
    
    if (hasCalculation) {
      items.add(_InfoItem('━━━ DỰ KIẾN TÍNH TOÁN ━━━', ''));
      
      if (details['daily_payment']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Số tiền trả/ngày', '${_formatCurrency(details['daily_payment'].toString())} VND'));
      }
      
      if (details['total_interest']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Tổng tiền lãi', '${_formatCurrency(details['total_interest'].toString())} VND'));
      }
      
      if (details['total_payment']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Tổng phải trả', '${_formatCurrency(details['total_payment'].toString())} VND'));
      }
      
      if (details['debt_to_income_ratio']?.toString().isNotEmpty == true) {
        items.add(_InfoItem('Tỷ lệ trả nợ/thu nhập', '${details['debt_to_income_ratio']}%'));
      }
    }
    
    // ====== 8. THÔNG TIN BỔ SUNG ======
    final processedKeys = {
      // Main loan proposal fields
      'loan_type', 'loan_amount', 'own_capital', 'total_capital_need', 'loan_term',
      'branch_code', 'loan_method', 'loan_purpose', 'loan_purpose_other', 'repayment_method',
      'disbursement_method', 'disbursement_account',
      
      // Main borrower fields
      'borrower_id_type', 'borrower_id_number', 'borrower_id_issue_date', 'borrower_id_expiry_date', 
      'borrower_id_issue_place', 'borrower_name', 'borrower_birth_date', 'borrower_gender', 
      'borrower_marital_status', 'borrower_phone', 'borrower_permanent_address', 
      'borrower_permanent_province', 'borrower_permanent_district', 'borrower_current_address', 
      'borrower_current_province', 'borrower_current_district', 'borrower_current_same_permanent',
      
      // Financial fields
      'income_source', 'daily_revenue', 'daily_income', 'business_location_address', 
      'business_location_province', 'business_location_district', 'employer_name', 'position', 
      'work_experience', 'monthly_salary',
      
      // Collateral fields
      'collateral_type', 'collateral_value', 'collateral_value_text', 'collateral_condition', 
      'collateral_owner', 'collateral_owner_birth_year', 'vehicle_plate_number', 'vehicle_name', 
      'vehicle_frame_number', 'vehicle_engine_number', 'vehicle_registration_number', 
      'vehicle_registration_place', 'vehicle_registration_date', 'vehicle_condition_at_handover', 
      'total_collateral_value',
      
      // Co-borrower fields
      'has_co_borrower', 'co_borrower_name', 'co_borrower_id_type', 'co_borrower_id_number', 
      'co_borrower_id_issue_date', 'co_borrower_id_expiry_date', 'co_borrower_id_issue_place', 
      'co_borrower_birth_date', 'co_borrower_gender', 'co_borrower_marital_status', 'co_borrower_phone',
      'co_borrower_permanent_address', 'co_borrower_permanent_province', 'co_borrower_permanent_district',
      'co_borrower_current_address', 'co_borrower_current_province', 'co_borrower_current_district',
      'co_borrower_current_same_permanent',
      
      // Calculation fields
      'daily_payment', 'total_interest', 'total_payment', 'debt_to_income_ratio',
      
      // Technical/system fields to exclude
      'borrower_card_expanded', 'co_borrower_card_expanded', 'loan_proposal_card_expanded',
      'financial_card_expanded', 'collateral_card_expanded', 'detailed_collateral_card_expanded',
      'product_code', 'product_name', 'created_at', 'updated_at', 'is_validated', 'validation_errors'
    };
    
    // Add any remaining fields that weren't processed above
    final additionalItems = <_InfoItem>[];
    for (final entry in details.entries) {
      if (!processedKeys.contains(entry.key) && 
          entry.value?.toString().isNotEmpty == true && 
          !entry.key.contains('_expanded') &&
          !entry.key.startsWith('product_') &&
          !entry.key.contains('_at') &&
          entry.key != 'is_validated' &&
          entry.key != 'validation_errors') {
        
        // Format field name for display
        String displayName = entry.key;
        displayName = displayName.replaceAll('_', ' ');
        displayName = displayName.split(' ').map((word) => 
            word.isNotEmpty ? word[0].toUpperCase() + word.substring(1).toLowerCase() : word
        ).join(' ');
        
        String displayValue = entry.value.toString();
        
        // Format currency fields
        if (entry.key.contains('amount') || entry.key.contains('value') || 
            entry.key.contains('revenue') || entry.key.contains('income') ||
            entry.key.contains('salary') || entry.key.contains('capital')) {
          if (displayValue.isNotEmpty && RegExp(r'^\d+$').hasMatch(displayValue.replaceAll(RegExp(r'[^\d]'), ''))) {
            displayValue = '${_formatCurrency(displayValue)} VND';
          }
        }
        
        additionalItems.add(_InfoItem(displayName, displayValue));
      }
    }
    
    // Add additional items section if any exist
    if (additionalItems.isNotEmpty) {
      items.add(_InfoItem('━━━ THÔNG TIN KHÁC ━━━', ''));
      items.addAll(additionalItems);
    }
    
    // Fallback: If still no items found, show basic debug info
    if (items.isEmpty) {
      items.add(_InfoItem('Trạng thái dữ liệu', 'Chưa có thông tin chi tiết'));
      items.add(_InfoItem('Số trường dữ liệu', '${details.length}'));
      if (details.isNotEmpty) {
        items.add(_InfoItem('Các trường có sẵn', details.keys.take(5).join(', ')));
      }
    }
    
    return items;
  }

  String? _getMainAmountFromDetails(Map<String, dynamic>? details) {
    if (details == null) return null;
    
    final productName = widget.selectedProduct?.name;
    
    // For daily installment loan, prioritize loan_amount
    if (productName?.contains('Vay trả góp ngày') == true) {
      if (details['loan_amount']?.toString().isNotEmpty == true) {
        return details['loan_amount'].toString();
      }
    }
    
    // For other products, use amount
    if (details['amount']?.toString().isNotEmpty == true) {
      return details['amount'].toString();
    }
    
    // Fallback to loan_amount if amount is not available
    if (details['loan_amount']?.toString().isNotEmpty == true) {
      return details['loan_amount'].toString();
    }
    
    return null;
  }


  /// Get required documents based on product type
  List<String> _getRequiredDocumentsForProduct() {
    final productCode = widget.selectedProduct?.code;

    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        final requiredDocs = <String>[
          'CMND/CCCD người vay chính',
          'Giấy chứng nhận đăng ký xe',
        ];

        // Add co-borrower documents if applicable
        final hasCoBorrower = widget.transactionDetails['has_co_borrower'] == true;
        if (hasCoBorrower) {
          requiredDocs.add('CMND/CCCD người đồng vay');
        }

        // Add marital documents based on marital status
        final maritalStatus = widget.transactionDetails['borrower_marital_status']?.toString() ?? '';
        if (maritalStatus.contains('Có gia đình') || maritalStatus.contains('Kết hôn')) {
          requiredDocs.add('Giấy kết hôn/Giấy xác nhận tình trạng hôn nhân');
        }

        return requiredDocs;

      case 'PERSONAL_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Sổ hộ khẩu',
        ];

      case 'MORTGAGE_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Sổ đỏ/Giấy chứng nhận quyền sử dụng đất',
          'Giấy thẩm định giá',
        ];

      case 'AUTO_LOAN':
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
          'Đăng ký xe',
          'Bảo hiểm xe',
        ];

      case 'BUSINESS_LOAN':
        return [
          'CMND/CCCD',
          'Giấy phép kinh doanh',
          'Báo cáo tài chính',
          'Chứng từ thu nhập',
        ];

      default:
        return [
          'CMND/CCCD',
          'Chứng minh thu nhập',
        ];
    }
  }

  /// Get optional documents based on product type
  List<String> _getOptionalDocumentsForProduct() {
    final productCode = widget.selectedProduct?.code;

    switch (productCode) {
      case 'GOLD_LOAN':
      case 'MANGO':
        final optionalDocs = <String>[
          'Chứng minh thu nhập',
          'Sổ hộ khẩu',
        ];

        // Add business certificate if income source is business
        final incomeSource = widget.transactionDetails['income_source']?.toString() ?? '';
        if (incomeSource.toLowerCase().contains('kinh doanh') ||
            incomeSource.toLowerCase().contains('business')) {
          optionalDocs.add('Giấy phép kinh doanh');
        }

        return optionalDocs;

      case 'PERSONAL_LOAN':
        return [
          'Giấy kết hôn',
          'Chứng nhận lương',
        ];

      case 'MORTGAGE_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng mua bán',
          'Báo cáo thẩm định',
        ];

      case 'AUTO_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng mua xe',
          'Hóa đơn mua xe',
        ];

      case 'BUSINESS_LOAN':
        return [
          'Giấy kết hôn',
          'Hợp đồng thuê mặt bằng',
          'Chứng nhận đăng ký thuế',
        ];

      default:
        return [
          'Giấy kết hôn',
          'Chứng nhận khác',
        ];
    }
  }

}

class _InfoItem {
  final String label;
  final String value;

  _InfoItem(this.label, this.value);
} 