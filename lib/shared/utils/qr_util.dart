import 'dart:convert';
import '../models/qr_models.dart';
import 'vehicle_qr_basic_util.dart';

/// Utility class for parsing QR codes and mapping data to forms
/// 
/// This utility handles:
/// - CCCD QR code parsing
/// - Data mapping to borrower forms
/// - Data mapping to co-borrower forms  
/// - Data mapping to customer forms
class QrUtil {
  // Private constructor to prevent instantiation
  QrUtil._();

  /// Parse CCCD QR data from raw QR string
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data
  static Map<String, String> parseCccdQrData(String qrData) {
    if (qrData.isEmpty) {
      throw ArgumentError('QR data cannot be empty');
    }

    // Initialize result map with empty values
    final Map<String, String> result = {
      'id_number': '',
      'full_name': '',
      'date_of_birth': '',
      'gender': '',
      'nationality': '',
      'place_of_origin': '',
      'place_of_residence': '',
      'personal_id': '',
      'issue_date': '',
      'issue_place': '',
      'expiry_date': '',
    };

    // Try to parse as JSON format first
    try {
      final Map<String, dynamic> data = jsonDecode(qrData);
      _parseCccdJsonData(data, result);
      return result;
    } catch (e) {
      // If JSON parsing fails, try text format
      _parseCccdTextData(qrData, result);
      return result;
    }
  }

  /// Parse CCCD data from JSON format
  static void _parseCccdJsonData(Map<String, dynamic> data, Map<String, String> result) {
    // Handle nested object format (thong_tin_ca_nhan)
    if (data.containsKey('thong_tin_ca_nhan') && data['thong_tin_ca_nhan'] is Map) {
      final personalInfo = data['thong_tin_ca_nhan'] as Map<String, dynamic>;
      _parseCccdFields(personalInfo, result);
      return;
    }

    // Handle flat object format
    _parseCccdFields(data, result);
  }

  /// Parse CCCD data from text format
  static void _parseCccdTextData(String qrData, Map<String, String> result) {
    // Try different text formats
    final lines = qrData.split('\n');
    
    for (final line in lines) {
      if (line.contains(':')) {
        final parts = line.split(':');
        if (parts.length >= 2) {
          final key = parts[0].trim().toLowerCase();
          final value = parts[1].trim();
          
          _mapCccdTextField(key, value, result);
        }
      }
    }
  }

  /// Parse CCCD fields from JSON data
  static void _parseCccdFields(Map<String, dynamic> data, Map<String, String> result) {
    // Parse ID number (Số CCCD)
    result['id_number'] = _getFieldValue(data, [
      'id_number', 'so_cccd', 'so_cmnd', 'id'
    ]);

    // Parse full name (Họ và tên)
    result['full_name'] = _getFieldValue(data, [
      'full_name', 'ho_ten', 'name', 'ten'
    ]);

    // Parse date of birth (Ngày sinh)
    result['date_of_birth'] = _getFieldValue(data, [
      'date_of_birth', 'ngay_sinh', 'birth_date', 'ngay_sinh_cccd'
    ]);

    // Parse gender (Giới tính)
    result['gender'] = _getFieldValue(data, [
      'gender', 'gioi_tinh', 'sex'
    ]);

    // Parse nationality (Quốc tịch)
    result['nationality'] = _getFieldValue(data, [
      'nationality', 'quoc_tich', 'quoc_gia'
    ]);

    // Parse place of origin (Quê quán)
    result['place_of_origin'] = _getFieldValue(data, [
      'place_of_origin', 'que_quan', 'noi_sinh'
    ]);

    // Parse place of residence (Nơi thường trú)
    result['place_of_residence'] = _getFieldValue(data, [
      'place_of_residence', 'noi_thuong_tru', 'dia_chi_thuong_tru', 'address'
    ]);

    // Parse personal ID (Số CMND/CCCD cũ)
    result['personal_id'] = _getFieldValue(data, [
      'personal_id', 'so_cmnd_cu', 'so_cccd_cu'
    ]);

    // Parse issue date (Ngày cấp)
    result['issue_date'] = _getFieldValue(data, [
      'issue_date', 'ngay_cap', 'ngay_cap_cccd'
    ]);

    // Parse issue place (Nơi cấp)
    result['issue_place'] = _getFieldValue(data, [
      'issue_place', 'noi_cap', 'co_quan_cap'
    ]);

    // Parse expiry date (Ngày hết hạn)
    result['expiry_date'] = _getFieldValue(data, [
      'expiry_date', 'ngay_het_han', 'han_su_dung'
    ]);
  }

  /// Map CCCD text field to result
  static void _mapCccdTextField(String key, String value, Map<String, String> result) {
    if (key.contains('so') && key.contains('cccd')) {
      result['id_number'] = value;
    } else if (key.contains('ho') && key.contains('ten')) {
      result['full_name'] = value;
    } else if (key.contains('ngay') && key.contains('sinh')) {
      result['date_of_birth'] = value;
    } else if (key.contains('gioi') && key.contains('tinh')) {
      result['gender'] = value;
    } else if (key.contains('quoc') && key.contains('tich')) {
      result['nationality'] = value;
    } else if (key.contains('que') && key.contains('quan')) {
      result['place_of_origin'] = value;
    } else if (key.contains('thuong') && key.contains('tru')) {
      result['place_of_residence'] = value;
    } else if (key.contains('ngay') && key.contains('cap')) {
      result['issue_date'] = value;
    } else if (key.contains('noi') && key.contains('cap')) {
      result['issue_place'] = value;
    } else if (key.contains('het') && key.contains('han')) {
      result['expiry_date'] = value;
    }
  }

  /// Get field value from data using multiple possible field names
  static String _getFieldValue(Map<String, dynamic> data, List<String> fieldNames) {
    for (final fieldName in fieldNames) {
      if (data.containsKey(fieldName) && data[fieldName] != null) {
        final value = data[fieldName].toString().trim();
        if (value.isNotEmpty) {
          return value;
        }
      }
    }
    return '';
  }

  /// Map CCCD QR data to borrower form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToBorrowerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final borrowerData = BorrowerFormData.fromCccdQr(cccdData);
    final formMap = borrowerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Map CCCD QR data to co-borrower form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToCoBorrowerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final coBorrowerData = CoBorrowerFormData.fromCccdQr(cccdData);
    final formMap = coBorrowerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Map CCCD QR data to customer form
  /// 
  /// [qrData] - Parsed QR data map
  /// [formData] - Form data map to update
  static void mapCccdToCustomerForm(Map<String, String> qrData, Map<String, dynamic> formData) {
    final cccdData = CccdQrData.fromMap(qrData);
    final customerData = CustomerFormData.fromCccdQr(cccdData);
    final formMap = customerData.toFormMap();
    
    // Update form data with mapped values
    formData.addAll(formMap);
  }

  /// Validate CCCD QR data
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns true if QR data is valid and contains essential fields
  static bool isValidCccdQrData(String qrData) {
    if (qrData.isEmpty) return false;

    try {
      final parsedData = parseCccdQrData(qrData);
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid;
    } catch (e) {
      return false;
    }
  }

  /// Get CCCD QR data as model
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns CccdQrData model or null if invalid
  static CccdQrData? getCccdQrDataModel(String qrData) {
    try {
      final parsedData = parseCccdQrData(qrData);
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid ? cccdData : null;
    } catch (e) {
      return null;
    }
  }

  /// Get Vehicle QR data as model
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns VehicleQrData model or null if invalid
  static VehicleQrData? getVehicleQrDataModel(String qrData) {
    try {
      // Use existing VehicleQRBasicUtil
      final parsedData = VehicleQRBasicUtil.parseBasicVehicleQRData(qrData);
      final vehicleData = VehicleQrData.fromMap(parsedData);
      return vehicleData.isValid ? vehicleData : null;
    } catch (e) {
      return null;
    }
  }
}

