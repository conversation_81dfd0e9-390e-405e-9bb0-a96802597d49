import '../models/qr_models.dart';

/// Utility class for parsing QR codes and mapping data to forms
/// 
/// This utility handles:
/// - CCCD QR code parsing from pipe-separated format
/// - Data mapping to borrower forms
/// - Data mapping to co-borrower forms  
/// - Data mapping to customer forms
/// - Gender mapping with API config support
class QrUtils {
  // Private constructor to prevent instantiation
  QrUtils._();

  /// Parse CCCD QR data from pipe-separated format
  /// 
  /// Format: idNumber|personalId|fullName|dateOfBirth|gender|placeOfOrigin|issueDate
  /// Example: 027098009929|125856144|Tr<PERSON>n <PERSON>|12021998|Nam|Th<PERSON><PERSON>, Song Giang, Gia Bình, Bắc Ninh|25082021
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data
  static Map<String, String?> parseCccdQrData(String qrData) {
    if (qrData.isEmpty) {
      throw ArgumentError('QR data cannot be empty');
    }

    // Split by pipe separator
    final parts = qrData.split('|');
    
    if (parts.length < 7) {
      throw ArgumentError('Invalid QR data format. Expected at least 7 fields separated by |');
    }

    // Map the parts to corresponding fields
    final Map<String, String?> result = {
      'id_number': parts[0].trim().isNotEmpty ? parts[0].trim() : null,
      'personal_id': parts[1].trim().isNotEmpty ? parts[1].trim() : null,
      'full_name': parts[2].trim().isNotEmpty ? parts[2].trim() : null,
      'date_of_birth': parts[3].trim().isNotEmpty ? parts[3].trim() : null,
      'gender': parts[4].trim().isNotEmpty ? parts[4].trim() : null,
      'place_of_origin': parts[5].trim().isNotEmpty ? parts[5].trim() : null,
      'issue_date': parts[6].trim().isNotEmpty ? parts[6].trim() : null,
      'nationality': null,
      'place_of_residence': null,
      'issue_place': null,
      'expiry_date': null,
    };

    return result;
  }

  /// Map gender text to API config code
  /// 
  /// [genderText] - Gender text from QR (e.g., "Nam", "Nữ")
  /// [sexConfigs] - List of sex configs from API
  /// 
  /// Returns the corresponding config code or null if not found
  static String? mapGenderToConfigCode(String? genderText, List<Map<String, dynamic>>? sexConfigs) {
    if (genderText == null || genderText.isEmpty || sexConfigs == null) {
      return null;
    }

    // Normalize gender text for comparison
    final normalizedGender = genderText.trim().toLowerCase();
    
    for (final config in sexConfigs) {
      final label = config['label']?.toString().toLowerCase() ?? '';
      final description = config['description']?.toString().toLowerCase() ?? '';
      final code = config['code']?.toString();
      
      if (code != null && 
          (label == normalizedGender || description == normalizedGender)) {
        return code;
      }
    }
    
    return null;
  }

  /// Validate CCCD QR data
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns true if QR data is valid and contains essential fields
  static bool isValidCccdQrData(String qrData) {
    if (qrData.isEmpty) return false;

    try {
      final parsedData = parseCccdQrData(qrData);
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid;
    } catch (e) {
      return false;
    }
  }

  /// Get CCCD QR data as model
  /// 
  /// [qrData] - Raw QR data string
  /// 
  /// Returns CccdQrData model or null if invalid
  static CccdQrData? getCccdQrDataModel(String qrData) {
    try {
      final parsedData = parseCccdQrData(qrData);
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid ? cccdData : null;
    } catch (e) {
      return null;
    }
  }

  /// Get CCCD QR data as model with gender mapping
  /// 
  /// [qrData] - Raw QR data string
  /// [sexConfigs] - Optional sex configs for gender mapping
  /// 
  /// Returns CccdQrData model with mapped gender or null if invalid
  static CccdQrData? getCccdQrDataModelWithGenderMapping(
    String qrData, {
    List<Map<String, dynamic>>? sexConfigs,
  }) {
    try {
      final parsedData = parseCccdQrData(qrData);
      
      // Map gender if sex configs provided
      if (sexConfigs != null && parsedData['gender'] != null) {
        final genderCode = mapGenderToConfigCode(parsedData['gender'], sexConfigs);
        if (genderCode != null) {
          parsedData['gender'] = genderCode;
        }
      }
      
      final cccdData = CccdQrData.fromMap(parsedData);
      return cccdData.isValid ? cccdData : null;
    } catch (e) {
      return null;
    }
  }

  /// Parse date from DDMMYYYY format to DD/MM/YYYY
  /// 
  /// [dateString] - Date string in DDMMYYYY format
  /// 
  /// Returns formatted date string or original if invalid
  static String? formatDateFromDDMMYYYY(String? dateString) {
    if (dateString == null || dateString.isEmpty || dateString.length != 8) {
      return dateString;
    }

    try {
      final day = dateString.substring(0, 2);
      final month = dateString.substring(2, 4);
      final year = dateString.substring(4, 8);
      
      return '$day/$month/$year';
    } catch (e) {
      return dateString;
    }
  }

  /// Parse CCCD QR data with formatted dates
  /// 
  /// [qrData] - The raw QR code data string
  /// 
  /// Returns a map with parsed CCCD data and formatted dates
  static Map<String, String?> parseCccdQrDataWithFormattedDates(String qrData) {
    final parsedData = parseCccdQrData(qrData);
    
    // Format dates
    parsedData['date_of_birth'] = formatDateFromDDMMYYYY(parsedData['date_of_birth']);
    parsedData['issue_date'] = formatDateFromDDMMYYYY(parsedData['issue_date']);
    
    return parsedData;
  }
}
