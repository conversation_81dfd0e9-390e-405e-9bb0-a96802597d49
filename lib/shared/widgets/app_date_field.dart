import 'package:flutter/material.dart';
import 'package:flutter_tabler_icons/flutter_tabler_icons.dart';
import '../../core/theme/index.dart';

/// Common Date Field Widget tối ưu cho toàn bộ app

class AppDateField extends StatefulWidget {
  final String label;
  final String? initialValue;
  final ValueChanged<String?>? onChanged;
  final String? Function(String?)? validator;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final DateTime? initialDate;
  final bool enabled;
  final String? hintText;
  final IconData? prefixIcon;
  final bool required;
  final bool shouldValidate;

  const AppDateField({
    super.key,
    required this.label,
    this.initialValue,
    this.onChanged,
    this.validator,
    this.firstDate,
    this.lastDate,
    this.initialDate,
    this.enabled = true,
    this.hintText,
    this.prefixIcon,
    this.required = true,
    this.shouldValidate = false,
  });

  @override
  State<AppDateField> createState() => _AppDateFieldState();
}

class _AppDateFieldState extends State<AppDateField> {
  late TextEditingController _controller;
  String? _currentValue;

  @override
  void initState() {
    super.initState();
    _currentValue = widget.initialValue;
    _controller = TextEditingController(text: _currentValue ?? '');
  }

  @override
  void didUpdateWidget(AppDateField oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialValue != oldWidget.initialValue) {
      _currentValue = widget.initialValue;
      // Sử dụng WidgetsBinding để tránh setState during build
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _controller.text = _currentValue ?? '';
        }
      });
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Lấy date range với default values hợp lý
  Map<String, DateTime> _getDateRange() {
    final now = DateTime.now();
    
    // Default values hợp lý cho các trường hợp phổ biến
    return {
      'initial': widget.initialDate ?? now,
      'first': widget.firstDate ?? DateTime(1940), // Cho phép chọn từ 1940
      'last': widget.lastDate ?? DateTime(2100),  // Cho phép chọn đến 2100
    };
  }

  /// Parse date từ string format dd/mm/yyyy
  /// TODO: linhlt1/cập nhật thư viện format time
  DateTime? _parseDate(String dateString) {
    if (dateString.isEmpty) return null;
    
    final parts = dateString.split('/');
    if (parts.length != 3) return null;
    
    try {
      return DateTime(
        int.parse(parts[2]), // year
        int.parse(parts[1]), // month
        int.parse(parts[0]), // day
      );
    } catch (e) {
      debugPrint('Error parsing date: $dateString');
      return null;
    }
  }

  /// Format DateTime thành string dd/mm/yyyy
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/'
           '${date.month.toString().padLeft(2, '0')}/'
           '${date.year}';
  }

  /// Hiển thị date picker và xử lý selection
  Future<void> _selectDate() async {
    if (!widget.enabled) return;
    
    FocusScope.of(context).requestFocus(FocusNode());
    
    final dateRange = _getDateRange();
    final currentDate = _parseDate(_currentValue ?? '');
    
    // Đảm bảo initial date nằm trong range cho phép
    DateTime initialDate = currentDate ?? dateRange['initial']!;
    if (initialDate.isBefore(dateRange['first']!)) {
      initialDate = dateRange['first']!;
    } else if (initialDate.isAfter(dateRange['last']!)) {
      initialDate = dateRange['last']!;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: initialDate,
      firstDate: dateRange['first']!,
      lastDate: dateRange['last']!,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: AppColors.kienlongOrange,
              onPrimary: Colors.white,
              surface: Theme.of(context).cardColor,
              onSurface: Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(
                foregroundColor: AppColors.kienlongOrange,
                textStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

          ),
          child: child!,
        );
      },
    );

    if (picked != null && mounted) {
      final formattedDate = _formatDate(picked);
      setState(() {
        _currentValue = formattedDate;
        _controller.text = formattedDate;
      });
      widget.onChanged?.call(formattedDate);
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    // Chỉ validate khi shouldValidate = true
    final hasError = widget.shouldValidate && widget.validator?.call(_currentValue) != null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        Row(
          children: [
            Text(
              widget.label,
              style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: hasError ? colorScheme.error : null,
              ),
            ),
            if (widget.required) ...[
              SizedBox(width: AppDimensions.spacingXS),
              Text(
                '*',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.error,
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: AppDimensions.spacingS),
        
        // Date field input
       TextFormField(
            controller: _controller,
            readOnly: true,
            enabled: widget.enabled,
            style: textTheme.bodyMedium?.copyWith(
              color: _currentValue?.isNotEmpty == true 
                  ? textTheme.bodyLarge?.color 
                  : AppColors.textSecondary,
            ),
            decoration: InputDecoration(
              hintText: widget.hintText ?? 'dd/mm/yyyy',
              hintStyle: textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
              prefixIcon: widget.prefixIcon != null 
                  ? Icon(
                      widget.prefixIcon,
                      size: AppDimensions.iconS,
                      color: hasError 
                          ? colorScheme.error 
                          : AppColors.textSecondary,
                    )
                  : null,
              suffixIcon: Icon(
                TablerIcons.calendar,
                size: AppDimensions.iconS,
                color: hasError 
                    ? colorScheme.error 
                    : widget.enabled 
                        ? AppColors.textSecondary 
                        : AppColors.neutral400,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(AppDimensions.paddingM),
              errorStyle: const TextStyle(height: 0), // Hide built-in error text
            ),
            onTap: _selectDate,
            validator: widget.validator,
          ),
   
      ],
    );
  }
}
